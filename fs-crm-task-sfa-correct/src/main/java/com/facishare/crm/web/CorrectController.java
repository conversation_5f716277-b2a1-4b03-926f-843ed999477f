package com.facishare.crm.web;

import com.facishare.crm.task.sfa.correct.ConfusionWords;
import com.facishare.crm.task.sfa.correct.ProperCorrector;
import com.facishare.crm.task.sfa.correct.ProperWord;
import com.facishare.crm.task.sfa.correct.ProperWords;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@AllArgsConstructor
@RestController
@RequestMapping("correct")
public class CorrectController {

    private final ProperCorrector properCorrector;

    @PostMapping("proper")
    public Result proper(@RequestBody ProperCorrectParam param) {
        List<ProperWord> properWordWords = param.properWords.stream().map(t -> new ProperWord(t, param.sim)).collect(Collectors.toList());
        String correct = properCorrector.correct(param.text, new ProperWords(properWordWords), new ConfusionWords());
        return new Result(correct);
    }

    @Data
    public static class Result {
        Object data;

        public Result(Object data) {
            this.data = data;
        }
    }


    @Data
    public static class ProperCorrectParam {
        String text;
        List<String> properWords;
        float sim = 0.85f;
    }
}
