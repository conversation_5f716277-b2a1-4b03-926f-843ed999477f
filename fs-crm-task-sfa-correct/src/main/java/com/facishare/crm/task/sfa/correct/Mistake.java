package com.facishare.crm.task.sfa.correct;

public class Mistake {
    int start;
    int end;
    String source;
    String correct;
    Type type;
    int diff;

    public Mistake(int start, int end, String source, String correct, Type type) {
        this.start = start;
        this.end = end;
        this.source = source;
        this.correct = correct;
        this.type = type;
        this.diff = correct.length() - end + start;
    }
}