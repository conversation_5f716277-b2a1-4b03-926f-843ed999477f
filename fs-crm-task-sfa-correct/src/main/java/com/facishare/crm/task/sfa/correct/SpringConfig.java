package com.facishare.crm.task.sfa.correct;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Setter
@Getter
@Configuration("com.facishare.crm.task.sfa.correct.SpringConfig")
@PropertySource("classpath:correct.properties")
public class SpringConfig {
    @Value("${path.dict.stroke}")
    private String strokeDictPath;
    @Value("${path.dict.confusion.en}")
    private String confusionEnDictPath;
}
