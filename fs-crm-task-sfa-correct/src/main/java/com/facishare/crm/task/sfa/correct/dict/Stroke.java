package com.facishare.crm.task.sfa.correct.dict;

import com.facishare.crm.task.sfa.correct.SpringConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.stream.Stream;

@Component
@Slf4j
public class Stroke implements InitializingBean {
    @Qualifier("com.facishare.crm.task.sfa.correct.SpringConfig")
    private final SpringConfig springConfig;
    private final int INDEX_OFFSET = '一';
    private String[] inner;

    public Stroke(SpringConfig springConfig) {
        this.springConfig = springConfig;
    }

    public String query(char s) {
        int i = s - INDEX_OFFSET;
        if (0 <= i && i < inner.length) {
            return inner[i];
        } else {
            return null;
        }
    }

    @Override
    public void afterPropertiesSet() {
        InputStream resource = Stroke.class.getClassLoader().getResourceAsStream(springConfig.getStrokeDictPath());
        if (resource == null) {
            log.warn("resource is null, path={}", springConfig.getStrokeDictPath());
            return;
        }
        inner = new String[21000];
        try (Stream<String> stream = new BufferedReader(new InputStreamReader(resource)).lines()) {
            // java11下trim换strip
            stream.filter(line -> (!StringUtils.equals(line.trim(), "#")) && (line.trim().split("\\s+").length >= 2))
                    .map(line -> line.split("\\s+")).forEach(t -> inner[t[0].charAt(0) - INDEX_OFFSET] = t[1]);
        }
    }
}