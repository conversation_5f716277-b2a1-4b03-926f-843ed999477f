<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:spring/common.xml"></import>
    <import resource="classpath:spring/eservice-rest.xml"/>
    <import resource="classpath:paasauthrest/paasauthrest.xml"></import>


    <bean id="egressApiProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.EgressApiProxy" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>


    <bean id="stoneAuthProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.StoneAuthProxy" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="fsBigFileManager" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.FsBigFileManagerProxy" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="fileParseTextProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.FileParseTextProxy" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="fsFileToContentResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.FsFileToContentResource" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>


    <bean id="orgServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean">
        <property name="factory" ref="restServiceProxyFactory"/>
        <property name="type" value="com.facishare.paas.appframework.common.service.OrgServiceProxy"/>
    </bean>


    <!-- 自定义服务rest接口 -->

    <bean id="socialFeedProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.SocialFeedProxy" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="paasUserRoleProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.PaasUserRoleProxy" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="fsCrmProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.FsCrmProxy" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="knowledgeSpaceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.KnowledgeSpaceProxy" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="biRestServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-bi-rest-proxy" init-method="init"/>
    <bean id="biCrmRestProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.BiCrmRestProxy">
        <property name="factory" ref="biRestServiceProxyFactory"/>
    </bean>


    <bean id="aiRestProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.task.sfa.rest.AiRestProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
</beans>