[{"id": "67e37400d4bcec0001465996", "tenant_id": "93747", "name": "L2C管理流程", "priority": "8", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472167, "last_modified_by": "-10000", "last_modified_time": 1752486472167, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "MethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "a5f281003f626ce2", "change_type": null, "out_data_auth_code": "b667b5d228817892", "order_by": null, "data_auth_id": 7800749, "out_data_auth_id": 631549, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503474294, "system_type": "system", "remark": null, "type": "flow", "status": "published", "stage_level": 3, "​​process_overview": null, "show_level": null, "seq": 10, "methodology_ids": null}, {"id": "680e21b283a9df00014e00b7", "tenant_id": "93747", "name": "BANT", "priority": "10", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472168, "last_modified_by": "-10000", "last_modified_time": 1752486472168, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "MethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "a5f281003f626ce2", "change_type": null, "out_data_auth_code": "b667b5d228817892", "order_by": null, "data_auth_id": 7800749, "out_data_auth_id": 631549, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486500542146, "system_type": "system", "remark": "​​BANT方法论简介​​\nBANT是一种经典的销售线索评估框架，由IBM在20世纪50年代提出，用于帮助销售人员快速识别潜在客户的商业价值。其核心是通过四个维度判断客户是否值得跟进，从而提高销售效率。", "type": "profile", "status": "published", "stage_level": 1, "​​process_overview": null, "show_level": 2, "seq": 30, "methodology_ids": ["67e37400d4bcec0001465996"]}, {"id": "683063399c6dd800060bec1c", "tenant_id": "93747", "name": "C139", "priority": "12", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472169, "last_modified_by": "1000", "last_modified_time": 1752570599805, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "MethodologyObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "a5f281003f626ce2", "change_type": null, "out_data_auth_code": "b667b5d228817892", "order_by": null, "data_auth_id": 7800749, "out_data_auth_id": 631549, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752570599827568, "system_type": "system", "remark": "C139销售方法论是一个高效销售模型，C指客户关键人，1指1个决胜点，3指3个必要条件，9指9个必清事项。它帮助销售人员精准定位客户，明确销售重点，全面把握销售进程，提升销售成功率。该方法论现阶段仅适用商机对象。", "type": "profile", "status": "published", "stage_level": 1, "​​process_overview": null, "show_level": 2, "seq": 40, "methodology_ids": null}, {"id": "685920fbed27860001ebba4b", "tenant_id": "93747", "name": "商机管理流程", "priority": "9", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472170, "last_modified_by": "-10000", "last_modified_time": 1752486472170, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "MethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "a5f281003f626ce2", "change_type": null, "out_data_auth_code": "b667b5d228817892", "order_by": null, "data_auth_id": 7800749, "out_data_auth_id": 631549, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503913710, "system_type": "system", "remark": null, "type": "flow", "status": "published", "stage_level": 3, "​​process_overview": null, "show_level": null, "seq": 20, "methodology_ids": null}]