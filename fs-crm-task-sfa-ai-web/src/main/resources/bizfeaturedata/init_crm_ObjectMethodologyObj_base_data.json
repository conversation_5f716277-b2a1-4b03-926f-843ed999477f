[{"id": "6837d1eb48bde14810f2342d", "tenant_id": "93747", "name": null, "object_api_name": "LeadsObj", "object_field_name": "leads_stage", "object_field_value": "Lead", "node_id": "67ed11e8829bce0001398b61", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472679, "last_modified_by": "-10000", "last_modified_time": 1752486472679, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503535012, "system_type": "system", "methodology_id": "67e37400d4bcec0001465996"}, {"id": "6837d1eb48bde14810f2342e", "tenant_id": "93747", "name": null, "object_api_name": "LeadsObj", "object_field_name": "leads_stage", "object_field_value": "MQL", "node_id": "67ed1206829bce0001398f4f", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472680, "last_modified_by": "-10000", "last_modified_time": 1752486472680, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503967872, "system_type": "system", "methodology_id": "67e37400d4bcec0001465996"}, {"id": "6837d1eb48bde14810f2342f", "tenant_id": "93747", "name": null, "object_api_name": "LeadsObj", "object_field_name": "leads_stage", "object_field_value": "SQL", "node_id": "6836ad89a7c3f00001710a75", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472681, "last_modified_by": "-10000", "last_modified_time": 1752486472681, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503968180, "system_type": "system", "methodology_id": "67e37400d4bcec0001465996"}, {"id": "6837d1eb48bde14810f23430", "tenant_id": "93747", "name": null, "object_api_name": "NewOpportunityObj", "object_field_name": "sales_stage", "object_field_value": "1", "node_id": "67ed1262829bce0001399d1b", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472682, "last_modified_by": "-10000", "last_modified_time": 1752486472682, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503537036, "system_type": "system", "methodology_id": "67e37400d4bcec0001465996"}, {"id": "6837d1eb48bde14810f23431", "tenant_id": "93747", "name": null, "object_api_name": "NewOpportunityObj", "object_field_name": "sales_stage", "object_field_value": "2", "node_id": "67ed124b829bce00013998b3", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472683, "last_modified_by": "-10000", "last_modified_time": 1752486472683, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503968835, "system_type": "system", "methodology_id": "67e37400d4bcec0001465996"}, {"id": "6837d1eb48bde14810f23432", "tenant_id": "93747", "name": null, "object_api_name": "NewOpportunityObj", "object_field_name": "sales_stage", "object_field_value": "3", "node_id": "6836aed1938aa30001c8898e", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472684, "last_modified_by": "-10000", "last_modified_time": 1752486472684, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503969053, "system_type": "system", "methodology_id": "67e37400d4bcec0001465996"}, {"id": "6837d1eb48bde14810f23433", "tenant_id": "93747", "name": null, "object_api_name": "NewOpportunityObj", "object_field_name": "sales_stage", "object_field_value": "4", "node_id": "6836af01938aa30001c88da4", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472685, "last_modified_by": "-10000", "last_modified_time": 1752486472685, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503537541, "system_type": "system", "methodology_id": "67e37400d4bcec0001465996"}, {"id": "683ef453e3ce15000103e8d7", "tenant_id": "93747", "name": null, "object_api_name": "NewOpportunityObj", "object_field_name": "sales_stage", "object_field_value": "4", "node_id": "685922a0ed27860001ebdb7b", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472686, "last_modified_by": "-10000", "last_modified_time": 1752486472686, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503969505, "system_type": "system", "methodology_id": "685920fbed27860001ebba4b"}, {"id": "6847f9c86bea48000176d6f4", "tenant_id": "93747", "name": null, "object_api_name": "NewOpportunityObj", "object_field_name": "sales_stage", "object_field_value": "3", "node_id": "6859227fed27860001ebd5fa", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472687, "last_modified_by": "-10000", "last_modified_time": 1752486472687, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503538010, "system_type": "system", "methodology_id": "685920fbed27860001ebba4b"}, {"id": "684809cbbb9e2600015fbf23", "tenant_id": "93747", "name": null, "object_api_name": "NewOpportunityObj", "object_field_name": "sales_stage", "object_field_value": "2", "node_id": "68592257ed27860001ebd172", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472688, "last_modified_by": "-10000", "last_modified_time": 1752486472688, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503969931, "system_type": "system", "methodology_id": "685920fbed27860001ebba4b"}, {"id": "6848166edbcbe9000151e412", "tenant_id": "93747", "name": null, "object_api_name": "NewOpportunityObj", "object_field_name": "sales_stage", "object_field_value": "1", "node_id": "685921d7ed27860001ebc41e", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486472689, "last_modified_by": "-10000", "last_modified_time": 1752486472689, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "ObjectMethodologyObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "9f82e1b696093eca", "change_type": null, "out_data_auth_code": "faf61b9c7ed64d0e", "order_by": null, "data_auth_id": 7800761, "out_data_auth_id": 631558, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503970357, "system_type": "system", "methodology_id": "685920fbed27860001ebba4b"}]