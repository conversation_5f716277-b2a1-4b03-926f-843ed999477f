{"validate_rules": {}, "triggers": {}, "actions": {}, "index_version": 1, "api_name": "ActivityContactInsightObj", "display_name": "联系人洞察", "package": "CRM", "define_type": "package", "is_active": true, "store_table_name": "biz_activity_contact_insight", "is_deleted": false, "fields": {"name": {"is_index": true, "is_active": true, "prefix": "{yyyy}-{mm}-{dd}_", "auto_adapt_places": false, "description": "主属性", "is_unique": false, "start_number": 1, "serial_number": 6, "label": "主属性", "type": "auto_number", "is_need_convert": false, "is_required": true, "api_name": "name", "define_type": "package", "postfix": "", "is_single": false, "is_index_field": false, "status": "released"}, "contact_id": {"default_is_expression": false, "is_index": true, "is_active": true, "description": "联系人", "is_unique": false, "label": "联系人", "target_api_name": "ContactObj", "type": "object_reference", "target_related_list_name": "contact_id_list", "target_related_list_label": "联系人洞察", "action_on_target_delete": "cascade_delete", "is_need_convert": false, "is_required": false, "api_name": "contact_id", "define_type": "package", "is_single": false, "status": "released"}, "opportunity_contacts_id": {"default_is_expression": false, "is_index": true, "is_active": true, "description": "商机联系人", "is_unique": false, "label": "商机联系人", "target_api_name": "NewOpportunityContactsObj", "type": "object_reference", "target_related_list_name": "opportunity_contacts_id_list", "target_related_list_label": "联系人洞察", "action_on_target_delete": "cascade_delete", "is_need_convert": false, "is_required": false, "api_name": "opportunity_contacts_id", "define_type": "package", "is_single": false, "status": "released"}, "current_attitude": {"is_unique": false, "disable_after_filter": true, "type": "select_one", "is_required": false, "options": [{"label": "支持", "value": "support"}, {"label": "中立", "value": "neutrality"}, {"label": "不满", "value": "oppose"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "当前态度", "api_name": "current_attitude", "status": "released"}, "comprehensive_attitude": {"is_unique": false, "disable_after_filter": true, "type": "select_one", "is_required": false, "options": [{"label": "强支持", "value": "101"}, {"label": "弱支持", "value": "102"}, {"label": "中立", "value": "103"}, {"label": "弱反对", "value": "104"}, {"label": "强反对", "value": "105"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "综合态度", "api_name": "comprehensive_attitude", "status": "released"}, "attendeeinsight_record_id": {"default_is_expression": false, "is_index": true, "is_active": true, "description": "最近五次洞察记录", "is_unique": false, "label": "最近五次洞察记录", "type": "array", "is_need_convert": false, "is_required": false, "api_name": "attendeeinsight_record_id", "define_type": "package", "is_single": false, "status": "released"}, "character_content": {"expression_type": "json", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 31074, "is_index": false, "is_active": true, "is_encrypted": false, "label": "性格", "api_name": "character_content", "help_text": "", "status": "released"}, "bant_budget": {"default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "max_length": 1000, "is_encrypted": false, "label": "预算", "api_name": "bant_budget", "status": "released"}, "budget_active_record_id": {"default_is_expression": false, "is_index": true, "is_active": true, "description": "预算关联销售记录", "is_unique": false, "label": "预算关联销售记录", "target_api_name": "ActiveRecordObj", "type": "object_reference_many", "target_related_list_name": "budget_active_record_id_list", "target_related_list_label": "联系人洞察预算", "action_on_target_delete": "cascade_delete", "is_need_convert": false, "is_required": false, "api_name": "budget_active_record_id", "define_type": "system", "is_single": false, "status": "released"}, "bant_authority": {"default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "max_length": 1000, "is_encrypted": false, "label": "决策人", "api_name": "bant_authority", "status": "released"}, "authority_active_record_id": {"default_is_expression": false, "is_index": true, "is_active": true, "description": "决策人关联销售记录", "is_unique": false, "label": "决策人关联销售记录", "target_api_name": "ActiveRecordObj", "type": "object_reference_many", "target_related_list_name": "authority_active_record_id_list", "target_related_list_label": "联系人洞察决策人", "action_on_target_delete": "cascade_delete", "is_need_convert": false, "is_required": false, "api_name": "authority_active_record_id", "define_type": "system", "is_single": false, "status": "released"}, "bant_need": {"default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "max_length": 1000, "is_encrypted": false, "label": "需求", "api_name": "bant_need", "status": "released"}, "need_active_record_id": {"default_is_expression": false, "is_index": true, "is_active": true, "description": "需求关联销售记录", "is_unique": false, "label": "需求关联销售记录", "target_api_name": "ActiveRecordObj", "type": "object_reference_many", "target_related_list_name": "budget_active_record_id_list", "target_related_list_label": "联系人洞察需求", "action_on_target_delete": "cascade_delete", "is_need_convert": false, "is_required": false, "api_name": "need_active_record_id", "define_type": "system", "is_single": false, "status": "released"}, "bant_time": {"default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "max_length": 1000, "is_encrypted": false, "label": "时间", "api_name": "bant_time", "status": "released"}, "time_active_record_id": {"default_is_expression": false, "is_index": true, "is_active": true, "description": "时间所需销售记录", "is_unique": false, "label": "时间所需销售记录", "target_api_name": "ActiveRecordObj", "type": "object_reference_many", "target_related_list_name": "time_active_record_id_list", "target_related_list_label": "联系人洞察", "action_on_target_delete": "cascade_delete", "is_need_convert": false, "is_required": false, "api_name": "time_active_record_id", "define_type": "system", "is_single": false, "status": "released"}, "meeting_content": {"expression_type": "json", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 31074, "is_index": false, "is_active": true, "is_encrypted": false, "label": "会议总结", "api_name": "meeting_content", "help_text": "", "status": "released"}, "invisible_content": {"expression_type": "json", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 31074, "is_index": false, "is_active": true, "is_encrypted": false, "label": "隐形担忧", "api_name": "invisible_content", "help_text": "", "status": "released"}, "attitude_upd_field": {"is_index": true, "is_active": true, "is_unique": false, "default_value": false, "label": "态度是否更新到字段", "type": "true_or_false", "is_abstract": null, "is_required": false, "api_name": "attitude_upd_field", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "status": "released"}, "attitude_upd_tag": {"is_index": true, "is_active": true, "is_unique": false, "default_value": false, "label": "态度是否更新标签", "type": "true_or_false", "is_abstract": null, "is_required": false, "api_name": "attitude_upd_tag", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "status": "released"}, "character_upd_tag": {"is_index": true, "is_active": true, "is_unique": false, "default_value": false, "label": "性格是否更新到标签", "type": "true_or_false", "is_abstract": null, "is_required": false, "api_name": "character_upd_tag", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "status": "released"}, "comprehensive_attitude_is_ai_upd": {"is_index": true, "is_active": true, "is_unique": false, "default_value": true, "label": "综合态度是否需要ai更新", "type": "true_or_false", "is_abstract": null, "is_required": false, "api_name": "comprehensive_attitude_is_ai_upd", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "status": "released"}, "state": {"is_unique": false, "disable_after_filter": true, "type": "select_one", "is_required": false, "options": [{"label": "未更新", "value": "0"}, {"label": "更新中", "value": "1"}, {"label": "已完成", "value": "2"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "任务进度", "api_name": "state", "status": "released"}, "lock_status": {"type": "select_one", "api_name": "lock_status", "define_type": "package", "is_required": false, "description": "锁定状态", "label": "锁定状态", "default_value": "0", "is_unique": false, "is_index": true, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}]}, "lock_rule": {"is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "status": "released"}, "lock_user": {"is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "released"}, "life_status": {"is_index": true, "is_active": true, "description": "生命状态", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "status": "released"}, "life_status_before_invalid": {"is_index": false, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "max_length": 256, "status": "released"}, "owner": {"type": "employee", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "round_mode": 4, "length": 20, "decimal_places": 0, "api_name": "owner", "label": "负责人", "description": "负责人", "status": "released", "is_single": true}, "owner_department": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 100, "pattern": "", "api_name": "owner_department", "label": "负责人主属部门", "description": "负责人主属部门", "status": "released"}, "data_own_department": {"define_type": "package", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": false, "is_single": true, "is_unique": false, "description": "归属部门", "label": "归属部门", "api_name": "data_own_department", "is_index_field": false, "status": "released", "type": "department"}, "out_owner": {"type": "employee", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "out_owner", "status": "released", "label": "外部负责人"}, "relevant_team": {"is_index": true, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "相关团队", "status": "released", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": false, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}}, "created_by": {"type": "employee", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "创建人", "api_name": "created_by", "description": "创建人", "status": "released", "is_extend": false}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "最后修改人", "api_name": "last_modified_by", "description": "最后修改人", "status": "released", "is_extend": false}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "api_name": "package", "description": "package", "status": "released", "is_extend": false}, "object_describe_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_id", "api_name": "object_describe_id", "description": "object_describe_id", "status": "released", "is_extend": false}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "is_extend": false}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "is_extend": false}, "record_type": {"is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": true, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "status": "released"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "创建时间", "status": "released", "is_extend": false}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}}}