package com.facishare.crm.web;

import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.bizfeature.constant.ObjectMethodologyConstants;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureNode;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureEngineService;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureNodeFinishService;
import com.facishare.crm.task.sfa.bizfeature.service.MethodologyNodeService;
import com.facishare.crm.task.sfa.bizfeature.service.MethodologyService;
import com.facishare.crm.task.sfa.bizfeature.service.dao.mongo.FeatureTaskDocument;
import com.facishare.crm.task.sfa.bizfeature.service.dao.mongo.FeatureTaskInitDao;
import com.facishare.crm.task.sfa.bizfeature.service.dao.mongo.FeatureTaskInitDocument;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.model.ObjectData;
import com.facishare.crm.task.sfa.xxl.FeatureTaskJobHandler;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.TriggerParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 特征测试
 *
 * @IgnoreI18nFile
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("feature")
public class FeatureController {
    @Resource
    private FeatureEngineService FeatureEngineService;

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Resource
    private CompletionsService completionsService;
    @Resource
    private MethodologyService methodologyService;
    @Resource
    private MethodologyNodeService methodologyNodeService;
    @Resource
    private FeatureNodeFinishService nodeService;

    @Resource
    private FeatureTaskInitDao featureTaskInitDao;

    @Resource
    private FeatureTaskJobHandler jobHandler;

    @PostMapping("generate")
    public Object generate(@RequestBody ObjectData.ObjectChange message) {
        FeatureEngineService.generate(message);
        return "1";
    }

    @PostMapping("generateTag")
    public Object generateTag(@RequestBody FeatureMqModel.Message message) {
        FeatureEngineService.generateTag(message);
        return "1";
    }

    @PostMapping("generateActivity")
    public Object generateActivity(@RequestBody FeatureMqModel.Message message) {
        FeatureEngineService.generateActivity(message);
        return "1";
    }

    @PostMapping("finish")
    public Object finish(@RequestBody FeatureNode message) {
        nodeService.finish(message);
        return "1";
    }

    @PostMapping("expression")
    public Object expression(@RequestBody ObjectData.ObjectChange message) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(message.getContext().getTenantId(), "AccountObj");
        String id = message.getObjectId();
        IObjectData data = new com.facishare.paas.metadata.impl.ObjectData(message.getAfterTriggerData());
        Map<String, Object> extData = Maps.newHashMap();

        String[] strs = new String[] { "establishment_date__c", "name", "field_aO9b1__c", "field_fIKHO__c" };
        for (String str : strs) {
            String content = "!ISNULL($establishment_date__c$)";
            content = content.replaceAll("establishment_date__c", str);
            // 获取高级计算公式
            SimpleExpression simpleExpression = SimpleExpression.builder().expression(content).id(id).build();
            simpleExpression.setReturnType(IFieldType.TRUE_OR_FALSE);

            // 调接口执行计算，calculateResult的key是"test_exp__c"，value是计算结果
            long start = System.currentTimeMillis();
            Map<String, Object> calculateResult = expressionCalculateLogicService.calculateWithExpression(
                    objectDescribe,
                    data, extData, Lists.newArrayList(simpleExpression));
            System.out.println("expression:" + (System.currentTimeMillis() - start));
            System.out.println("calculateResult:" + calculateResult);
        }

        return "expression";
    }

    @PostMapping("semantic")
    public Object semantic(@RequestBody ObjectData.ObjectChange message) {
        User user = User.systemUser(message.getContext().getTenantId());

//        String[] prompts = new String[] { "prompt_qianwen__c", "prompt_doubao__c", "prompt_hunyuan__c",
//                "prompt_deepseek__c", "prompt_ERNIE_Bot__c" };
        String[] prompts = new String[] { "sfa_feature_industry" };
        for (String str : prompts) {
            // 调LLM接口执行计算
            String promptApiName = str;
            String[] industrys = new String[] { "SAAS行业","汽车行业" };
            for (String industry : industrys) {
                Map<String, Object> sceneVariables = new HashMap<>();
                sceneVariables.put("industry", industry);
                sceneVariables.put("comparison", "高服行业,制造行业,快消行业");
                AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
                        .apiName(promptApiName)
                        .sceneVariables(sceneVariables)
                        .build();
                long start = System.currentTimeMillis();
                String result = completionsService.requestCompletion(user, arg);
                System.out.println(str + industry + "耗时:" + (System.currentTimeMillis() - start));
                System.out.println(str + industry + "结果:" + result);
            }
        }
        // String id = message.getObjectId();
        //
        // String[] strs = new String[] { "prompt_industry__c", "prompt_oqw5y__c" };
        // for (String str : strs) {
        // String content = str;
        //
        // // 调LLM接口执行计算
        // long start = System.currentTimeMillis();
        // String promptApiName = content;
        // AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
        // .apiName(promptApiName)
        // .bingObjectDataId(id)
        // .build();
        // String result = completionsService.requestCompletion(user, arg);
        // System.out.println("传id耗时:" + (System.currentTimeMillis() - start));
        // System.out.println("传id结果:" + result);
        // }
        //
        //
        // for (String str : strs) {
        // String content = str;
        //
        // // 调LLM接口执行计算
        // long start = System.currentTimeMillis();
        // String promptApiName = content;
        // AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
        // .apiName(promptApiName)
        // .objectData(message.getAfterTriggerData())
        // .build();
        // String result = completionsService.requestCompletion(user, arg);
        // System.out.println("传数据耗时:" + (System.currentTimeMillis() - start));
        // System.out.println("传数据结果:" + result);
        // }
        //
        //
        // Map<String, String> argMap = new HashMap<>();
        // argMap.put("制造行业", "高服行业");
        // argMap.put("教育行业", "高服行业");
        // argMap.put("SAAS", "高服行业");
        //
        // for (Map.Entry<String, String> entry : argMap.entrySet()) {
        // String key = entry.getKey();
        // String value = entry.getValue();
        //
        // Map<String, Object> sceneVariables = new HashMap<>();
        // sceneVariables.put("srcIndustry", value);
        // sceneVariables.put("industry", key);
        //
        // long start = System.currentTimeMillis();
        // String promptApiName = "prompt_0UpqP__c";
        // AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
        // .apiName(promptApiName)
        // .sceneVariables(sceneVariables)
        // .build();
        // String result = completionsService.requestCompletion(user, arg);
        // System.out.println("表达式耗时:" + (System.currentTimeMillis() - start));
        // System.out.println(key +" " +value+" 结果:" + result);
        //
        // }

        return "semantic";
    }

    /**
     * 调用方法论服务的处理方法
     * 
     * @param message 对象变更信息
     * @return 处理结果
     */
    @PostMapping("methodology")
    public Object applyMethodology(@RequestBody ObjectData.ObjectChange message) {
        log.info("Calling methodology service for objectId: {}, entityId: {}",
                message.getObjectId(), message.getEntityId());
        try {
            long startTime = System.currentTimeMillis();
            // 调用MethodologyService的consumer方法处理业务数据
            methodologyService.consumer(message);
            long duration = System.currentTimeMillis() - startTime;
            log.info("Methodology service completed in {}ms", duration);
            return "Methodology service processed successfully";
        } catch (Exception e) {
            log.error("Error while processing methodology: {}", e.getMessage(), e);
            return "Error: " + e.getMessage();
        }
    }

    /**
     * 启动方法论节点
     * 
     * @param message 对象变更信息
     * @return 处理结果
     */
    @PostMapping("startMethodologyNode")
    public Object startMethodologyNode(@RequestBody ObjectData.ObjectChange message) {
        log.info("启动方法论节点，objectId: {}, entityId: {}", 
                message.getObjectId(), message.getEntityId());
        try {
            long startTime = System.currentTimeMillis();
            // 调用MethodologyNodeService的startMethodologyNode方法
            methodologyNodeService.startMethodologyNode(message);
            long duration = System.currentTimeMillis() - startTime;
            log.info("方法论节点启动完成，耗时: {}ms", duration);
            return "方法论节点启动成功";
        } catch (Exception e) {
            log.error("启动方法论节点异常: {}", e.getMessage(), e);
            return "启动方法论节点失败: " + e.getMessage();
        }
    }

    @PostMapping("addObjectWorkflowObj")
    public Object addObjectWorkflowObj(@RequestBody ObjectData.ObjectChange message) {
        log.info("开始创建ObjectWorkflowObj记录");
        try {
            User user = User.systemUser(message.getContext().getTenantId());
            
            // 根据描述创建ObjectWorkflowObj记录
            List<IObjectData> objectWorkflowList = Lists.newArrayList();
            
            // 配置数据：objectApiName, fieldName, fieldValue, methodologyId, nodeId
            String[][] configs = {
                // LeadsObj配置
                {"LeadsObj", "leads_status", "Lead", "67e37400d4bcec0001465996", "67ed11e8829bce0001398b61"},
                {"LeadsObj", "leads_status", "MQL", "67e37400d4bcec0001465996", "67ed1206829bce0001398f4f"},
                {"LeadsObj", "leads_status", "SQL", "67e37400d4bcec0001465996", "6836ad89a7c3f00001710a75"},
                
                // NewOpportunityObj配置
                {"NewOpportunityObj", "sales_stage", "1", "67e37400d4bcec0001465996", "67ed1262829bce0001399d1b"},
                {"NewOpportunityObj", "sales_stage", "2", "67e37400d4bcec0001465996", "67ed124b829bce00013998b3"},
                {"NewOpportunityObj", "sales_stage", "3", "67e37400d4bcec0001465996", "6836aed1938aa30001c8898e"},
                {"NewOpportunityObj", "sales_stage", "4", "67e37400d4bcec0001465996", "6836af01938aa30001c88da4"}
            };
            
            // 为每个配置创建一条ObjectWorkflowObj记录
            for (String[] config : configs) {
                String objectApiName = config[0];
                String objectFieldName = config[1];
                String objectFieldValue = config[2];
                String methodologyId = config[3];
                String nodeId = config[4];
                
                IObjectData objectWorkflowData = new com.facishare.paas.metadata.impl.ObjectData();
                objectWorkflowData.setTenantId(user.getTenantId());
                objectWorkflowData.setDescribeApiName(ObjectMethodologyConstants.API_NAME);
                
                // 设置负责人（必填字段）
                objectWorkflowData.setOwner(Lists.newArrayList(user.getUserId()));
                
                // 设置字段值
                objectWorkflowData.set(ObjectMethodologyConstants.OBJECT_API_NAME, objectApiName);
                objectWorkflowData.set(ObjectMethodologyConstants.OBJECT_FIELD_NAME, objectFieldName);
                objectWorkflowData.set(ObjectMethodologyConstants.OBJECT_FIELD_VALUE, objectFieldValue);
                objectWorkflowData.set(ObjectMethodologyConstants.METHODOLOGY_ID, methodologyId);
                objectWorkflowData.set(ObjectMethodologyConstants.NODE_ID, nodeId);
                
                // 设置名称
                objectWorkflowData.setName(String.format("%s-%s-%s", objectApiName, objectFieldName, objectFieldValue));
                
                objectWorkflowList.add(objectWorkflowData);
                
                log.info("创建配置记录: {} - {} - {}", objectApiName, objectFieldName, objectFieldValue);
            }
            
            // 批量保存到数据库
            serviceFacade.bulkSaveObjectData(objectWorkflowList, user);
            
            log.info("成功创建{}条ObjectWorkflowObj记录", objectWorkflowList.size());
            return String.format("成功创建%d条ObjectWorkflowObj记录", objectWorkflowList.size());
            
        } catch (Exception e) {
            log.error("创建ObjectWorkflowObj记录失败: {}", e.getMessage(), e);
            return "创建ObjectWorkflowObj记录失败: " + e.getMessage();
        }
    }


    @PostMapping("initFeatureTask")
    public Object initFeatureTask(@RequestBody FeatureTaskInitDocument message) {
        featureTaskInitDao.initTask(message);
        return "1";
    }

    @PostMapping("generateJob")
    public Object generateJob(@RequestBody FeatureTaskDocument message) {
        FeatureEngineService.generateJob(message.getTenantId());
        return "1";
    }

    @PostMapping("generateAllJob")
    public Object generateAllJob(@RequestBody FeatureTaskDocument message) {
        TriggerParam triggerParam = new TriggerParam();
        jobHandler.execute(triggerParam);
        return "1";
    }
}
