package com.facishare.crm.web;

import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.crm.task.sfa.bizfeature.service.NomonProfileScoreService;
import com.facishare.crm.task.sfa.bizfeature.service.ProfileCommonService;
import com.facishare.crm.task.sfa.bizfeature.service.ProfileScoreService;
import com.facishare.crm.task.sfa.bizfeature.service.RealTimeProfileScoreService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("profileScore")
public class ProfileScoreController {

    @Resource
    private RealTimeProfileScoreService realTimeProfileScoreService;

    @Resource
    private NomonProfileScoreService nomonProfileScoreService;

    @Resource
    private ProfileCommonService profileCommonService;

    @Resource
    private ProfileScoreService profileScoreService;

    @PostMapping("realTimeProfileScore")
    public Object realTimeProfileScore(@RequestBody ProfileScoreModel param) {
        if (StringUtils.isAnyEmpty(param.getTenantId(), param.getObjectApiName(), param.getObjectId())) {
            return "error";
        }
        realTimeProfileScoreService.scoreCalc(param);
        return "success";
    }

    @PostMapping("nomonProfileScore")
    public Object nomonProfileScore(@RequestBody ProfileScoreModel param) {
        if (StringUtils.isEmpty(param.getTenantId())) {
            return "error";
        }
        nomonProfileScoreService.scoreCalc(param);
        return "success";
    }

    @PostMapping("initProfileComponent")
    public Object initProfileComponent(@RequestBody ProfileScoreModel param) {
        if (StringUtils.isEmpty(param.getTenantId())) {
            return "error";
        }
        profileCommonService.initProfileComponent(User.systemUser(param.getTenantId()));
        return "success";
    }

    @PostMapping("createNextTask")
    public Object createNextTask(@RequestBody ProfileScoreModel param) {
        if (StringUtils.isEmpty(param.getTenantId())) {
            return "error";
        }
        profileCommonService.createNextTask(param.getTenantId());
        return "success";
    }

    @PostMapping("getTaskFeatureScores")
    public Object getTaskFeatureScores(@RequestBody TaskFeatureScoreParam param) {
        if (StringUtils.isAnyEmpty(param.getTenantId(), param.getMethodologyId(), param.getObjectApiName(), param.getObjectId())) {
            return "error";
        }
        if (CollectionUtils.isEmpty(param.getTaskFeatureList())) {
            return "error";
        }
        User user = User.systemUser(param.getTenantId());
        List<IObjectData> taskFeatureList = ObjectDataDocument.ofDataList(param.getTaskFeatureList());
        profileScoreService.getTaskFeatureScores(user, param.getMethodologyId(), param.getObjectApiName(), param.getObjectId(), taskFeatureList);
        return "success";
    }

    @Data
    public static class TaskFeatureScoreParam {
        String tenantId;
        String methodologyId;
        String objectApiName;
        String objectId;
        List<ObjectDataDocument> taskFeatureList;
    }
}
