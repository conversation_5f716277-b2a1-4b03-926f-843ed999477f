package com.facishare.crm.web;

import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.service.ActivityResourceUsageService;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityContactInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.*;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.sfa.lto.activity.mongo.InteractionStrategyTaskDocument;
import com.facishare.crm.task.sfa.xxl.StrategyTaskJobHandler;
import com.facishare.paas.appframework.core.model.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("activity_suggest_issue")
@AllArgsConstructor
public class ActivitySuggestIssueController {
    private ActivitySuggestIssueService activitySuggestIssueService;
    private ActivityNewBusinessService activityNewBusinessService;
    private ActivityInteractionService activityInteractionService;
    @Autowired
    protected ActivityMongoDao activityMongoDao;
    @Autowired
    private AIKnowledgeBaseService aiKnowledgeBaseService;
    @Autowired
    private RequirementService requirementService;
    @Autowired
    private StrategyTaskJobHandler strategyTaskJobHandler;
    @Autowired
    private ActivityContactInsightService activityContactInsightService;

    @PostMapping("consumer")
    public Object consumer(@RequestBody ActivityMessage activityMessage) {
        activitySuggestIssueService.consumer(activityMessage);
        return "OK";
    }

    @PostMapping("usage/query")
    public long queryUsage(@RequestBody ActivityMessage activityMessage) {
        InteractiveDocument document = activityMongoDao.queryLargestSeqDocument(activityMessage.getTenantId(), activityMessage.getObjectApiName(), activityMessage.getObjectId());
        if (document == null) {
            return -1;
        }
        Duration duration = ActivityResourceUsageService.parseDuration(document.getStartTime());
        return TimeUnit.MILLISECONDS.toSeconds(duration.toMillis());
    }

    @PostMapping("activity_newbusness")
    public void activity_newbusness(@RequestBody ActivityMessage activityMessage) {
        activityNewBusinessService.handleNewBusiness(activityMessage,false);
    }

    @PostMapping("activity_Interaction")
    public void activity_Interaction(@RequestBody ActivityMessage activityMessage) {
        activityInteractionService.handleActivityInteraction(activityMessage,false,false);
    }

    @PostMapping("refreshDataIsDeleted")
    public void refreshDataIsDeleted(@RequestBody String body) {
        boolean success = false;
        String[] tenantIds = body.split(";");
        for (String tenantId : tenantIds) {
            activityMongoDao.refreshDataIsDeleted(tenantId);
        }
    }
    @PostMapping("refreshDataNewBusiness")
    public void refreshDataNewBusiness(@RequestBody String body) {
        boolean success = false;
        String[] tenantIds = body.split(";");
        for (String tenantId : tenantIds) {
            activityNewBusinessService.refreshData(tenantId);
        }
    }
    @PostMapping("activity_Interaction_find_base")
    public Object activity_Interaction_find_base(@RequestBody ActivityMessage activityMessage) {
        return aiKnowledgeBaseService.proxyKnowledgeService(new User(activityMessage.getTenantId(), CommonConstant.SUPER_USER),activityMessage.getObjectId());
    }


    @PostMapping("requirementService_execute")
    public void requirementService_execute(@RequestBody ActivityMessage activityMessage) {
        requirementService.execute(activityMessage);
    }

    @PostMapping("queryKnowledgeBaseByAISummary")
    public Object queryKnowledgeBaseByAISummary(@RequestBody ActivityMessage activityMessage) {
       return aiKnowledgeBaseService.queryKnowledgeBaseByAISummary(new User(activityMessage.getTenantId(), CommonConstant.SUPER_USER),activityMessage.getObjectId());
    }

    /**
     * 处理策略任务
     * @param taskRequestDTO 任务请求DTO
     */
    @PostMapping("processStrategyTasks")
    public void processStrategyTasks(@RequestBody StrategyTaskRequestDTO taskRequestDTO) {
        log.info("接收到策略任务处理请求: {}", taskRequestDTO);
        try {
            // 将DTO转换为InteractionStrategyTaskDocument列表
            List<InteractionStrategyTaskDocument> taskList = taskRequestDTO.getTasks().stream()
                    .map(dto -> {
                        InteractionStrategyTaskDocument document = new InteractionStrategyTaskDocument();

                        // 只在非null时设置字段值
                        if (dto.getId() != null) {
                            document.setId(new ObjectId(dto.getId()));
                        }

                        if (dto.getTenantId() != null) {
                            document.setTenantId(dto.getTenantId());
                        }

                        if (dto.getStrategyId() != null) {
                            document.setStrategyId(dto.getStrategyId());
                        }

                        if (dto.getStatus() != null) {
                            document.setStatus(dto.getStatus());
                        }

                        if (dto.getCreateTime() != null) {
                            document.setCreateTime(dto.getCreateTime());
                        }

                        // 其他必要字段设置
                        return document;
                    })
                    .collect(Collectors.toList());

            // 调用StrategyTaskJobHandler的processTasks方法
            strategyTaskJobHandler.processTasks(taskList);
            log.info("策略任务处理完成，共处理{}个任务", taskList.size());
        } catch (Exception e) {
            log.error("处理策略任务时发生错误", e);
            throw new RuntimeException("处理策略任务失败: " + e.getMessage());
        }
    }
    @PostMapping("ActivityContactInsightService_initTags")
    public void ActivityContactInsightService_initTags(@RequestBody ActivityMessage activityMessage) {
        activityContactInsightService.initTags( User.systemUser(activityMessage.getTenantId()));
    }

    @PostMapping("ActivityContactInsightService_execute")
    public void ActivityContactInsightService_execute(@RequestBody ActivityContactInsightModel.Arg arg) {
        activityContactInsightService.execute( arg);
    }
    /**
     * 策略任务请求DTO
     */
    @Data
    public static class StrategyTaskRequestDTO {
        private List<StrategyTaskDTO> tasks;
    }

    /**
     * 策略任务DTO
     */
    @Data
    public static class StrategyTaskDTO {
        private String id;
        private String tenantId;
        private String strategyId;
        private String status;
        private Long createTime;
        // 其他必要字段
    }
}
