package com.facishare.crm.web;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileConstants;
import com.facishare.crm.task.sfa.bizfeature.model.*;
import com.facishare.crm.task.sfa.bizfeature.service.*;
import com.facishare.crm.task.sfa.common.enums.ConfigType;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("profileAdvantage")
public class ProfileAdvantageController {
    @Resource
    private ProfileProsConsService profileProsConsService;
    @Resource
    private ProfileAdviceService profileAdviceService;
    @Resource
    private CrmNoteManager crmNoteManager;
    @Resource
    ServiceFacade  serviceFacade;
    @Resource
    ProfileAdviceProducer profileAdviceProducer;
    @Resource
    FeatureInitService featureInitService;
    @Resource
    protected ConfigService configService;

    @PostMapping("generateProfileProsCons")
    public void generateProfileProsCons(@RequestBody ProfileAdviceModel param) {
        FeatureCrmNoteContext featureCrmNoteContext = FeatureCrmNoteContext.builder()
                .tenantId(param.getTenantId()).receiverIds(Lists.newArrayList(Integer.valueOf(param.getReceiverId())))
                .refreshType(param.getRefreshType()).build();
        try {
            if (StringUtils.isAnyEmpty(param.getTenantId(), param.getProfileId())) {
                featureCrmNoteContext.setSucess(false);
                log.error("generateProfileProsCons param error ");
            }
            fillProfileAdviceModel(param,featureCrmNoteContext);
            profileProsConsService.generateProfileProsCons(param, featureCrmNoteContext);
            featureCrmNoteContext.setSucess(true);
        } catch (Exception ex) {
            log.error("generateProfileProsCons unknown exception : ", ex);
            featureCrmNoteContext.setSucess(false);
            throw ex;
        } finally {
            crmNoteManager.sendCrmNote(featureCrmNoteContext);
        }
    }



    @PostMapping("generateProfileAdvice")
    public void generateProfileAdvice(@RequestBody ProfileAdviceModel param) {
        FeatureCrmNoteContext featureCrmNoteContext = FeatureCrmNoteContext.builder()
                .tenantId(param.getTenantId()).receiverIds(Lists.newArrayList(Integer.valueOf(param.getReceiverId())))
                .refreshType(param.getRefreshType()).build();
        try {
            if (StringUtils.isAnyEmpty(param.getTenantId(), param.getProfileId())) {
                featureCrmNoteContext.setSucess(false);
                log.error("generateProfileProsCons param error ");
            }
            fillProfileAdviceModel(param,featureCrmNoteContext);
            profileAdviceService.generateAdvice(param, featureCrmNoteContext);
            featureCrmNoteContext.setSucess(true);
        } catch (Exception ex) {
            log.error("generateProfileProsCons unknown exception : ", ex);
            featureCrmNoteContext.setSucess(false);
            throw ex;
        } finally {
            crmNoteManager.sendCrmNote(featureCrmNoteContext);
        }
    }

    @PostMapping("init_feature")
    public void initFeature(@RequestBody FeatureInitModel param) {
        try {
            featureInitService.initModule(param);
            setConfigValue(User.systemUser(param.getTenantId()), "1");
            crmNoteManager.sendFeatureInitNote(param.getTenantId(), param.getReceiverId(), true);
        } catch (Exception ex) {
            log.error("generateProfileProsCons unknown exception : ", ex);
            setConfigValue(User.systemUser(param.getTenantId()), "0");
            crmNoteManager.sendFeatureInitNote(param.getTenantId(), param.getReceiverId(), false);
        }
    }

/*    @PostMapping("getKeyScoreMap")
    public Map<String, BigDecimal> getKeyScoreMap(@RequestBody ProfileAdviceModel param) {
        return profileProsConsService.getKeyScoreMap(param.getTenantId(), param.getProfileId(), param.getObjectId());
    }*/

    @PostMapping("testAdviceMq")
    public void testAdviceMq(@RequestBody ProfileAdviceModel param) {
        ProfileAdviceMqModel.Message profileAdviceMqModel = ProfileAdviceMqModel.Message.builder()
                .tenantId(param.getTenantId())
                .profileId(param.getProfileId())
                .refreshType(param.getRefreshType())
                .receiverId(param.getReceiverId())
                .build();
        profileAdviceProducer.sendMessage(profileAdviceMqModel);
    }

    @PostMapping("testInitMq")
    public void testInitMq(@RequestBody FeatureInitModel param) {
        ProfileAdviceMqModel.Message profileAdviceMqModel = ProfileAdviceMqModel.Message.builder()
                .tenantId(param.getTenantId())
                .tags("feature-init-data")
                .receiverId(param.getReceiverId())
                .build();
        profileAdviceProducer.sendMessage(profileAdviceMqModel);
    }

    private void fillProfileAdviceModel(ProfileAdviceModel param,FeatureCrmNoteContext featureCrmNoteContext) {
        String profileId = param.getProfileId();
        if (com.google.common.base.Strings.isNullOrEmpty(profileId)) {
            return;
        }

        // 1. 查询画像及所有分项得分
        User user = User.systemUser(param.getTenantId());
        IObjectData profile = serviceFacade.findObjectData(user, profileId, FeatureConstants.PROFILE);
        if (profile == null) {
            return;
        }
        param.setProfile(profile);
        String type = profile.get(ProfileConstants.TYPE, String.class);
        String objectId = StringUtil.EMPTY;
        String objectApiName = StringUtil.EMPTY;
        if (type.equals(ProfileConstants.Type.ACCOUNT.getValue())) {
            objectId = profile.get(ProfileConstants.ACCOUNT_ID, String.class);
            objectApiName = Utils.ACCOUNT_API_NAME;
        }
        if (type.equals(ProfileConstants.Type.LEAD.getValue())) {
            objectId = profile.get(ProfileConstants.LEAD_ID, String.class);
            objectApiName = Utils.LEADS_API_NAME;
        }
        if (type.equals(ProfileConstants.Type.OPPORTUNITY.getValue())) {
            objectId = profile.get(ProfileConstants.OPPORTUNITY_ID, String.class);
            objectApiName = Utils.NEW_OPPORTUNITY_API_NAME;
        }

        param.setObjectId(objectId);
        param.setObjectDescribeApiName(objectApiName);
        featureCrmNoteContext.setObjectId(objectId);
        featureCrmNoteContext.setApiName(objectApiName);
    }

    private void setConfigValue(User user, String value) {
        String queryRst = configService.findTenantConfig(user, ConfigType.CUSTOMER_PROFILE_AGENT.getKey());
        if (StringUtils.isBlank(queryRst)) {
            configService.createTenantConfig(user, ConfigType.CUSTOMER_PROFILE_AGENT.getKey(), value, ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(user, ConfigType.CUSTOMER_PROFILE_AGENT.getKey(), value, ConfigValueType.STRING);
        }
    }
}
