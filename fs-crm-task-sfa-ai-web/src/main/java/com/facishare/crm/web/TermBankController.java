package com.facishare.crm.web;

import com.facishare.crm.task.sfa.services.TermBankService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("term_bank")
public class TermBankController {
    private final TermBankService termBankService;

    @PostMapping("correct")
    public Object correct(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestBody CorrectParam param) {
        return termBankService.correct(tenantId, param.text);
    }

    @Data
    public static class CorrectParam {
        List<String> text;
    }

    @PostMapping("cache/clear")
    public Object clearCache(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId) {
        termBankService.clearCache(tenantId);
        return "OK";
    }
}
