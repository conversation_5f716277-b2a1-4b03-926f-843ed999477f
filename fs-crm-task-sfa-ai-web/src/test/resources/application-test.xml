<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
   http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop.xsd">

  <import resource="classpath:spring/metadata.xml"/>
  <import resource="classpath:spring/common.xml"/>
  <import resource="classpath:spring/dubbo.xml"/>
  <import resource="classpath:spring/log.xml"/>
  <import resource="classpath:spring/flow.xml"/>
  <import resource="classpath:spring/privilege.xml"/>
  <import resource="classpath:spring/licence.xml"/>
  <import resource="classpath:spring/fsi.xml"/>
  <import resource="classpath:spring/rest.xml"/>
  <import resource="classpath:spring/mq.xml"/>
  <import resource="classpath:spring/config.xml"/>
  <import resource="classpath:spring/function-service.xml"/>
  <import resource="classpath:spring/spring-xxl-job.xml"/>
  <import resource="classpath:spring/spring-timing-task.xml"/>
  <import resource="classpath:spring/payment.xml"/>
  <import resource="classpath:privilege-temp.xml"/>
  <import resource="classpath:spring/cores.xml"/>
  <import resource="classpath:spring/global-transaction-tcc.xml"/>
  <import resource="classpath:spring/action.xml"/>
  <import resource="classpath:spring/restdriver.xml"/>
  <import resource="classpath:lto.xml"/>
  <import resource="classpath:/META-INF/spring/industry-spring-client.xml"/>
  <import resource="classpath:META-INF/fs-paas-ai-client.xml"/>



  <context:component-scan base-package="com.facishare.paas.ext"/>
  <context:component-scan base-package="com.facishare.paas.appframework,com.facishare.crm"/>
  <context:annotation-config/>

  <bean id="autoConf"
        class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
        p:fileEncoding="UTF-8"
        p:ignoreResourceNotFound="true"
        p:ignoreUnresolvablePlaceholders="false"
        p:location="classpath:application.properties"
        p:configName="dubbo-common,fs-paas-metadata-mongo,fs-paas-appframework-rest,fs-crm-java-config
            ,fs-crm-printconfig,fs-metadata,fs-crm-icon-path,fs-crm-java-detailpage-layout-setting,fs-crm-sys-variable
            ,fs-crm-follow-crm-action-consumer，fs-restful-common,fs-crm-java-openapi-black-list,fs-crm-privilege
            , fs-crm-java-fsi-proxy,fs-crm-java-special-field,fs-crm-add-function-code
            ,fs-crm-java-reference_relation_type,fs-crm-printconfig,fs-metadata
            ,new-predefined-com.facishare.crm.privilege.objects,new-predefined-object"/>

  <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
        c:placeholderConfigurer-ref="autoConf"/>

  <!--    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>-->

  <bean id="paymentPlanOverdueMQSender"
        class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
        p:configName="payment-plan-overdue-mq"></bean>

  <!--蜂眼监控-->
  <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>

  <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.crm.task.sfa.services.rebate.rest.RuleEngineProxy">
    <property name="factory" ref="restServiceProxyFactory"/>
  </bean>
  <aop:config proxy-target-class="true">
    <aop:aspect ref="serviceProfiler" order="1">
      <aop:around method="profile"
                  pointcut="execution(* com.facishare.crm.available.range.*.*(..))"/>
      <aop:around method="profile"
                  pointcut="execution(* com.facishare.crm.available.range.listener.*.*(..))"/>
      <aop:around method="profile"
                  pointcut="execution(* com.facishare.crm.available.range.dao.*.*(..))"/>
      <aop:around method="profile"
                  pointcut="execution(* com.facishare.crm.available.range.service.*.*(..))"/>
    </aop:aspect>
  </aop:config>
</beans>

