POST {{baseUrl}}/available_range/re_calculate?tenantId=79535&availableRangeId=5f685ee9d1967e0001f76f98&duplicatedCheck=true
Content-Type: application/json

###

POST {{baseUrl}}/available_range/re_calculate_tenant?tenantId=79535
Content-Type: application/json


###
POST {{baseUrl}}/available_range/object_data
Content-Type: application/json

{
  "body": [{
    "eventId": "5ed8a8dfaf2ecc0001f70442",
    "context": {
      "appId": "CRM",
      "tenantId": "79535",
      "userId": "1000"
    },
    "name": "sdfsdf",
    "entityId": "AvailableRangeObj",
    "triggerType": "i",
    "objectId": "5f685ee9d1967e0001f76f98"
  }],
  "name": "object_data",
  "op": "i",
  "tenantId": "79535"
}


###

POST {{baseUrl}}/price_policy/re_calculate?tenantId=81134&pricePolicyId=5fdb0fc73507010884175b7a&duplicatedCheck=true
Content-Type: application/json

###

POST {{baseUrl}}/price_policy/re_calculate_tenant?tenantId=83382
Content-Type: application/json