variables:
  GIT_DEPTH: 0
  MAVEN_OPTS: >-
    -Dserver
    -Dhttps.protocols=TLSv1.2
    -Dorg.slf4j.simpleLogger.showDateTime=true
    -Djava.awt.headless=true
    -Xmx4096m

  MAVEN_CLI_OPTS: >-
    --batch-mode
    --errors
    --fail-at-end
    --show-version
    --no-transfer-progress

default:
  interruptible: true
  tags:
    - jdk17
    - sonar
  artifacts:
    expire_in: 3 week
    exclude:
      - /**/*.class
      - /**/*.war
      - /**/lib/*.jar

stages:
  - verify
  - deploy

verify:
  stage: verify
  timeout: 50m
  only:
    - merge_requests
    - main
    - master
    - release
    - develop
    - dev
    - hotfix
    - bugfix
    - /develop\/[-\d.]*/
    - /dev\/[-\d.]*/
    - /hotfix\/[-\d.]*/
    - /bugfix\/[-\d.]*/
    - /hotfix\/.*/
  before_script:
    - export SONAR_OPTS=$(grep '^sonar.' sonar-project.properties | sed -e 's|^|-D|g' | grep "sonar." | tr '\r\n' ' ')
  script:
    - pwd
    - FORMATTED_VERSION=`date +%Y%m%d`
    - echo "Formatted Version is $FORMATTED_VERSION"
    - 'mvn $MAVEN_CLI_OPTS clean verify sonar:sonar -Dmaven.test.failure.ignore=true $SONAR_OPTS -Dsonar.branch.name=${CI_COMMIT_REF_NAME} -Dsonar.projectVersion=${FORMATTED_VERSION}#${CI_BUILD_ID}'


deploy:
  stage: deploy
  timeout: 10m
  script:
    - pwd
    - 'mvn $MAVEN_CLI_OPTS clean deploy -Dmaven.test.skip=true'
  except:
    variables:
      - $NightlyBuild == "1"
  only:
    variables:
      - $SHARECRM_RELEASE == "1"