package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 方法论匹配规则常量
 *
 * <AUTHOR>
 */
public interface MethodologyRuleConstants {
     String API_NAME = "MethodologyRuleObj";
     /**
      * 方法论
      */
     String METHODOLOGY_ID = "methodology_id";
     /**
      * 匹配对象
      */
     String OBJECT_API_NAME = "object_api_name";
     /**
      * 匹配规则
      */
     String RULE_CONTENT = "rule_content";
     /**
      * 负责人所在部门
      */
     String OWNER_DEPARTMENT = "owner_department";
     /**
      * 相关团队
      */
     String RELEVANT_TEAM = "relevant_team";
     /**
      * 前置对象
      */
     String PRE_OBJECT_API_NAME = "pre_object_api_name";
     /**
      * 前置对象字段
      */
     String PRE_FIELD_API_NAME = "pre_field_api_name";
     /**
      * 顺序
      */
     String SEQ = "seq";
}