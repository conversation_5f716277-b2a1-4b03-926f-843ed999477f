package com.facishare.crm.task.sfa.activitysummary.service.paragraph;

import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphDocument;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTagMessage;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphContext;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphResultModel;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent.ParagraphAIAgentService;
import com.facishare.crm.task.sfa.activitysummary.service.ParagraphAIService;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.User;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 内容批处理服务类，负责处理内容批次相关的操作
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class ContentBatchProcessService {

    @Autowired
    private ParagraphAIService paragraphAiService;
    @Autowired
    private ParagraphAIAgentService paragraphAIAgentService;
    @Autowired
    private IDProcessingService idProcessingService;
    @Autowired
    private DocumentProcessService documentProcessService;
    @Autowired
    private ParagraphProducer paragraphProducer;
    @Autowired
    private ParagraphEndService endService;

    // 是否使用AI Agent模式
    private static boolean useAIAgent = false;

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            useAIAgent = config.getBool("use_ai_agent_for_paragraph", false);
        });
    }

    /**
     * 处理页面文档
     */
    public String processPageDocuments(User user, List<InteractiveDocument> pageDocuments, String previousSummary,
                                       ParagraphContext paragraphContext, StopWatch stopWatch) {

        if (CollectionUtils.isEmpty(pageDocuments)) {
            return previousSummary;
        }
        String currentSummary = previousSummary;
        for (InteractiveDocument doc : pageDocuments) {
            paragraphContext.addContent(doc);
            if (!paragraphContext.isContentLengthSufficient()) {
                continue;
            }
            Optional<ParagraphResultModel> paragraphModelOpt = processParagraphDocument(user, paragraphContext,
                    currentSummary);
            currentSummary = paragraphModelOpt.map(ParagraphResultModel::getChunks)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(chunks -> chunks.get(0))
                    .map(ParagraphResultModel.Chunk::getSummary)
                    .orElse("");
            stopWatch.lap("处理内容批次");
        }
        return currentSummary;
    }

    /**
     * 处理一批内容并更新摘要
     *
     * @return 更新后的摘要
     */
    public Optional<ParagraphResultModel> processParagraph(User user, ParagraphContext paragraphContext, String previousSummary) {
        try {
            String currentContent = paragraphContext.contentBuilder();
            ParagraphResultModel paragraphResultModels = doParagraph(user, currentContent, previousSummary,
                    paragraphContext.getIdList(), paragraphContext.getContentList());
            if (paragraphResultModels != null) {
                List<ParagraphDocument> paragraphDocuments = documentProcessService.saveSegmentationResult(user, paragraphResultModels, paragraphContext);
                sendMarkMessage(paragraphDocuments, paragraphContext, user);
            }
            return Optional.ofNullable(paragraphResultModels);
        } catch (Exception e) {
            log.error("处理文档内容时发生异常", e);
            return Optional.empty();
        } finally {
            paragraphContext.clearAll();
        }
    }

    private void sendMarkMessage(List<ParagraphDocument> paragraphDocuments, ParagraphContext paragraphContext, User user) {
        if (CollectionUtils.isEmpty(paragraphDocuments)) {
            return;
        }
        setBatchTotalCache(paragraphContext, user);
        List<String> paragraphIds = paragraphDocuments.stream()
                .map(ParagraphDocument::getId)
                .map(ObjectId::toString)
                .collect(Collectors.toList());
        ActivityTagMessage activityTagMessage = ActivityTagMessage.builder()
                .paragraphIds(paragraphIds)
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .objectApiName(paragraphContext.getActiveRecordData().getDescribeApiName())
                .objectId(paragraphContext.getActiveRecordData().getId())
                .type(paragraphContext.getType())
                .build();
        paragraphProducer.sendMessage(activityTagMessage);
    }

    private void setBatchTotalCache(ParagraphContext context, User user) {
        endService.incrTotal(context, user);
        endService.setEndFlag(context, user);
    }

    /**
     * 处理一批内容并更新摘要
     *
     * @return 更新后的摘要
     */
    public Optional<ParagraphResultModel> processParagraphDocument(User user, ParagraphContext paragraphContext,
                                                                   String previousSummary) {
        try {
            // 创建 ID 映射模型并构建映射关系
            paragraphContext.buildMapping();
            String contentWithIndexes = paragraphContext.contentBuilder();
            // 调用大模型
            ParagraphResultModel paragraphResultModels = doParagraph(user, contentWithIndexes, previousSummary,
                    paragraphContext.getIdList(), paragraphContext.getContentList());
            // 恢复原始 ID
            resetId(paragraphContext, paragraphResultModels);
            // 保存段落结果
            List<ParagraphDocument> paragraphDocuments = documentProcessService.saveSegmentationResult(user, paragraphResultModels, paragraphContext);
            sendMarkMessage(paragraphDocuments, paragraphContext, user);
            return Optional.ofNullable(paragraphResultModels);
        } catch (Exception e) {
            log.error("处理文档内容时发生异常", e);
            return Optional.empty();
        } finally {
            paragraphContext.clearAll();
        }
    }

    private void resetId(ParagraphContext paragraphContext, ParagraphResultModel paragraphResultModels) {
        if (paragraphResultModels != null && paragraphResultModels.getChunks() != null) {
            for (ParagraphResultModel.Chunk chunk : paragraphResultModels.getChunks()) {
                if (chunk.getContent() != null) {
                    chunk.setContent(paragraphContext.restoreIdsFromIndexes(chunk));
                }
            }
        }
    }

    /**
     * 处理内容并更新summary
     */
    public ParagraphResultModel doParagraph(User user, String currentContent, String previousSummary,
                                            List<String> currentBatchIds, List<String> contentList) {
        if (!validateInputs(currentContent, currentBatchIds)) {
            return null;
        }
        log.debug("处理内容并更新摘要, 当前摘要长度: {}, 使用AI Agent: {}",
                previousSummary != null ? previousSummary.length() : 0, useAIAgent);

        ParagraphResultModel results;
        if (useAIAgent) {
            // 使用AI Agent模式
            log.info("使用AI Agent模式进行段落分割");
            results = paragraphAIAgentService.requestWithRetry(user, currentContent, previousSummary, currentBatchIds, contentList);
        } else {
            // 使用普通模式
            results = paragraphAiService.requestWithRetry(user, currentContent, previousSummary);
        }

        if (results != null) {
            results = idProcessingService.checkMissingIds(user, currentContent, previousSummary, currentBatchIds, results);
        }
        return results;
    }

    /**
     * 验证输入参数是否有效
     */
    public boolean validateInputs(String currentContent, List<String> currentBatchIds) {
        if (StringUtils.isEmpty(currentContent)) {
            log.warn("处理内容为空，跳过处理");
            return false;
        }
        if (CollectionUtils.isEmpty(currentBatchIds)) {
            log.warn("当前批次ID为空，跳过处理");
            return false;
        }
        return true;
    }

    public void processFinalContent(User user, String previousSummary, ParagraphContext paragraphContext) {
        if (!paragraphContext.isContentEmpty()) {
            paragraphContext.setEnd(true);
            processParagraphDocument(user, paragraphContext, previousSummary);
        }
    }
}