package com.facishare.crm.task.sfa.activitysummary.service.tag;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTag;
import com.facishare.crm.task.sfa.activitysummary.model.ChatHistory;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphTagResult;
import com.facishare.crm.task.sfa.activitysummary.service.ChatBot;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.activitysummary.service.FixJSONFormatService;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标签裁判处理器
 * 负责使用裁判模型对标签分类结果进行评判和循环优化
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class TagJudgeProcessor extends BaseTagProcessingService {

    @Autowired
    private ChatBot chatBot;

    @Autowired
    private CompletionsService completionsService;

    @Autowired
    private FixJSONFormatService fixJSONFormatService;

    // 大模型打标签的API
    private static final String API_NAME_TAG = "sfa_paragraph_tag_with_suggest_prompt";
    private static final String JSON_FORMAT = "{\"tagResults\":[{\"paragraphId\":\"id1\",\"tagId\":\"P_1\"}]}";
    
    // 裁判结果的JSON格式模板
    private static final String JUDGE_JSON_FORMAT = "{\"overallScore\":85,\"isCorrect\":true,\"suggestions\":\"建议内容\",\"detailedAnalysis\":\"详细分析内容\"}";

    // 裁判模型的系统提示词
    private static final String JUDGE_SYSTEM_PROMPT = 
            "你是一个专业的标签分类质量评判专家。你的任务是对AI模型的标签分类结果进行质量评分。\n\n" +
            "请仔细阅读以下评判标准（按重要性程度从高到低排序）：\n" +
            "1. 准确性：标签分配应与段落内容严格对应，正确无误地反映内容主题和语义\n" +
            "2. 相关性：所选标签应直接关联段落内容，避免无关或不匹配的标签\n" +
            "3. 指令遵守度：严格按照标签分类的要求和限制条件执行\n" +
            "4. 逻辑连贯性：标签选择应具有内在逻辑性和一致性\n" +
            "5. 完整性：不应遗漏重要的标签，也不应包含错误的标签\n" +
            "6. 理解深度：体现对段落复杂语义和隐含信息的理解\n" +
            "7. 简洁性：标签选择应精准且避免冗余\n\n" +
            "特别注意：\n" +
            "- 当AI分类结果为空时，需要评估是否确实没有合适的标签可以分配\n" +
            "- 空结果可能是正确的（内容确实不匹配任何标签）或错误的（遗漏了应有的标签）\n" +
            "- 请根据段落内容和可选标签列表，判断空结果是否合理\n\n" +
            "评分流程：\n" +
            "1. 仔细阅读和理解段落内容及可选标签\n" +
            "2. 从上述标准中识别当前分类结果表现好和不足的关键点\n" +
            "3. 依据各标准重要性给出加权评分，综合计算总分（0-100分）\n" +
            "4. 如果评分低于80分，提供具体的改进建议\n\n" +
            "具体评判步骤：\n" +
            "1. 从准确性、相关性、指令遵守度、逻辑连贯性、完整性、理解深度、简洁性等维度进行评估\n" +
            "2. 综合各标准重要性给出加权总分\n" +
            "3. 如评分低于80分，请提供针对性的改进建议\n" +
            "4. 对于未打标签的段落，也要进行评判\n\n" +
            "返回格式要求：\n" +
            "{\n" +
            "    \"overallScore\": 85,\n" +
            "    \"isCorrect\": true,\n" +
            "    \"suggestions\": \"针对不足之处的具体改进建议\"\n" +
            "}\n\n" +
            "请严格按照上述评分流程和JSON格式返回评判结果。";

    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 2;
    // 及格分数线
    private static final int PASS_SCORE = 80;

    /**
     * 使用裁判模型处理标签分类，包含评判和循环优化逻辑
     *
     * @param user                  用户信息
     * @param paragraphContentsChunk 段落内容
     * @param tagList               标签列表
     * @param initialResult         已执行的初始标签结果
     * @return 优化后的标签结果
     */
    public Map<String, ParagraphTagResult.TagResult> processWithJudge(User user, Map<String, String> paragraphContentsChunk, List<ActivityTag> tagList,
                                                                      Map<String, ParagraphTagResult.TagResult> initialResult) {
        if (paragraphContentsChunk.isEmpty()) {
            return new HashMap<>();
        }

        log.info("开始裁判模型处理，段落数: {}", paragraphContentsChunk.size());

        // 创建会话历史，用于整个裁判过程
        ChatHistory chatHistory = chatBot.createChatHistory();

        // 评判初始结果
        JudgeOptimizeContext context = evaluateInitialResult(user, paragraphContentsChunk, tagList, initialResult, chatHistory);
        
        // 如果初始结果已达标，直接返回
        if (context.getCurrentScore() >= PASS_SCORE) {
            log.info("初始结果已达标，得分: {}", context.getCurrentScore());
            return initialResult;
        }

        // 执行优化循环
        return executeOptimizeLoop(user, paragraphContentsChunk, tagList, context, chatHistory);
    }

    /**
     * 评判初始结果
     */
    private JudgeOptimizeContext evaluateInitialResult(User user, Map<String, String> paragraphContentsChunk, 
                                                      List<ActivityTag> tagList, Map<String, ParagraphTagResult.TagResult> initialResult,
                                                      ChatHistory chatHistory) {
        JudgeOptimizeContext context = new JudgeOptimizeContext();
        context.setBestResult(initialResult != null ? initialResult : new HashMap<>());
        context.setCurrentScore(0);

        try {
            // 确保initialResult不为null，如果为null则传入空Map
            Map<String, ParagraphTagResult.TagResult> resultToEvaluate = initialResult != null ? initialResult : new HashMap<>();
            JudgeResult judgeResult = executeJudgeEvaluation(user, paragraphContentsChunk, tagList, resultToEvaluate, chatHistory, true);
            context.setCurrentScore(judgeResult.getOverallScore());
            context.setLastSuggestions(judgeResult.getSuggestions());
            
            if (initialResult == null || initialResult.isEmpty()) {
                log.info("初始结果为空，评判完成，得分: {}", judgeResult.getOverallScore());
            } else {
                log.info("初始结果评判完成，得分: {}", judgeResult.getOverallScore());
            }
        } catch (Exception e) {
            log.error("初始结果评判失败", e);
        }

        return context;
    }

    /**
     * 执行优化循环
     */
    private Map<String, ParagraphTagResult.TagResult> executeOptimizeLoop(User user, Map<String, String> paragraphContentsChunk,
                                                         List<ActivityTag> tagList, JudgeOptimizeContext context, 
                                                         ChatHistory chatHistory) {
        for (int attempt = 1; attempt <= MAX_RETRY_COUNT; attempt++) {
            try {
                // 重新执行标签分类
                Map<String, ParagraphTagResult.TagResult> tagResult = executeTagClassification(user, paragraphContentsChunk, tagList, context.getLastSuggestions());
                
                if (tagResult.isEmpty()) {
                    log.warn("第{}次标签分类结果为空", attempt);
                    continue;
                }

                // 评判新结果
                JudgeResult judgeResult = executeJudgeEvaluation(user, paragraphContentsChunk, tagList, tagResult, chatHistory, false);
                
                log.info("第{}次优化完成，得分: {}", attempt, judgeResult.getOverallScore());

                // 更新最佳结果
                updateBestResult(context, tagResult, judgeResult);

                // 如果达到及格分数，直接返回
                if (judgeResult.getOverallScore() >= PASS_SCORE) {
                    log.info("优化达标，第{}次完成", attempt);
                    return tagResult;
                }

            } catch (Exception e) {
                log.error("第{}次优化异常", attempt, e);
            }
        }

        log.info("优化完成，最终得分: {}", context.getCurrentScore());
        return context.getBestResult();
    }

    /**
     * 更新最佳结果
     */
    private void updateBestResult(JudgeOptimizeContext context, Map<String, ParagraphTagResult.TagResult> newResult, JudgeResult judgeResult) {
        if (judgeResult.getOverallScore() > context.getCurrentScore()) {
            context.setBestResult(newResult);
            context.setCurrentScore(judgeResult.getOverallScore());
        }
        context.setLastSuggestions(judgeResult.getSuggestions());
    }

    /**
     * 执行标签分类（带建议）
     */
    public Map<String, ParagraphTagResult.TagResult> executeTagClassification(User user, Map<String, String> paragraphContentsChunk, List<ActivityTag> tagList, String suggestions) {
        try {
            // 构建内容列表
            List<Map<String, Object>> contentList = buildContentList(paragraphContentsChunk);
            // 构建单个标签的列表
            List<Map<String, Object>> tagsList = buildTagsList(tagList);

            // 构建请求参数，包含建议
            AiRestProxyModel.Arg arg = buildBaseTagArgument(API_NAME_TAG, contentList, tagsList, suggestions);
            log.info("Requesting tag LLM arg is {}", JSON.toJSONString(arg));
            // 请求大模型
            ParagraphTagResult resultModel = completionsService.requestCompletionData(user, arg, JSON_FORMAT, ParagraphTagResult.class);
            log.info("Requesting tag LLM  return data is {}", JSON.toJSONString(resultModel));
            // 处理结果
            return processTagResultModel(resultModel);
        } catch (Exception e) {
            log.error("执行标签分类时发生异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 执行裁判评估
     */
    private JudgeResult executeJudgeEvaluation(User user, Map<String, String> inputParagraphs, 
                                             List<ActivityTag> inputTags, Map<String, ParagraphTagResult.TagResult> tagResult, ChatHistory chatHistory, boolean isFirstCall) {
        try {
            // 构建用户提示词
            String userPrompt = buildJudgeUserPrompt(inputParagraphs, inputTags, tagResult);
            
            // 执行裁判对话（使用传入的会话历史）
            String judgeResponse;
            if (isFirstCall) {
                // 第一次调用，设置系统提示词
                judgeResponse = chatBot.executeRound(user.getTenantId(), chatHistory, JUDGE_SYSTEM_PROMPT, userPrompt);
            } else {
                // 后续调用，只添加用户消息
                judgeResponse = chatBot.continueChat(user.getTenantId(), chatHistory, userPrompt);
            }
            
            // 解析裁判结果
            return parseJudgeResult(judgeResponse, user);
            
        } catch (Exception e) {
            log.error("裁判模型评估失败", e);
            // 如果裁判失败，返回默认通过结果
            return JudgeResult.builder()
                    .isCorrect(true)
                    .overallScore(60)
                    .suggestions("裁判模型评估失败，建议人工检查")
                    .detailedAnalysis("系统异常，无法进行详细分析")
                    .build();
        }
    }

    /**
     * 构建裁判模型的用户提示词
     */
    private String buildJudgeUserPrompt(Map<String, String> inputParagraphs, 
                                       List<ActivityTag> inputTags,
                                        Map<String, ParagraphTagResult.TagResult> tagResult) {
        StringBuilder prompt = new StringBuilder();
        
        // 添加输入的段落内容
        prompt.append("==== 段落内容 ====\n");
        inputParagraphs.forEach((paragraphId, content) -> {
            prompt.append("段落ID: ").append(paragraphId).append("\n");
            prompt.append("内容: ").append(content).append("\n\n");
        });
        
        // 添加可选标签列表
        prompt.append("==== 可选标签列表 ====\n");
        inputTags.forEach(tag -> {
            prompt.append("标签ID: ").append(tag.getId()).append("\n");
            prompt.append("标签名称: ").append(tag.getName()).append("\n");
            if (tag.getDescription() != null) {
                prompt.append("标签描述: ").append(tag.getDescription()).append("\n");
            }
            prompt.append("\n");
        });
        
        // 添加标签分类结果
        prompt.append("==== AI分类结果 ====\n");
        if (tagResult == null || tagResult.isEmpty()) {
            prompt.append("AI模型没有为任何段落分配标签（结果为空）\n\n");
        } else {
            tagResult.forEach((paragraphId, tagResultItem) -> {
                List<String> tagIds = tagResultItem.getTagId();
                prompt.append("段落ID: ").append(paragraphId).append("\n");
                if (tagIds == null || tagIds.isEmpty()) {
                    prompt.append("分配标签: 无（该段落未分配任何标签）\n\n");
                } else {
                    prompt.append("分配标签: ").append(String.join(", ", tagIds)).append("\n\n");
                }
            });
        }
        
        return prompt.toString();
    }

    /**
     * 解析裁判结果
     */
    private JudgeResult parseJudgeResult(String judgeResponse, User user) {
        try {
            // 使用FixJSONFormatService修复可能的JSON格式问题
            JudgeResult result = fixJSONFormatService.getDataFixedInvalidJSON(user, JUDGE_JSON_FORMAT, judgeResponse, JudgeResult.class);
            
            // 验证必要字段
            if (result != null) {
                if (result.getOverallScore() == null) {
                    result.setOverallScore(60);
                }
                if (result.getIsCorrect() == null) {
                    result.setIsCorrect(result.getOverallScore() >= PASS_SCORE);
                }
                return result;
            } else {
                // 如果修复失败，进行文本解析
                return parseTextJudgeResult(judgeResponse);
            }
        } catch (Exception e) {
            log.warn("解析裁判结果JSON失败，尝试文本解析: {}", e.getMessage());
            
            // 如果JSON解析失败，进行文本解析
            return parseTextJudgeResult(judgeResponse);
        }
    }

    /**
     * 文本解析裁判结果
     */
    private JudgeResult parseTextJudgeResult(String judgeResponse) {
        JudgeResult result = new JudgeResult();
        
        // 简单的文本解析逻辑
        String lowerResponse = judgeResponse.toLowerCase();
        
        // 根据关键词判断是否正确
        boolean isCorrect = !lowerResponse.contains("错误") && 
                           !lowerResponse.contains("不正确") && 
                           !lowerResponse.contains("有误");
        
        result.setIsCorrect(isCorrect);
        result.setOverallScore(isCorrect ? 85 : 65);
        result.setSuggestions(judgeResponse);
        result.setDetailedAnalysis(judgeResponse);
        
        return result;
    }

    /**
     * 裁判结果类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class JudgeResult {
        private Boolean isCorrect;
        private Integer overallScore;
        private String suggestions;
        private String detailedAnalysis;
    }

    /**
     * 优化上下文类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class JudgeOptimizeContext {
        private Map<String, ParagraphTagResult.TagResult> bestResult;
        private int currentScore;
        private String lastSuggestions;
    }
} 