package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileAdviceMqModel;
import com.facishare.crm.task.sfa.util.SFAConfigUtil;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;

/**
 * 特征值计算消息生产者
 */
@Slf4j
@Component
public class ProfileAdviceProducer {

    private static final String MQ_PRODUCER_CONFIG_NAME = "fs-crm-task-sfa-mq.ini";
    private static final String MQ_PRODUCER_CONFIG_SECTION_NAME = ProfileAdviceMqModel.MQ_PRODUCER_NAME;

    private AutoConfMQProducer producer;

    @PostConstruct
    public void init() {
        producer = new AutoConfMQProducer(MQ_PRODUCER_CONFIG_NAME, MQ_PRODUCER_CONFIG_SECTION_NAME);
    }

    @PreDestroy
    public void destroy() {
        if (producer != null) {
            producer.close();
        }
    }

    /**
     * 发送特征计算消息
     *
     * @param message 特征计算消息对象
     * @return 是否发送成功
     */
    public boolean sendMessage(ProfileAdviceMqModel.Message message) {
        // 验证消息参数
        if (!validateMessage(message)) {
            return false;
        }
        if(!SFAConfigUtil.isOpenCustomerProfileAgent(message.getTenantId())){
            return true;
        }

        try {
            // 构建并发送消息
            Message mqMessage = buildMessage(message);
            return doSendMessage(mqMessage, message);
        } catch (Exception e) {
            log.error("特征计算消息发送异常, message: {}", message, e);
            return false;
        }
    }

    /**
     * 验证消息参数
     *
     * @param message 特征计算消息对象
     * @return 验证是否通过
     */
    private boolean validateMessage(ProfileAdviceMqModel.Message message) {
        if (message == null) {
            log.error("特征计算消息对象不能为空");
            return false;
        }
        if (StringUtils.isBlank(message.getTenantId())) {
            log.error("租户ID不能为空, message: {}", message);
            return false;
        }
        return true;
    }

    /**
     * 构建消息对象
     *
     * @param message 特征计算消息对象
     * @return 构建好的消息对象
     */
    private Message buildMessage(ProfileAdviceMqModel.Message message) {
        JSONObject messageObject = (JSONObject) JSON.toJSON(message);
        return getMessage(message.getTags(), messageObject, message.getTenantId());
    }

    /**
     * 执行消息发送
     *
     * @param mqMessage RocketMQ消息对象
     * @param originalMessage 原始特征计算消息对象（用于日志记录）
     * @return 发送是否成功
     */
    private boolean doSendMessage(Message mqMessage, ProfileAdviceMqModel.Message originalMessage) {
        SendResult sendResult = producer.send(mqMessage);
        if (sendResult != null && sendResult.getSendStatus() == SendStatus.SEND_OK) {
            log.info("特征计算消息发送成功, message: {}, msgId: {}", originalMessage, sendResult.getMsgId());
            return true;
        } else {
            log.error("特征计算消息发送失败, message: {}, result: {}", originalMessage, sendResult);
            return false;
        }
    }

    /**
     * 组装消息
     *
     * @param tags 消息标签
     * @param messageObject 消息内容对象
     * @param tenantId 租户ID
     * @return 组装好的消息对象
     */
    private Message getMessage(String tags, JSONObject messageObject, String tenantId) {
        String messageString = JSON.toJSONString(messageObject);
        Message message = new Message(producer.getDefaultTopic(), tags, messageString.getBytes(StandardCharsets.UTF_8));
        message.putUserProperty("x-fs-ei", tenantId);
        message.setTags(tags);
        return message;
    }
}