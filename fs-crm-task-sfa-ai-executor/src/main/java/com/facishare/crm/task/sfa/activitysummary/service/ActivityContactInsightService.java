package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.business.models.DescribeChangeData;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.DateUtil;
import com.facishare.crm.task.sfa.activitysummary.enums.ComprehensiveAttitudeOptionEnum;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityContactInsightModel;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.util.*;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardBatchUpdateTagAction;
import com.facishare.paas.appframework.core.predef.service.TagService;
import com.facishare.paas.appframework.core.predef.service.dto.tag.CreateTagGroup;
import com.facishare.paas.appframework.core.predef.service.dto.tag.FindDataTags;
import com.facishare.paas.appframework.core.predef.service.dto.tag.FindTagGroupByName;
import com.facishare.paas.appframework.core.predef.service.dto.tag.FindTagsByGroupId;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.activitysummary.constant.AttendeesInsightConstants.*;

@Service
@Slf4j
public class ActivityContactInsightService {

    @Autowired
    public MetaDataService metaDataService;
    @Resource
    private ActivityMongoDao activityMongoDao;
    @Autowired
    public ActivitySummaryService activitySummaryService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private TagService tagService;
    @Autowired
    private ObjectDescribeServiceImpl objectDescribeService;
    @Autowired
    @Qualifier("SFAJedisCmd")
    private MergeJedisCmd mergeJedisCmd;

    /**态度*/
    public static final String ATTITUDE="attitude";
    /**性格*/
    public static final String PERSONALITY="personality";
    /**关注点*/
    public static final String FOCUS_POINT="focus_point";
    /**会议总结*/
    public static final String MEETING_SUMMARY="meeting_summary";
    /**隐形担忧*/
    public static final String INVISIBLE_CONCERN="invisible_concern";
    /**支持*/
    public static final String ATTITUDE_SUPPORT="support";
    /**反对*/
    public static final String ATTITUDE_OPPOSE="oppose";

    public static final String ATTITUDE_TAGS_JSON="{\"api_name\":\"group_activity_contact_insight_attitude__c\",\"tag_group_name\":\"综合态度\",\"describe_api_names\":[\"ContactObj\",\"NewOpportunityContactsObj\"],\"is_applied_to_all\":false,\"group_description\":\"\",\"tag_define_type\":\"package\",\"label_names\":[{\"tag_group_api_name\":\"group_activity_contact_insight_attitude__c\",\"tag_name\":\"强支持\",\"tag_api_name\":\"label_101__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_attitude__c\",\"tag_name\":\"弱支持\",\"tag_api_name\":\"label_102__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_attitude__c\",\"tag_name\":\"中立\",\"tag_api_name\":\"label_103__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_attitude__c\",\"tag_name\":\"弱反对\",\"tag_api_name\":\"label_104__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_attitude__c\",\"tag_name\":\"强反对\",\"tag_api_name\":\"label_105__c\",\"enable\":true}]}";
    public static final String CHARACTER_TAGS_JSON="{\"api_name\":\"group_activity_contact_insight_character__c\",\"tag_group_name\":\"性格标签\",\"describe_api_names\":[\"ContactObj\",\"NewOpportunityContactsObj\"],\"is_applied_to_all\":false,\"group_description\":\"\",\"tag_define_type\":\"package\",\"label_names\":[{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"专业驱动：专家型\",\"tag_api_name\":\"label_professional_drive_expert__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"专业驱动：新手型\",\"tag_api_name\":\"label_professional_drive_newbie__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"决策风格：果断型\",\"tag_api_name\":\"label_decision_style_decisive__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"决策风格：犹豫型\",\"tag_api_name\":\"label_decision_style_hesitant__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"合作导向：任务型\",\"tag_api_name\":\"label_cooperation_orientation_task__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"合作导向：关系型\",\"tag_api_name\":\"label_cooperation_orientation_relation__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"沟通偏好：直接型\",\"tag_api_name\":\"label_communication_preference_direct__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"沟通偏好：间接型\",\"tag_api_name\":\"label_communication_preference_indirect__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"风险偏好：风险偏好型\",\"tag_api_name\":\"label_risk_preference_preference__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"风险偏好：风险回避型\",\"tag_api_name\":\"label_risk_preference_avoidance__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"驱动模式：感性型\",\"tag_api_name\":\"label_motivation_pattern_sensitive__c\",\"enable\":true},{\"tag_group_api_name\":\"group_activity_contact_insight_character__c\",\"tag_name\":\"驱动模式：理想型\",\"tag_api_name\":\"label_motivation_pattern_rational__c\",\"enable\":true}]}";

    public void execute(ActivityContactInsightModel.Arg arg){
        TraceContext.get().setTraceId("ActivityContactInsight-"+UUID.randomUUID()+"-"+arg.getActiveRecordId());
        log.warn("ActivityContactInsightService execute begin activeRecordId:{}",arg.getActiveRecordId());
        if(!SFAConfigUtil.isOpenCustomerProfileAgent(arg.getTenantId())){
            return;
        }
        User user = new User(arg.getTenantId(), CommonConstant.SUPER_USER);
        IObjectData activityRecode = metaDataService.findObjectData(user,arg.getActiveRecordId(),CommonConstant.ACTIVE_RECORD_API_NAME);
        if(ObjectUtils.isEmpty(activityRecode)){
            log.warn("ActivityContactInsightService execute activeRecordData is null activeRecordId:{}",arg.getActiveRecordId());
            return;
        }
        String accountId = InheritRecordUtil.getStringValue(activityRecode,CommonConstant.ACCOUNT_ID,"");
        String newOpportunityId = InheritRecordUtil.getStringValue(activityRecode,CommonConstant.NEW_OPPORTUNITY_ID,"");
        if(ObjectUtils.isEmpty(accountId)  && ObjectUtils.isEmpty(newOpportunityId)){
            log.warn("ActivityContactInsightService  accountId && newOpportunityId is null  activeRecordId:{}",arg.getActiveRecordId());
            return;
        }
        //获取关联的发言人
        List<IObjectData> activityUserList = queryActivityUser(arg.getActiveRecordId(),user,null);
        //过滤出绑定联系的人数据
        activityUserList = activityUserList.stream().filter(activityUser->ObjectUtils.isNotEmpty(activityUser.get(CommonConstant.CONTACT_ID))).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(activityUserList)){
            log.warn("ActivityContactInsightService   activityUserList is null");
            return;
        }
        List<IObjectData> newOpportunityContactList = new ArrayList<>();
        //获取所有的商机联系人
        if(ObjectUtils.isNotEmpty(newOpportunityId)){
            newOpportunityContactList = queryNewOpportunityContactData(user,newOpportunityId);
        }
        //处理每个联系人的洞察
        for(IObjectData activityUser : activityUserList){
            String contactId = InheritRecordUtil.getStringValue(activityUser,CommonConstant.CONTACT_ID,"");
            List<IObjectData> allActivityUserList = queryActivityUser(null,user,contactId);
            IObjectData contactData = metaDataService.findObjectData(user,contactId,CommonConstant.ContactObj);
            if(ObjectUtils.isNotEmpty(accountId)){
                handleContactInsight(user,contactData,allActivityUserList);
            }
            List<IObjectData> linkOpportunityContactList = newOpportunityContactList.stream().filter(x->
                    ObjectUtils.isNotEmpty(x.get(CommonConstant.CONTACT_ID)) && contactId.equals(x.get(CommonConstant.CONTACT_ID).toString())).collect(Collectors.toList());
            if(ObjectUtils.isNotEmpty(newOpportunityId) && CollectionUtils.isNotEmpty(linkOpportunityContactList)){
                handleNewOpportunityContactInsight(user,contactData, linkOpportunityContactList,allActivityUserList);
            }
        }
    }

    /**
     * 处理联系人洞察
     * @param user
     * @param contactData
     */
    public void handleContactInsight(User user,IObjectData contactData,List<IObjectData> activityUserList){
        //获取联系人关联的所有的发言人
        SFAJedisLock lock = new SFAJedisLock(mergeJedisCmd, getRedisKey(user,contactData.getId(),null), 5*60*1000);
        boolean b = lock.tryLock();
        if (!b) {
            log.warn("ActivityContactInsightService handleContactInsight tryLock b:{},tenantId:{},contactId:{}",b,user.getTenantId(),contactData.getId());
            return;
        }
        try {
            List<IObjectData> allActivityUserList = filterActivityUser(user,activityUserList,CommonConstant.ACCOUNT_ID,null);
            handleInsightData(user,contactData,null,allActivityUserList);
        }catch (Exception e){
            log.error("ActivityContactInsightService handleContactInsight handleInsightData error ",e);
        } finally {
            lock.unlock();
        }

    }
    /**
     * 处理商机联系人洞察
     * @param user
     * @param contactData
     */
    public void handleNewOpportunityContactInsight(User user,IObjectData contactData,List<IObjectData> linkOpportunityContactList,List<IObjectData> activityUserList){
        //获取联系人关联的所有的发言人
        List<IObjectData> allActivityUserList = filterActivityUser(user,activityUserList,CommonConstant.NEW_OPPORTUNITY_ID,linkOpportunityContactList.get(0).get(CommonConstant.NEW_OPPORTUNITY_ID).toString());
        for(IObjectData opportunityContact : linkOpportunityContactList){
            SFAJedisLock lock = new SFAJedisLock(mergeJedisCmd, getRedisKey(user,contactData.getId(),opportunityContact.getId()), 5*60*1000);
            boolean b = lock.tryLock();
            if (!b) {
                log.warn("ActivityContactInsightService handleNewOpportunityContactInsight tryLock b:{},tenantId:{},contactId:{},opportunityContactId:{}",b,user.getTenantId(),contactData.getId(),opportunityContact.getId());
                return;
            }
            try {
                handleInsightData(user,contactData,opportunityContact.getId(),allActivityUserList);
            }catch (Exception e){
                log.error("ActivityContactInsightService handleNewOpportunityContactInsight handleInsightData error ",e);
            } finally {
                lock.unlock();
            }
        }


    }

    /**
     * 过滤出客户或者商机的关联的参会人
     * @param user
     * @param activityUserList
     * @param fieldName
     * @param fieldValue
     * @return
     */
    public List<IObjectData> filterActivityUser(User user,List<IObjectData> activityUserList,String fieldName,String fieldValue){
        if(activityUserList.size()==1){
            return activityUserList;
        }
        //有多条需要过滤出属于客户的数据
        List<String> activeRecordId =  activityUserList.stream().map(x->x.get(CommonConstant.ACTIVE_RECORD_ID,String.class)).collect(Collectors.toList());
        List<IObjectData> allActiveRecords = queryActiveRecord(activeRecordId,user);
        //过滤出属于客户的销售记录
        if(ObjectUtils.isNotEmpty(fieldValue)){
            allActiveRecords = allActiveRecords.stream().filter(x->ObjectUtils.isNotEmpty(x.get(fieldName)) && fieldValue.equals(x.get(fieldName).toString())).collect(Collectors.toList());
        }else{
            allActiveRecords = allActiveRecords.stream().filter(x->ObjectUtils.isNotEmpty(x.get(fieldName))).collect(Collectors.toList());
        }
        allActiveRecords = allActiveRecords.stream().filter(x->ObjectUtils.isNotEmpty(x.get(fieldName))).collect(Collectors.toList());
        List<String> allActiveRecordIds =  allActiveRecords.stream().map(IObjectData::getId).collect(Collectors.toList());
        return activityUserList.stream().filter(x->allActiveRecordIds.contains(x.get(CommonConstant.ACTIVE_RECORD_ID,String.class))).collect(Collectors.toList());
    }
    /**
     * 处理洞察数据
     */
    public void handleInsightData(User user,IObjectData contactData,String newOpportunityContactId,List<IObjectData> allActivityUserList){
        if(CollectionUtil.isEmpty(allActivityUserList)){
            log.warn("ActivityContactInsightService  handleInsightData  allActivityUserList is null");
            return ;
        }
        StopWatch stopWatch1 = StopWatch.create("handleInsightData");
        List<String> activityUserIds =  allActivityUserList.stream().map(IObjectData::getId).collect(Collectors.toList());
        //获取全部的参会人洞察记录
        List<IObjectData> attendeeInsightRecordList = queryAttendeeInsightRecord(activityUserIds,user);
        if(CollectionUtil.isEmpty(attendeeInsightRecordList)){
            log.warn("ActivityContactInsightService  handleInsightData  attendeeInsightRecordList is null");
            return ;
        }
        stopWatch1.lap("attendeeInsightRecordList");
        //获取洞察数据是更新还是新建
         IObjectData insightData = buildInsightDataEntity(user,contactData,newOpportunityContactId);

        List<String> needUpdFiledList = new ArrayList<>();
        stopWatch1.lap("queryInsightData");
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //过滤出性格相关得数据
            List<IObjectData> characterDataList = attendeeInsightRecordList.stream().filter(x->PERSONALITY.equals(x.get(CommonConstant.INSIGHT_TYPE,String.class))).collect(Collectors.toList());
            handleCharacterAnalysis(user,insightData,contactData,characterDataList,needUpdFiledList);
            stopWatch1.lap("handleCharacterAnalysis");
        });
        parallelTask.submit(() -> {
            //过滤出关注点相关得数据
            List<IObjectData> concernDataList = attendeeInsightRecordList.stream().filter(x->FOCUS_POINT.equals(x.get(CommonConstant.INSIGHT_TYPE,String.class))).collect(Collectors.toList());
            handleConcernAnalysis(user,insightData,contactData,concernDataList,needUpdFiledList);
            stopWatch1.lap("handleConcernAnalysis");
        });
        parallelTask.submit(() -> {
            //过滤出会议总结相关得数据
            List<IObjectData> meetingSummaryList = attendeeInsightRecordList.stream().filter(x->MEETING_SUMMARY.equals(x.get(CommonConstant.INSIGHT_TYPE,String.class))).collect(Collectors.toList());
            handleMeetingSummaryAnalysis(user,insightData,contactData,meetingSummaryList,needUpdFiledList);
            stopWatch1.lap("handleMeetingSummaryAnalysis");
        });
        parallelTask.submit(() -> {
            //过滤出隐形担忧相关的数据
            List<IObjectData> invisibleConcernsList = attendeeInsightRecordList.stream().filter(x->INVISIBLE_CONCERN.equals(x.get(CommonConstant.INSIGHT_TYPE,String.class))).collect(Collectors.toList());
            handleInvisibleConcernsAnalysis(user,insightData,contactData,invisibleConcernsList,needUpdFiledList);
            stopWatch1.lap("handleInvisibleConcernsAnalysis");
        });
        try {
            parallelTask.await(600, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("ActivityContactInsightService execute error e:", e);
        }
        //过滤态度的数据
        List<IObjectData> attitudeList = attendeeInsightRecordList.stream().filter(x->ATTITUDE.equals(x.get(CommonConstant.INSIGHT_TYPE,String.class))).collect(Collectors.toList());
        handleAttitudeAnalysis(user,insightData,contactData,attitudeList,needUpdFiledList);
        stopWatch1.lap("handleAttitudeAnalysis");
        queryInsightData(user,contactData,newOpportunityContactId,insightData);
        if(ObjectUtils.isNotEmpty(insightData.getId())){
            serviceFacade.batchUpdateWithData(user,Lists.newArrayList(insightData),needUpdFiledList);
        }else{
            serviceFacade.bulkSaveObjectData(Lists.newArrayList(insightData),user);
        }
        handleUpdateFiled(user,insightData,contactData,newOpportunityContactId);
        stopWatch1.lap("handleUpdateFiled");
        stopWatch1.logSlow(1000);
    }


    /**
     * 更新联系人||商机联系人的字段或标签
     * @param user
     * @param insightData
     * @param contactData
     * @param newOpportunityContactId
     */
    public void handleUpdateFiled(User user,IObjectData insightData,IObjectData contactData,String newOpportunityContactId){
        boolean attitudeUpdField = InheritRecordUtil.getBooleanValue(insightData,CommonConstant.ATTITUDE_UPD_FIELD,false);
        String comprehensiveAttitude = InheritRecordUtil.getStringValue(insightData,CommonConstant.COMPREHENSIVE_ATTITUDE,"");
        if(attitudeUpdField){
            IObjectDescribe describe = null;
            IFieldDescribe fieldDescribe = null;
            if(ObjectUtils.isNotEmpty(newOpportunityContactId)){
                describe = AccountPathUtil.getObjectDescribe(user, CommonConstant.NEW_OPPORTUNITY_CONTACTS_API_NAME);
                fieldDescribe = describe.getFieldDescribe(CommonConstant.POSITION);
            }else{
                describe = AccountPathUtil.getObjectDescribe(user, CommonConstant.ContactObj);
                fieldDescribe = describe.getFieldDescribe(CommonConstant.CONTACT_OUR_STRENGTH);
            }
            String needUpdValue = comprehensiveAttitude;
            if(ComprehensiveAttitudeOptionEnum.NEUTRALITY.getCode().equals(needUpdValue)){
                //如果是中立，联系人和商机联系人字段中本身就有中立
                List<Map> options = (List<Map>) fieldDescribe.get("options");
                for(Map map : options){
                    String label = (String) map.get("label");
                    if("中立".equals(label)){
                        needUpdValue = (String) map.get("value");
                        break;
                    }
                }
            }
            if(ObjectUtils.isNotEmpty(newOpportunityContactId)){
                IObjectData iObjectData = serviceFacade.findObjectData(user,newOpportunityContactId,CommonConstant.NEW_OPPORTUNITY_CONTACTS_API_NAME);
                IObjectData copyData = ObjectDataExt.of(iObjectData).copy();
                iObjectData.set(CommonConstant.POSITION,needUpdValue);
                serviceFacade.batchUpdateWithData(user,Lists.newArrayList(iObjectData),Lists.newArrayList(CommonConstant.POSITION));
                Map<String, Object> diffMap = Maps.newHashMap();
                diffMap.put(CommonConstant.POSITION, needUpdValue);
                serviceFacade.log(user, EventType.MODIFY, ActionType.Modify, describe, iObjectData, diffMap, copyData);

            }else{
                IObjectData copyData = ObjectDataExt.of(contactData).copy();
                contactData.set(CommonConstant.CONTACT_OUR_STRENGTH,needUpdValue);
                serviceFacade.batchUpdateWithData(user,Lists.newArrayList(contactData),Lists.newArrayList(CommonConstant.CONTACT_OUR_STRENGTH));
                Map<String, Object> diffMap = Maps.newHashMap();
                diffMap.put(CommonConstant.CONTACT_OUR_STRENGTH, needUpdValue);
                serviceFacade.log(user, EventType.MODIFY, ActionType.Modify, describe, contactData, diffMap, copyData);
            }
        }
        boolean attitudeUpdTag = InheritRecordUtil.getBooleanValue(insightData,CommonConstant.ATTITUDE_UPD_TAG,false);
        String objectApiName = CommonConstant.CONTACT_API_NAME;
        String dataId = contactData.getId();
        if(ObjectUtils.isNotEmpty(newOpportunityContactId)){
            objectApiName = CommonConstant.NEW_OPPORTUNITY_CONTACTS_API_NAME;
            dataId = newOpportunityContactId;
        }
        RequestContext requestContext = RequestContext.builder()
                .user(user)
                .tenantId(user.getTenantId())
                .build();
        ServiceContext serviceContext = new ServiceContext(requestContext, "fs-crm-task-sfa-ai", "handleUpdateFiled");
        //要删除的tagsid
        List<String> needDeleteTagsIds = Lists.newArrayList();
        //要增加的tagsid
        Set<String> needAddTagsIds = new HashSet<>();
        if(attitudeUpdTag){
            //comprehensiveAttitude
            String needAddTagApiName = "label_"+comprehensiveAttitude+"__c";
            FindTagGroupByName.Arg argTagGroup = new FindTagGroupByName.Arg();
            argTagGroup.setGroupApiName("group_activity_contact_insight_attitude__c");
            FindTagGroupByName.Result result = tagService.findTagGroupByName(argTagGroup,serviceContext);
            FindTagsByGroupId.Arg argTags = new FindTagsByGroupId.Arg();
            argTags.setTag_group_id(result.getTagGroupId());
            FindTagsByGroupId.Result resultTagInfo = tagService.findTagsByGroupId(argTags,serviceContext);
            for(TagDocument tagDocument : resultTagInfo.getTags()){
                if(needAddTagApiName.equals(tagDocument.get("tag_api_name").toString())){
                    needAddTagsIds.add(tagDocument.get("id").toString());
                }else{
                    needDeleteTagsIds.add(tagDocument.get("id").toString());
                }
            }

        }
        boolean characterUpdTag = InheritRecordUtil.getBooleanValue(insightData,CommonConstant.CHARACTER_UPD_TAG,false);
        if(characterUpdTag && ObjectUtils.isNotEmpty(insightData.get(CommonConstant.CHARACTER_CONTENT))){
            List<AttendeesInsightModel.Insight> insightList = (List<AttendeesInsightModel.Insight>)insightData.get(CommonConstant.CHARACTER_CONTENT);
            if(CollectionUtil.isNotEmpty(insightList)){
                List<String> needAddTagApiName = insightList.stream().map(x->"label_"+x.getInsightType()+"_"+x.getInsightText()+"__c").collect(Collectors.toList());
                FindTagGroupByName.Arg argTagGroup = new FindTagGroupByName.Arg();
                argTagGroup.setGroupApiName("group_activity_contact_insight_character__c");
                FindTagGroupByName.Result result = tagService.findTagGroupByName(argTagGroup,serviceContext);
                FindTagsByGroupId.Arg argTags = new FindTagsByGroupId.Arg();
                argTags.setTag_group_id(result.getTagGroupId());
                FindTagsByGroupId.Result resultTagInfo = tagService.findTagsByGroupId(argTags,serviceContext);
                for(TagDocument tagDocument : resultTagInfo.getTags()){
                    if(needAddTagApiName.contains(tagDocument.get("tag_api_name").toString())){
                        needAddTagsIds.add(tagDocument.get("id").toString());
                    }else{
                        needDeleteTagsIds.add(tagDocument.get("id").toString());
                    }
                }
            }

        }
        if(CollectionUtil.isNotEmpty(needAddTagsIds)){
            FindDataTags.Arg arg =  new FindDataTags.Arg();
            arg.setDataId(dataId);
            arg.setDescribeApiName(objectApiName);
            FindDataTags.Result resultData = tagService.findDataTags(arg,serviceContext);
            if(ObjectUtils.isNotEmpty(resultData) && CollectionUtils.isNotEmpty(resultData.getGroups())){
                resultData.getGroups().stream().forEach(group -> {
                    group.getTags().stream().forEach(tag -> {
                        if(!needDeleteTagsIds.contains(tag.getTagId())){
                            needAddTagsIds.add(tag.getTagId());
                        }
                    });
                });
            }
            StandardBatchUpdateTagAction.Arg saveArg = new StandardBatchUpdateTagAction.Arg();
            saveArg.setDataId(dataId);
            saveArg.setAppend(false);
            saveArg.setTagIds(Lists.newArrayList(needAddTagsIds));
            ActionContext actionContext = new ActionContext(requestContext, objectApiName,
                    "BatchUpdateTag");
            serviceFacade.triggerAction(actionContext, saveArg,
                    StandardBatchUpdateTagAction.Result.class);
        }

    }


    /**
     * 处理态度分析
     * @param user
     * @param insightData
     * @param contactData
     * @param attitudeList
     * @param needUpdFiledList
     */
    public void handleAttitudeAnalysis(User user,IObjectData insightData,IObjectData contactData,List<IObjectData> attitudeList,List<String> needUpdFiledList) {
        if(CollectionUtil.isEmpty(attitudeList)){
            log.warn("ActivityContactInsightService  handleAttitudeAnalysis  attitudeList is null");
            return;
        }
        attitudeList = attitudeList.stream().sorted(Comparator.comparing(IObjectData::getCreateTime).reversed()).collect(Collectors.toList());
        //获取最近的第一条,赋值当前态度
        insightData.set(CommonConstant.CURRENT_ATTITUDE, attitudeList.get(0).get(CommonConstant.ATTITUDE));
        needUpdFiledList.add(CommonConstant.CURRENT_ATTITUDE);
        //处理最近五条态度
        List<String> lastFiveList= new ArrayList<>();
        for(int i=0;i<attitudeList.size() && i<5;i++){
            lastFiveList.add(attitudeList.get(i).getId());
        }
        insightData.set(CommonConstant.ATTENDEEINSIGHT_RECORD_ID,lastFiveList);
        needUpdFiledList.add(CommonConstant.ATTENDEEINSIGHT_RECORD_ID);
        //判断综合态度是否需要计算，默认是计算的
        boolean comprehensiveAttitudeIsAiUpd = InheritRecordUtil.getBooleanValue(insightData,CommonConstant.COMPREHENSIVE_ATTITUDE_IS_AI_UPD,true);
        if(!comprehensiveAttitudeIsAiUpd){
            log.warn("ActivityContactInsightService  handleAttitudeAnalysis  comprehensiveAttitudeIsAiUpd:{}",comprehensiveAttitudeIsAiUpd);
            return;
        }
        insightData.set(CommonConstant.COMPREHENSIVE_ATTITUDE,handleComprehensiveAttitudeCount(attitudeList));
        needUpdFiledList.add(CommonConstant.COMPREHENSIVE_ATTITUDE);

    }

    /**
     * 综合态度的计算
     * @param attitudeList
     * @return
     */
    public String handleComprehensiveAttitudeCount(List<IObjectData> attitudeList){
        long supportNum = attitudeList.stream().filter(x -> ATTITUDE_SUPPORT.equals(x.get(CommonConstant.ATTITUDE, String.class))).count();
        long opposeNum = attitudeList.stream().filter(x -> ATTITUDE_OPPOSE.equals(x.get(CommonConstant.ATTITUDE, String.class))).count();
        //计算支持，反对率
        long supportRating = (supportNum*100)/(attitudeList.size());
        long opposeRating = (opposeNum*100)/(attitudeList.size());
        log.warn("ActivityContactInsightService  handleAttitudeAnalysis supportRating:{},opposeRating:{} ,attitudeList.size:{}",supportRating,opposeRating,attitudeList.size());
        String comprehensiveAttitudeValue = "";
        if(attitudeList.size()>=3 && supportRating>=50 && opposeRating<=10 && JudgingLastThreeRecords(attitudeList,ATTITUDE_OPPOSE)){
            //强支持  判断
            comprehensiveAttitudeValue = ComprehensiveAttitudeOptionEnum.STRONG_SUPPORT.getCode();
        }else if((supportRating>=30 && supportRating<50 && opposeRating<=20 ) ||
                (supportRating>=30 && supportRating<50 && supportRating > opposeRating+15) ||
                (opposeRating<=20 && supportRating > opposeRating+15)){
                //弱支持  判断
            comprehensiveAttitudeValue = ComprehensiveAttitudeOptionEnum.WEAK_SUPPORT.getCode();
        }else if((opposeRating>=30 && opposeRating < 50 && supportRating<= 20) ||
                (opposeRating>=30 && opposeRating < 50 && opposeRating > supportRating+15 ) ||
                (supportRating<= 20 && opposeRating > supportRating+15)){
            //弱反对  判断
            comprehensiveAttitudeValue = ComprehensiveAttitudeOptionEnum.WEAK_OPPOSITION.getCode();
        }else if(attitudeList.size()>=3 && opposeRating>= 50 &&  supportRating<10 && JudgingLastThreeRecords(attitudeList,ATTITUDE_SUPPORT)){
            //强反对  判断
            comprehensiveAttitudeValue = ComprehensiveAttitudeOptionEnum.STRONGLY_OPPOSE.getCode();
        }else {
            //中立  判断
            comprehensiveAttitudeValue = ComprehensiveAttitudeOptionEnum.NEUTRALITY.getCode();
        }
        if(ComprehensiveAttitudeOptionEnum.STRONG_SUPPORT.getCode().equals(comprehensiveAttitudeValue)
                || ComprehensiveAttitudeOptionEnum.NEUTRALITY.getCode().equals(comprehensiveAttitudeValue)){
            return comprehensiveAttitudeValue;
        }
        if(attitudeList.size()<3){
            return comprehensiveAttitudeValue;
        }

        String one = attitudeList.get(0).get(CommonConstant.ATTITUDE, String.class);
        String two = attitudeList.get(1).get(CommonConstant.ATTITUDE, String.class);
        String three = attitudeList.get(2).get(CommonConstant.ATTITUDE, String.class);
        if(one.equals(two) &&  one.equals(three)){
            log.warn("ActivityContactInsightService  handleAttitudeAnalysis comprehensiveAttitudeValue:{}",comprehensiveAttitudeValue);
            return String.valueOf(Integer.parseInt(comprehensiveAttitudeValue)-1);
        }
        long absDiff = Math.abs(supportRating - opposeRating);
        if(absDiff<5){
            log.warn("ActivityContactInsightService  handleAttitudeAnalysis absDiff:{}",absDiff);
            return ComprehensiveAttitudeOptionEnum.NEUTRALITY.getCode();
        }
        return comprehensiveAttitudeValue;
    }

    /**
     * 判断最近三次记录状态
     * @return
     */
    public boolean JudgingLastThreeRecords(List<IObjectData> attitudeList ,String optionValue){
        boolean flag = true;
        for(int i=0;i<attitudeList.size() && i<3;i++){
            if(optionValue.equals(attitudeList.get(i).get(CommonConstant.ATTITUDE, String.class))){
                flag = false;
            }
        }
        return flag;
    }

        /**
         * 处理隐形担忧分析
         * @param user
         * @param insightData
         * @param contactData
         * @param invisibleConcernsList
         * @param needUpdFiledList
         */
    public void handleInvisibleConcernsAnalysis(User user,IObjectData insightData,IObjectData contactData,List<IObjectData> invisibleConcernsList,List<String> needUpdFiledList) {
        if(CollectionUtil.isEmpty(invisibleConcernsList)){
            log.warn("ActivityContactInsightService  handleInvisibleConcernsAnalysis  invisibleConcernsList is null");
            return;
        }
        invisibleConcernsList = invisibleConcernsList.stream().sorted(Comparator.comparing(IObjectData::getCreateTime).reversed()).collect(Collectors.toList());
        List<Map<String,String>> corpusList = new ArrayList<>();
        for(IObjectData x : invisibleConcernsList){

            String activeRecordId = InheritRecordUtil.getStringValue(x,CommonConstant.ACTIVE_RECORD_ID,"");
            Map<String,String> corpusMap = handleCommonCorpusAttr(activeRecordId,x,contactData.getName());
            List<AttendeesInsightModel.Insight> insightList = handleInsightResultStrTransfEntity(x);
            //整理原文得seq
            Map<Long,String> documentMap = handleQueryMongoData(user,activeRecordId,insightList);

            for(int i=0;i<insightList.size();i++){
                AttendeesInsightModel.Insight insight = insightList.get(i);
                String title = "担忧"+i;
                corpusMap.put(title,insight.getInsightText());
                if(CollectionUtil.isNotEmpty(insight.getQuotaSeqList())){
                    StringBuilder sb = new StringBuilder();
                    for(int j=0;j<insight.getQuotaSeqList().size();j++){
                        sb.append("原文").append(j+1).append(documentMap.get(Long.valueOf(insight.getQuotaSeqList().get(j)))).append(";");
                    }
                    corpusMap.put(title+"的原文",sb.toString());
                }
            }
            corpusList.add(corpusMap);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e1) {
                Thread.currentThread().interrupt();
            }
        }
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put("corpus", JSONObject.toJSONString(corpusList));
        sceneParamMap = activitySummaryService.handleAiCompleteLanguage(sceneParamMap, null, null);
        String resultAi = activitySummaryService.getAiComplete(user, "sfa_activity_contact_insight_invisible_prompt", sceneParamMap, null);
        resultAi = activitySummaryService.captureAiResult(resultAi);
        List<ActivityContactInsightModel.Meeting> result = JSONObject.parseArray(resultAi, ActivityContactInsightModel.Meeting.class);
        insightData.set(CommonConstant.INVISIBLE_CONTENT, result);
        needUpdFiledList.addAll(Lists.newArrayList(CommonConstant.INVISIBLE_CONTENT));

    }

        /**
         * 处理会议总结的ai分析
         * @param user
         * @param insightData
         * @param contactData
         * @param meetingSummaryList
         * @param needUpdFiledList
         */
    public void handleMeetingSummaryAnalysis(User user,IObjectData insightData,IObjectData contactData,List<IObjectData> meetingSummaryList,List<String> needUpdFiledList){
        if(CollectionUtil.isEmpty(meetingSummaryList)){
            log.warn("ActivityContactInsightService  handleMeetingSummaryAnalysis  meetingSummaryList is null");
            return;
        }
        meetingSummaryList = meetingSummaryList.stream().sorted(Comparator.comparing(IObjectData::getCreateTime).reversed()).collect(Collectors.toList());
        List<Map<String,String>> corpusList = new ArrayList<>();
        for(IObjectData x : meetingSummaryList){

            String activeRecordId = InheritRecordUtil.getStringValue(x,CommonConstant.ACTIVE_RECORD_ID,"");
            Map<String,String> corpusMap = handleCommonCorpusAttr(activeRecordId,x,contactData.getName());
            List<AttendeesInsightModel.Insight> insightList = handleInsightResultStrTransfEntity(x);
            //整理原文得seq
            Map<Long,String> documentMap = handleQueryMongoData(user,activeRecordId,insightList);

            for(int i=0;i<insightList.size();i++){
                AttendeesInsightModel.Insight insight = insightList.get(i);
                String title = "议题"+i;
                corpusMap.put(title,insight.getInsightText());
                if(CollectionUtil.isNotEmpty(insight.getQuotaSeqList())){
                    StringBuilder sb = new StringBuilder();
                    for(int j=0;j<insight.getQuotaSeqList().size();j++){
                        sb.append("原文").append(j+1).append(documentMap.get(Long.valueOf(insight.getQuotaSeqList().get(j)))).append(";");
                    }
                    corpusMap.put(title+"的原文",sb.toString());
                }
            }
            corpusList.add(corpusMap);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e1) {
                Thread.currentThread().interrupt();
            }
        }
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put("corpus", JSONObject.toJSONString(corpusList));
        sceneParamMap = activitySummaryService.handleAiCompleteLanguage(sceneParamMap, null, null);
        String resultAi = activitySummaryService.getAiComplete(user, "sfa_activity_contact_insight_meeting_prompt", sceneParamMap, null);
        resultAi = activitySummaryService.captureAiResult(resultAi);
        List<ActivityContactInsightModel.Meeting> result = JSONObject.parseArray(resultAi, ActivityContactInsightModel.Meeting.class);
        insightData.set(CommonConstant.MEETING_CONTENT, result);
        needUpdFiledList.addAll(Lists.newArrayList(CommonConstant.MEETING_CONTENT));
    }

    /**
     * 处理性格分析
     * @param user
     * @param insightData
     * @param contactData
     * @param characterDataList
     * @param needUpdFiledList
     */
    public void handleCharacterAnalysis(User user,IObjectData insightData,IObjectData contactData,List<IObjectData> characterDataList,List<String> needUpdFiledList){
        if(CollectionUtil.isEmpty(characterDataList)){
            log.warn("ActivityContactInsightService  handleCharacterAnalysis  characterDataList is null");
            return;
        }
        characterDataList = characterDataList.stream().sorted(Comparator.comparing(IObjectData::getCreateTime).reversed()).collect(Collectors.toList());
        List<Map<String,String>> corpusList = new ArrayList<>();
        for(IObjectData x : characterDataList){
            String activeRecordId = InheritRecordUtil.getStringValue(x,CommonConstant.ACTIVE_RECORD_ID,"");
            Map<String,String> corpusMap = handleCommonCorpusAttr(activeRecordId,x,contactData.getName());
            List<AttendeesInsightModel.Insight> insightList = handleInsightResultStrTransfEntity(x);
            //整理原文得seq
            Map<Long,String> documentMap = handleQueryMongoData(user,activeRecordId,insightList);
            List<Map<String,String>> insightMapList = new ArrayList<>();
            for(int i=0;i<insightList.size();i++){
                Map<String,String> map = new HashMap<>();
                AttendeesInsightModel.Insight insight =  insightList.get(i);
                String key ="sfa.activity.contact.insight.character."+insight.getInsightType()+"."+insight.getInsightText();
                map.put("性格名称", I18nClient.getInstance().getOrDefault(key, 0,"zh-CN",""));
                map.put("insightType",insight.getInsightType());
                map.put("insightText",insight.getInsightText());
                if(CollectionUtil.isNotEmpty(insight.getQuotaSeqList())){
                    StringBuilder sb = new StringBuilder();
                    for(int j=0;j<insight.getQuotaSeqList().size();j++){
                        sb.append("原文").append(j+1).append(documentMap.get(Long.valueOf(insight.getQuotaSeqList().get(j)))).append(";");
                    }
                    corpusMap.put("性格的原文",sb.toString());
                }
                insightMapList.add(map);
            }
            corpusMap.put("性格列表",JSONObject.toJSONString(insightMapList));
            corpusList.add(corpusMap);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e1) {
                Thread.currentThread().interrupt();
            }
        }
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put("corpus", JSONObject.toJSONString(corpusList));
        sceneParamMap = activitySummaryService.handleAiCompleteLanguage(sceneParamMap, null, null);
        String resultAi = activitySummaryService.getAiComplete(user, "sfa_activity_contact_insight_character_prompt", sceneParamMap, null);
        resultAi = activitySummaryService.captureAiResult(resultAi);
        List<AttendeesInsightModel.Insight> insightList = JSONObject.parseArray(resultAi,AttendeesInsightModel.Insight.class);
        insightData.set(CommonConstant.CHARACTER_CONTENT, insightList);
        needUpdFiledList.addAll(Lists.newArrayList(CommonConstant.CHARACTER_CONTENT));
    }

    /**
     * 设置语料的公共属性
     * @param activeRecordId
     * @param x
     * @param contactName
     * @return
     */
    public Map<String,String> handleCommonCorpusAttr(String activeRecordId,IObjectData x,String contactName){
        Map<String,String> corpusMap = new HashMap<>();
        corpusMap.put("会议时间", DateUtil.getDateString(x.getCreateTime()));
        corpusMap.put("会议ID",activeRecordId );
        corpusMap.put("联系人名称", contactName);
        return corpusMap;
    }

    /**
     * 获取参会人洞察数据
     * @param x 参会人洞察数据
     * @return
     */
    public List<AttendeesInsightModel.Insight> handleInsightResultStrTransfEntity(IObjectData x){
         return JSONObject.parseArray(x.get(CommonConstant.INSIGHT_RESULT__O).toString(),AttendeesInsightModel.Insight.class);

    }

    /**
     * 获取mongo语料中的原文
     * @param user
     * @param activeRecordId
     * @param insightList
     */
    public Map<Long,String> handleQueryMongoData(User user,String activeRecordId,List<AttendeesInsightModel.Insight> insightList){
        List<String> seqList = new ArrayList<>();
        insightList.stream().forEach(i->seqList.addAll(i.getQuotaSeqList()));
        List<InteractiveDocument> documentList = activityMongoDao.queryListByActiveRecordIdAndSeq(user.getTenantId(),activeRecordId, seqList.stream().map(s->Long.valueOf(s)).collect(Collectors.toList()));
        return documentList.stream().collect(Collectors.toMap(InteractiveDocument::getSeq,InteractiveDocument::getContent));

    }
        /**
         * 处理关注点分析
         * @param user
         * @param insightData
         * @param contactData
         * @param concernDataList
         * @param needUpdFiledList
         */
    public void handleConcernAnalysis(User user,IObjectData insightData,IObjectData contactData,List<IObjectData> concernDataList,List<String> needUpdFiledList){
        if(CollectionUtil.isEmpty(concernDataList)){
            log.warn("ActivityContactInsightService  handleConcernAnalysis  concernDataList is null");
            return;
        }
            concernDataList = concernDataList.stream().sorted(Comparator.comparing(IObjectData::getCreateTime).reversed()).collect(Collectors.toList());
            List<Map<String,String>> corpusList = new ArrayList<>();
            for(IObjectData x : concernDataList){

                String activeRecordId = InheritRecordUtil.getStringValue(x,CommonConstant.ACTIVE_RECORD_ID,"");
                Map<String,String> corpusMap = handleCommonCorpusAttr(activeRecordId,x,contactData.getName());
                List<AttendeesInsightModel.Insight> insightList = handleInsightResultStrTransfEntity(x);
                //整理原文得seq
                Map<Long,String> documentMap = handleQueryMongoData(user,activeRecordId,insightList);

                for(AttendeesInsightModel.Insight insight : insightList){
                    if (FOCUS_BUDGET.equals(insight.getInsightType())) {
                        handleConcernCorpus(corpusMap,"预算",insight,documentMap);
                    }else
                    if (FOCUS_DECISION_MAKER.equals(insight.getInsightType())) {
                        handleConcernCorpus(corpusMap,"决策人",insight,documentMap);
                    }else
                    if (FOCUS_REQUIREMENT.equals(insight.getInsightType())) {
                        handleConcernCorpus(corpusMap,"需求",insight,documentMap);
                    }else
                    if (FOCUS_TIME.equals(insight.getInsightType())) {
                        handleConcernCorpus(corpusMap,"时间",insight,documentMap);
                    }
                }
                corpusList.add(corpusMap);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e1) {
                    Thread.currentThread().interrupt();
                }
            }
            Map<String, Object> sceneParamMap = new HashMap<>();
            sceneParamMap.put("corpus", JSONObject.toJSONString(corpusList));
            sceneParamMap = activitySummaryService.handleAiCompleteLanguage(sceneParamMap, null, null);
            String resultAi = activitySummaryService.getAiComplete(user, "sfa_activity_contact_insight_concern_prompt", sceneParamMap, null);
            ActivityContactInsightModel.Concern result = JSONObject.parseObject(resultAi, ActivityContactInsightModel.Concern.class);
            insightData.set(CommonConstant.BANT_BUDGET, result.getBant_b());
            insightData.set(CommonConstant.BUDGET_ACTIVE_RECORD_ID, result.getBant_b_source());
            insightData.set(CommonConstant.BANT_AUTHORITY, result.getBant_a());
            insightData.set(CommonConstant.AUTHORITY_ACTIVE_RECORD_ID, result.getBant_a_source());
            insightData.set(CommonConstant.BANT_NEED, result.getBant_n());
            insightData.set(CommonConstant.NEED_ACTIVE_RECORD_ID, result.getBant_n_source());
            insightData.set(CommonConstant.BANT_TIME, result.getBant_t());
            insightData.set(CommonConstant.TIME_ACTIVE_RECORD_ID, result.getBant_t_source());
            needUpdFiledList.addAll(Lists.newArrayList(CommonConstant.BANT_BUDGET,CommonConstant.BUDGET_ACTIVE_RECORD_ID,CommonConstant.BANT_AUTHORITY,
                CommonConstant.AUTHORITY_ACTIVE_RECORD_ID,CommonConstant.BANT_NEED,CommonConstant.NEED_ACTIVE_RECORD_ID,CommonConstant.BANT_TIME,
                CommonConstant.TIME_ACTIVE_RECORD_ID));
    }


    /**
     * 处理关注点的语料
     * @param corpusMap 语料
     * @param concernTitle 当前关注点标题
     * @param insight      关注点数据
     * @param documentMap   原文
     */
    public void handleConcernCorpus(Map<String,String> corpusMap,String concernTitle,AttendeesInsightModel.Insight insight,Map<Long,String> documentMap){
        corpusMap.put("关注点-"+concernTitle,insight.getInsightText());
        if(CollectionUtil.isNotEmpty(insight.getQuotaSeqList())){
            StringBuilder sb = new StringBuilder();
            for(int i=0;i<insight.getQuotaSeqList().size();i++){
                sb.append("原文").append(i+1).append(documentMap.get(Long.valueOf(insight.getQuotaSeqList().get(i)))).append(";");
            }
            corpusMap.put("关注点-"+concernTitle+"的原文",sb.toString());
        }
    }



    /**
     * 获取参会人记录
     * @param activeRecordId
     * @param user
     * @param contactId
     * @return
     */
    public List<IObjectData> queryActivityUser(String activeRecordId,User user,String contactId){
        if(ObjectUtils.isEmpty(activeRecordId) &&  ObjectUtils.isEmpty(contactId)){
            return null;
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        if(ObjectUtils.isNotEmpty(activeRecordId)){
            SearchUtil.fillFilterEq(filters, CommonConstant.ACTIVE_RECORD_ID, activeRecordId);
        }
        if(ObjectUtils.isNotEmpty(contactId)){
            SearchUtil.fillFilterEq(filters, CommonConstant.CONTACT_ID, contactId);
        }
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQueryWithFieldsIgnoreAll(user, CommonConstant.ActivityUserObj, searchTemplateQuery,Lists.newArrayList(IObjectData.ID,CommonConstant.CONTACT_ID,CommonConstant.ACTIVE_RECORD_ID));
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    /**
     * 获取销售记录
     * @param activeRecordIds
     * @param user
     * @return
     */
    public List<IObjectData> queryActiveRecord(List<String> activeRecordIds,User user){
        return metaDataService.findObjectDataByIdsIgnoreAll(user.getTenantId(),activeRecordIds,CommonConstant.ACTIVE_RECORD_API_NAME);
    }

    /**
     * 获取参会人洞察记录
     * @param activityUserIds
     * @param user
     * @return
     */
    public List<IObjectData> queryAttendeeInsightRecord(List<String> activityUserIds,User user){
        if(ObjectUtils.isEmpty(activityUserIds) ){
            return null;
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, CommonConstant.ACTIVITY_USER_ID, activityUserIds);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQueryIgnoreAll(user, CommonConstant.AttendeeInsightRecordObj, searchTemplateQuery);
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    /**
     * 获取洞察数据
     * @param user
     * @param contactData
     * @param newOpportunityContactId
     */
    public void queryInsightData(User user,IObjectData contactData,String newOpportunityContactId,IObjectData insightData){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        if(ObjectUtils.isNotEmpty(newOpportunityContactId)){
            SearchUtil.fillFilterEq(filters, CommonConstant.CONTACT_ID, contactData.getId());
            SearchUtil.fillFilterEq(filters, CommonConstant.OPPORTUNITY_CONTACTS_ID, newOpportunityContactId);
        }else{
            SearchUtil.fillFilterEq(filters, CommonConstant.CONTACT_ID, contactData.getId());
            SearchUtil.fillFilterIsNull(filters, CommonConstant.OPPORTUNITY_CONTACTS_ID);
        }
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQueryIgnoreAll(user, CommonConstant.ActivityContactInsightObj, searchTemplateQuery);
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            return ;
        }
        insightData.setId(queryResult.getData().get(0).getId());
        insightData.set(CommonConstant.ATTITUDE_UPD_FIELD,queryResult.getData().get(0).get(CommonConstant.ATTITUDE_UPD_FIELD));
        insightData.set(CommonConstant.ATTITUDE_UPD_TAG,queryResult.getData().get(0).get(CommonConstant.ATTITUDE_UPD_TAG));
        insightData.set(CommonConstant.CHARACTER_UPD_TAG,queryResult.getData().get(0).get(CommonConstant.CHARACTER_UPD_TAG));
        insightData.set(CommonConstant.COMPREHENSIVE_ATTITUDE_IS_AI_UPD,queryResult.getData().get(0).get(CommonConstant.COMPREHENSIVE_ATTITUDE_IS_AI_UPD));
    }

    public IObjectData buildInsightDataEntity(User user,IObjectData contactData,String newOpportunityContactId){
        IObjectData objectData = new ObjectData();
        objectData.set("object_describe_api_name", CommonConstant.ActivityContactInsightObj);
        objectData.set(Tenantable.TENANT_ID, user.getTenantId());
        objectData.set("owner", contactData.getOwner());
        objectData.set("record_type", "default__c");
        objectData.set(CommonConstant.CONTACT_ID, contactData.getId());
        objectData.set(CommonConstant.OPPORTUNITY_CONTACTS_ID, newOpportunityContactId);
        return objectData;
    }


    /**
     * 获取商机联系人
     * @param user
     * @param newOpportunityContactId
     * @return
     */
    public List<IObjectData> queryNewOpportunityContactData(User user ,String newOpportunityContactId){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, CommonConstant.NEW_OPPORTUNITY_ID, newOpportunityContactId);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQueryIgnoreAll(user, CommonConstant.NEW_OPPORTUNITY_CONTACTS_API_NAME, searchTemplateQuery);
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            return null;
        }
        return queryResult.getData();
    }


    /**
     * 初始化标签
     * @param user
     */
    public void initTags(User user){
        log.warn("activityContactInsightService initTags begin tenantId:{} ",user.getTenantId());
        try {
            RequestContext requestContext = RequestContext.builder()
                    .user(user)
                    .tenantId(user.getTenantId())
                    .build();
            ServiceContext serviceContext = new ServiceContext(requestContext, "fs-crm-task-sfa-ai", "initTags");
            CreateTagGroup.Arg arg =  JSONObject.parseObject(ATTITUDE_TAGS_JSON, CreateTagGroup.Arg.class);
            tagService.createTagGroup(arg,serviceContext);
            arg =  JSONObject.parseObject(CHARACTER_TAGS_JSON, CreateTagGroup.Arg.class);
            tagService.createTagGroup(arg,serviceContext);
        }catch (Exception e){
            log.error("activityContactInsightService initTags error e",e);
        }

        List<String> apiNameList = Lists.newArrayList("ContactObj","NewOpportunityContactsObj");
        for(String apiName : apiNameList){
            try {
                //未个性化的描述不需要刷
                IObjectDescribe originalObjectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), apiName);
                if (!Boolean.TRUE.equals(originalObjectDescribe.isUdef())) {
                    objectDescribeService.update(originalObjectDescribe);
                }

                ClassLoader classLoader = getClass().getClassLoader();

                String changeJson = IOUtils.toString(classLoader.getResource(
                        "describeSpecialJSON/" + apiName + "_describe_change.json"), "UTF-8");
                DescribeChangeData changeModel = JSON.parseObject(changeJson, DescribeChangeData.class);

                List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();

                if (!CollectionUtils.isEmpty(changeModel.getUpdate_fields())) {
                    for (Map<String, Object> updateField : changeModel.getUpdate_fields()) {
                        String fieldName = updateField.get("api_name").toString();
                        IFieldDescribe originalField = originalObjectDescribe.getFieldDescribe(fieldName);
                        if (originalField == null) {
                            log.info(String.format("tenantid:%s apiname:%s field_name:%s not find!", user.getTenantId(), apiName, fieldName));
                            continue;
                        }
                        for (String key : updateField.keySet()) {
                            if ("api_name".equals(key)) {
                                continue;
                            }
                            if ("options".equals(key)) {
                                List<Map<String, Object>> originalOptions = (List<Map<String, Object>>) originalField.get("options");
                                List<Map<String, Object>> newOptions = (List<Map<String, Object>>) updateField.get("options");
                                List<Map<String, Object>> toAddOptions = newOptions.stream().filter(x -> x.get("op_type") != null
                                        && x.get("op_type").equals("add")).collect(Collectors.toList());
                                List<Map<String, Object>> toUpdateOptions = newOptions.stream().filter(x -> x.get("op_type") != null
                                        && x.get("op_type").equals("update")).collect(Collectors.toList());
                                if (!org.apache.commons.collections.CollectionUtils.isEmpty(toAddOptions)) {
                                    List<String> oldValueList = originalOptions.stream().map(e -> e.get("value").toString())
                                            .collect(Collectors.toList());
                                    List<String> newValueList = toAddOptions.stream().map(e -> e.get("value").toString())
                                            .collect(Collectors.toList());
                                    if (org.apache.commons.collections.CollectionUtils.containsAny(oldValueList, newValueList)) {
                                        log.warn("存在重复的optionValue，oldValueList:{}, newValueList:{}", oldValueList, newValueList);
                                        continue;
                                    }
                                    originalOptions.addAll(toAddOptions);
                                }
                                if (!org.apache.commons.collections.CollectionUtils.isEmpty(toUpdateOptions)) {
                                    for (Map<String, Object> updateOption : toUpdateOptions) {
                                        if (updateOption.get("value") != null) {
                                            String optionValue = updateOption.get("value").toString();
                                            Optional<Map<String, Object>> oldOption = originalOptions.stream().filter(x -> x.get("value").
                                                    toString().equals(optionValue)).findFirst();
                                            if (oldOption.isPresent()) {
                                                for (String optionKey : updateOption.keySet()) {
                                                    if ("value".equals(optionKey) || "op_type".equals(optionKey)) {
                                                        continue;
                                                    } else {
                                                        oldOption.get().put(optionKey, updateOption.get(optionKey));
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        fieldDescribeList.add(originalField);
                    }
                }
                if (CollectionUtils.isNotEmpty(fieldDescribeList)) {
                    objectDescribeService.updateOrInsertFieldsForOnline(user.getTenantId(), apiName, fieldDescribeList);
                }
            }catch (Exception e){
                log.error("activityContactInsightService initTags begin tenantId:{}   e:",user.getTenantId(),e);
            }

        }

    }

    public String getRedisKey(User user,String contactId , String newOpportunityContactId){
        return "ActivityContactInsightService:" + user.getTenantId()+":"+contactId+":"+newOpportunityContactId;
    }
}
