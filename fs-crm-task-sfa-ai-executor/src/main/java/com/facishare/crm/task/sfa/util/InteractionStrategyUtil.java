package com.facishare.crm.task.sfa.util;



import com.alibaba.fastjson2.JSON;
import com.facishare.crm.task.sfa.activitysummary.constant.InteractionStrategyConstants;
import com.facishare.crm.task.sfa.bizfeature.model.RuleWhere;
import com.facishare.crm.task.sfa.bizfeature.model.UseRangeInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class InteractionStrategyUtil {
    public static List<RuleWhere> getRuleWhereListByUseRange(IObjectData objectData) {
        String conditionStr = objectData.get(InteractionStrategyConstants.CONDITION, String.class);
        if (StringUtils.isBlank(conditionStr)) {
            return Lists.newArrayList();
        }
        UseRangeInfo useRangeInfo = JSON.parseObject(conditionStr, UseRangeInfo.class);
        if (!"CONDITION".equals(useRangeInfo.getType())) {
            return Lists.newArrayList();
        }
        List<RuleWhere> ruleWhere =  JSON.parseArray(useRangeInfo.getValue(), RuleWhere.class);
        for (RuleWhere where : ruleWhere) {
            for (RuleWhere.FiltersBean filter : where.getFilters()) {
                filter.setObjectApiName(objectData.get(InteractionStrategyConstants.USED_OBJECT_API_NAME, String.class));
            }
        }
        return ruleWhere;
    }
}
