package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.crm.task.sfa.services.SFALicenseService;
import com.facishare.crm.task.sfa.util.ProfileUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Slf4j
@Service
public class ProfileCommonService {

    @Resource
    private NomonProducer nomonProducer;
    @Resource
    private ServiceFacade serviceFacade;
    @Autowired
    private GDSHandler gdsHandler;
    @Autowired
    private SFALicenseService sfaLicenseService;

    private static final String PROFILE_BIZ = "feature-profile";

    private static final String AI_PROFILE_LEADS_JSON = "{\"field_section\":[],\"buttons\":[],\"related_list_name\":\"\",\"is_hidden\":false,\"nameI18nKey\":\"sfa.tab.leads.profile\",\"type\":\"sfa_ai_profile\",\"isSticky\":false,\"api_name\":\"sfa_ai_profile\",\"header\":\"线索画像\",\"grayLimit\":1,\"order\":0}";//ignoreI18n
    private static final String AI_PROFILE_ACCOUNT_JSON = "{\"field_section\":[],\"buttons\":[],\"related_list_name\":\"\",\"is_hidden\":false,\"nameI18nKey\":\"sfa.tab.account.profile\",\"type\":\"sfa_ai_profile\",\"isSticky\":false,\"api_name\":\"sfa_ai_profile\",\"header\":\"客户画像\",\"grayLimit\":1,\"order\":0}";//ignoreI18n
    private static final String AI_PROFILE_NEW_OPPORTUNITY_JSON = "{\"field_section\":[],\"buttons\":[],\"related_list_name\":\"\",\"is_hidden\":false,\"nameI18nKey\":\"sfa.tab.newopportunity.profile\",\"type\":\"sfa_ai_profile\",\"isSticky\":false,\"api_name\":\"sfa_ai_profile\",\"header\":\"商机画像\",\"grayLimit\":1,\"order\":0}";//ignoreI18n

    private static final String AI_PROFILE_API_NAME = "sfa_ai_profile";

    private static final String PROFILE_QUOTA_KEY = "sales_portrait_insight_agent_100portraits_limit";

    public void createNextTask(String tenantId) {
        Date nextExecuteTime = ProfileUtil.getNextTimestamp();
        int currentRetry = 0;
        int maxRetry = 3;
        while (currentRetry < maxRetry) {
            try {
                nomonProducer.send(NomonMessage.builder()
                        .tenantId(tenantId)
                        .biz(PROFILE_BIZ)
                        .dataId(tenantId)
                        .callArg(ProfileScoreModel.builder()
                                .tenantId(tenantId)
                                .build()
                                .toJSONString())
                        .executeTime(nextExecuteTime)
                        .build());
                break;
            } catch (Exception e) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e1) {
                    Thread.currentThread().interrupt();
                }
                currentRetry++;
                if (currentRetry == maxRetry) {
                    log.error("send profile nomon mq error, tenantId:{}", tenantId, e);
                    throw e;
                }
            }
        }
    }

    public void initProfileComponent(User user) {
        List<String> describeApiNameList = Lists.newArrayList(Utils.LEADS_API_NAME, Utils.ACCOUNT_API_NAME, Utils.NEW_OPPORTUNITY_API_NAME);
        describeApiNameList.forEach(objectApiName -> {
            ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(LayoutLogicService.LayoutContext.of(user), ILayout.DETAIL_LAYOUT_TYPE, objectApiName);
            if (layout == null) {
                log.warn("layout is null, tenantId:{} apiName:{}", user.getTenantId(), objectApiName);
                return;
            }
            RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId())
                    .appId("fs-crm-task-sfa-ai").contentType(RequestContext.ContentType.FULL_JSON)
                    .ea(gdsHandler.getEAByEI(user.getTenantId())).peerName("fs-crm-task-sfa-ai")
                    .user(user).build();
            ControllerContext controllerContext = new ControllerContext(requestContext, objectApiName, StandardController.DesignerLayout.name(), null);
            StandardDesignerLayoutController.Arg arg = new StandardDesignerLayoutController.Arg();
            arg.setDescribeApiName(objectApiName);
            arg.setIncludeDescribe(false);
            arg.setIncludeDescribeExtra(false);
            arg.setIncludeFieldsExtra(false);
            arg.setLayoutApiName(layout.getName());
            arg.setNoNeedReplaceI18n(true);
            StandardDesignerLayoutController.Result result = serviceFacade.triggerController(controllerContext, arg, StandardDesignerLayoutController.Result.class);
            if (result == null || result.getLayout() == null) {
                log.warn("designer layout is null, tenantId:{}", user.getTenantId());
            }
            ILayout newLayout = result.getLayout().toLayout();
            addProfileLayout(user.getTenantId(), objectApiName, newLayout);
            serviceFacade.getLayoutLogicService().updateLayout(user, newLayout);
        });
    }

    private void addProfileLayout(String tenantId, String objectApiName, ILayout layout) {
        try {
            List<IComponent> components = LayoutExt.of(layout).getComponentsSilently();
            if (components.isEmpty()) {
                return;
            }
            boolean hasProfileComponent = components.stream()
                    .anyMatch(component -> AI_PROFILE_API_NAME.equals(component.getName()));
            if (hasProfileComponent) {
                return;
            }
            String profileJson = getProfileJsonByObjectType(objectApiName);
            if (profileJson != null) {
                IComponent profileComponent = ComponentFactory.newInstance(profileJson);
                WebDetailLayout.of(layout).addComponents(Lists.newArrayList(profileComponent), 0);
            }
        } catch (Exception e) {
            log.error("addProfileLayout error tenantId {}", tenantId, e);
        }
    }

    private String getProfileJsonByObjectType(String objectApiName) {
        if (Utils.LEADS_API_NAME.equals(objectApiName)) {
            return AI_PROFILE_LEADS_JSON;
        } else if (Utils.ACCOUNT_API_NAME.equals(objectApiName)) {
            return AI_PROFILE_ACCOUNT_JSON;
        } else if (Utils.NEW_OPPORTUNITY_API_NAME.equals(objectApiName)) {
            return AI_PROFILE_NEW_OPPORTUNITY_JSON;
        }
        return null;
    }

    public int getProfileCount(String tenantId) {
        return sfaLicenseService.getQuotaByModule(tenantId, PROFILE_QUOTA_KEY);
    }

    public int getUsedProfileCount(String tenantId) {
        SearchTemplateQueryPlus searchTemplateQuery = SearchUtil.buildBaseSearchQuery();
        searchTemplateQuery.setLimit(1);
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, DBRecord.IS_DELETED, "0");
        Integer count = serviceFacade.countObjectDataFromDB(tenantId, FeatureConstants.SALES_COACH_RECORD, searchTemplateQuery);
        return count == null ? 0 : count;
    }
}
