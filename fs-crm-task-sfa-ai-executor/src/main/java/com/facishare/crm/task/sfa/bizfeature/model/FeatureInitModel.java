package com.facishare.crm.task.sfa.bizfeature.model;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FeatureInitModel {

    String tenantId;

    String receiverId;

    public String toJSONString() {
        return JSON.toJSONString(this);
    }
}
