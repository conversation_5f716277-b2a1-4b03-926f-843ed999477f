package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.model.RequirementModel;
import com.facishare.crm.task.sfa.activitysummary.service.RequirementService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@Slf4j
public class ActivityRequirementListener implements ApplicationListener<ContextRefreshedEvent> {


    private AutoConfMQPushConsumer consumer;

    @Autowired
    private RequirementService requirementService;

    /**
     * MQ 配置的 consumer section
     * @return
     */
    public String getSection(){
        return "sfa-ai-activity-requirement-topics";
    }

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", getSection(), (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                switch (msg.getTags()) {
                    case "activity-to-text":
                            ActivityMessage msgEntity = JSON.parseObject(msg.getBody(), ActivityMessage.class);
                            switch (msgEntity.getStage()) {
                                case "realtime2text":  // 实时音频转文字
                                    break;
                                case "AddNoAttachment":  // 无附件新增
                                case "file2text": // 附件转文本
                                case "realtime2textDone":// 实时音频转文字(全部结束时)
                                    requirementService.execute(msgEntity);
                                    break;
                            }
                        break;
                    case "sfa-requirement-batch-add":
                            RequirementModel.RequirementBatchMsg msgEntity1 = JSON.parseObject(msg.getBody(), RequirementModel.RequirementBatchMsg.class);
                            requirementService.executeBatchAddRequirement(msgEntity1);
                        break;
                    default:
                        break;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}
