package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl;

import com.facishare.crm.sfa.lto.activity.enums.AttendeesInsightType;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class QuestionAnswerInsightHandler extends AbstractInsightHandler<AttendeesInsightModel.InsightResult> {
    private static final String PROMPT = "prompt_attendee_insight_question_answer";


    @Override
    public String getInsightType() {
        return AttendeesInsightType.QUESTION_ANSWER_PERFORMANCE;
    }

    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        List<IObjectData> questionList = attendeesInsightMessage.getExtendData().getQuestionList();
        if (Safes.isEmpty(questionList)) {
            log.warn("No question list found for activeRecordId: {}", attendeesInsightMessage.getActiveRecordId());
            return;
        }
        super.insightByQuestionCorpus(attendeesInsightMessage, PROMPT);
    }

    @Override
    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return super.getOurSideNames(insightMessage);
    }

    @Override
    protected String getCorpus(AttendeesInsightModel.AttendeesInsightExtendData extendData) {
        return super.getQuestionCorpus(extendData);
    }
}
