package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityNewBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Component
@Slf4j
public class ActivityNewBusinessListener extends AbstractActivityCommonListener {

    @Resource
    private ActivityNewBusinessService activityNewBusinessService;

    @Override
    String getSection() {
        return "sfa-ai-activity-NewBusiness";
    }

    @Override
    void consume(ActivityMessage activityMessage) {
        String stage = activityMessage.getStage();
        if (ObjectUtils.isEmpty(stage)) {
            return;
        }
        
        log.info("ActivityNewBusinessListener activityMessage:{}", JSONObject.toJSONString(activityMessage));
        switch (stage) {
            case "AddNoAttachment":  // 无附件新增
                activityNewBusinessService.handleNewBusiness(activityMessage,false);
                break;
            case "file2text":  // 附件转文本
                activityNewBusinessService.handleNewBusiness(activityMessage,false);
                break;
            case "realtime2text":  // 实时音频转文字
                break;
            case "realtime2textDone":// 实时音频转文字(全部结束时)
                activityNewBusinessService.handleNewBusiness(activityMessage,true);
                break;
        }
    }
}
