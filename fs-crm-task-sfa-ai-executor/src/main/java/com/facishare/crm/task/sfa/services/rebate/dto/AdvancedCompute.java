package com.facishare.crm.task.sfa.services.rebate.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AdvancedCompute {

    @Data
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends BaseEngine.Arg {
        private List<String> macroGroupApiNames;
        private Map<String,Object> data;
        private Map<String, List<Map<String,Object>>> apiNameDetailDataList;

    }

    @Data
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Result extends BaseEngine.Result<MatchedRule> {

    }

    @Data
    class MatchedRule{
        /**
         * key:macroGroupApiName,value:ruleCodes
         */
        private Map<String,List<String>> macroGroupApiNameRuleCodesMap;
    }
}
