package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface InteractionModel {

    @Builder
    @Data
    class Arg{
        private String tenantId;
        private String activeRecordId;
        private String op;
        private String userId;
        private boolean realTimeFlag;
        private String linkAccountFieldApiName;
        private String linkAccountDataId;
        private String linkOpportunityFieldApiName;
        private String linkOpportunityDataId;
        private String linkLeadsFieldApiName;
        private String linkLeadsDataId;
        private boolean realTimeLastTimeFlag;
        private String language;
    }

    @Builder
    @Data
    @AllArgsConstructor
    class GetAiSuggestionMsg{
        private String tenantId;
        private String userId;
        private List<String> questionIds;
        private String key;
        private String redisRequestId;
        private String objectApiName;
        private String objectActionCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ChangeSpeakerMsg{
        private String tenantId;
        private String objectId;
        private String objectApiName;
        private List<ChangeSpeakerDetail> details;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ChangeSpeakerDetail{
        private String docId;
        private String oldActivityUserId;
        private String targetUserApiName;
        private String targetUserId;
    }
}
