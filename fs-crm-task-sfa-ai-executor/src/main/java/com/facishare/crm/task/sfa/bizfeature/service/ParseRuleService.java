package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

public interface ParseRuleService {

    String getRuleType();

    FeatureModel.FeatureData parse(User user, IObjectData feature, IObjectData rule, IObjectData data, IObjectDescribe dataDescribe);
}
