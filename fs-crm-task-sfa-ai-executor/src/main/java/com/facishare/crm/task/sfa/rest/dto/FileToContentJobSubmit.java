package com.facishare.crm.task.sfa.rest.dto;

import lombok.*;


public interface FileToContentJobSubmit {

    @Data
    class JobSubmitArg {
        /**
         * API名称，以__c结尾，业务侧定，范围比business小
         */
        private String apiName;
        
        /**
         * 异步回调MQ tag，每个业务自行申请消费组并监听自己的Tag
         */
        private String asyncCallbackMqTag;
        
        /**
         * 业务名称，对应消耗场景
         */
        private String business;
        
        /**
         * 显示名称，对应消耗源头
         */
        private String displayName;
        
        /**
         * 企业账号
         */
        private String ea;
        
        /**
         * 员工ID，支持-10000与其他真实用户ID
         */
        private int employeeId;
        
        /**
         * 文件名，源文件名称
         */
        private String fileName;
        
        /**
         * 忽略文档内图片最小像素大小，不传默认10000
         */
        private long ignoreMinImagePixel = 10000L;
        
        /**
         * LLM增强识别开关，默认false
         */
        private boolean llmEnhancement;
        
        /**
         * LLM增强识别模型名，当前仅支持MistralOcr
         */
        private String llmEnhancementModel;
        
        /**
         * LLM增强识别系统提示词
         */
        private String llmEnhancementSystemPrompt;
        
        /**
         * LLM增强识别用户提示词
         */
        private String llmEnhancementUserPrompt;
        
        /**
         * 图片识别开关，是否识别文档内图片内容
         */
        private boolean ocr;
        
        /**
         * 视觉模型名称，不传默认doubao-1.50-vision-lite
         */
        private String ocrModel;
        
        /**
         * 系统提示词，识别文档内图片内容使用的系统提示词
         */
        private String ocrSystemPrompt;
        
        /**
         * 用户提示词，识别文档内图片内容使用的用户提示词
         */
        private String ocrUserPrompt;
        
        /**
         * 文件Path，支持N|TN|C|TC|A|TA|G 类型文件
         */
        private String path;
        
        /**
         * 安全组，网盘文件必传 如：XiaoKeNetDisk
         */
        private String securityGroup;
        
        /**
         * 源文件类型，支持 "pdf", "ppt", "pptx", "doc", "docx", "xls", "xlsx"
         */
        private String sourceType;
        
        /**
         * 目标文件类型，仅支持 md
         */
        private String targetType;
    }


    @Data
    class JobSubmitResult{
        private boolean success;
        private int code;
        private String message;
        private JobSubmitData data;
    }

    @Data
    class JobSubmitData {
        private String jobId;
        private String requestId;
    }


    @Data
    @ToString
    @EqualsAndHashCode
    @NoArgsConstructor
    @AllArgsConstructor
    public class DocParseCompletedMsg {

        /**
         * 企业账号
         */
        private String ea;

        /**
         * 任务ID
         */
        private String jobId;

        /**
         * 任务是否成功
         */
        private boolean success;

        /**
         * 任务失败错误码 4xx 客户端错误, 5xx 服务端错误
         */
        private Integer jobFailCode;

        /**
         * 任务失败信息
         */
        private String jobFailMsg;

        /**
         * trace id 用于追踪日志
         */
        private String requestId;
    }



    /*
     *  JobContentGet 返回值
     * {
  "success": true,
  "code": 200,
  "message": "success",
  "data": {
    "jobProgress": 0,
    "requestId": "fs-file-process/67b2e54f63cba8a44724f38e"
  }
}
     */

    @Data
    class JobContentArg {
        private String requestId;
    }

    @Data
    class JobContentResult {
        private boolean success;
        private int code;
        private String message;
        private JobContentData data;
    }

    @Data
    class JobContentData {
        private int jobProgress;
        private String requestId;
        private String content;
    }

     /* 
      * JobQuery 返回值
      * {
  "success": true,
  "code": 200,
  "message": "success",
  "data": {
    "jobProgress": 0,
    "requestId": "fs-file-process/67b2e54f63cba8a44724f38e"
  }
}
      */

    @Data
    class JobQueryArg {
        private String requestId;
    }

    @Data
    class JobQueryResult {
        private boolean success;
        private int code;
        private String message;
        private JobQueryData data;
    }

    @Data
    class JobQueryData {
        private int jobProgress;
        private String requestId;
    }

    /**
     * 同步文件转换请求参数
     */
    @Data
    class DocConvertArg {
        private String ea;
        private int employeeId;
        private String path;
        private String securityGroup;
        private String sourceType;
        private String targetType;
        private boolean ocr;
    }

    /**
     * 同步文件转换返回结果
     */
    @Data
    class DocConvertResult {
        private boolean success;
        private int code;
        private String message;
        private DocConvertData data;
    }

    /**
     * 同步文件转换返回数据
     */
    @Data
    class DocConvertData {
        private String content;
    }

    /**
     * GetContent API 请求参数
     */
    @Data
    class JobGetContentArg {
        /**
         * 任务ID（全局唯一）
         */
        private String jobId;
    }

    /**
     * GetContent API 返回结果
     */
    @Data
    class JobGetContentResult {
        private boolean success;
        private int code;
        private String message;
        private Object data;
    }

    /**
     * GetContent API 返回数据
     */
    @Data
    class JobGetContentData {
        private String content;
    }
}
