package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 画像
 */
public interface ProfileConstants {
    /**
     * 方法论实例
     */
    String METHODOLOGY_INSTANCE_ID = "methodology_instance_id";
    /**
     * 方法论
     */
    String METHODOLOGY_ID = "methodology_id";
    /**
     * 线索
     */
    String LEAD_ID = "lead_id";
    /**
     * 客户
     */
    String ACCOUNT_ID = "account_id";
    /**
     * 商机
     */
    String OPPORTUNITY_ID = "opportunity_id";
    /**
     * 类型
     */
    String TYPE = "type";
    /**
     * 综合分
     */
    String INTEGRATED_SCORE = "integrated_score";
    /**
     * 阶段总分
     */
    String PHASE_CUMULATIVE_SCORE = "phase_cumulative_score";
    /**
     * 计算时间
     */
    String CALC_TIME = "calc_time";
    /**
     * 是否最新画像
     */
    String IS_LATEST = "is_latest";
    /**
     * 总结
     */
    String SUMMARY = "summary";
    /**
     * 当前节点
     */
    String CURRENT_NODE_ID = "current_node_id";
    /**
     * 下一节点
     */
    String NEXT_NODE_ID = "next_node_id";
    /**
     * 当前节点得分
     */
    String CURRENT_SCORE = "current_score";
    /**
     * 趋势总结
     */
    String TREND_SUMMARY = "trend_summary";


    enum Type {
        /**
         * 客户画像
         */
        ACCOUNT("account"),
        /**
         * 线索画像
         */
        LEAD("lead"),
        /**
         * 商机画像
         */
        OPPORTUNITY("opportunity");

        private final String value;

        public String getValue() {
            return value;
        }

        Type(String value) {
            this.value = value;
        }
    }

    enum RefreshType {
        /**
         * 实时
         */
        REALTIME("realTime"),
        /**
         * 定时
         */
        TIMING("timing");

        private final String value;

        public String getValue() {
            return value;
        }

        RefreshType(String value) {
            this.value = value;
        }
    }
}