package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.EmotionAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
//@Component
public class EmotionAnalysisListener extends AbstractActivityCommonListener {

//    @Autowired
    private EmotionAnalysisService emotionAnalysisService;

    @Override
    String getSection() {
        return "activity-emotion-analysis-consumer";
    }

    @Override
    void consume(ActivityMessage activityMessage) {
//        emotionAnalysisService.generate(activityMessage);
    }
}
