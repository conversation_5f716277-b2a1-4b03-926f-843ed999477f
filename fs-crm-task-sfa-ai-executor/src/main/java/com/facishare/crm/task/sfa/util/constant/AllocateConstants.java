package com.facishare.crm.task.sfa.util.constant;

/**
 * @IgnoreI18nFile
 */
public interface AllocateConstants {


    static AllocateOperationSource getAllocateOperationSource(String value){
        AllocateOperationSource rst = null;
        switch (value){
            case "1":
                rst = AllocateOperationSource.Add;
                break;
            case "2":
                rst = AllocateOperationSource.Back;
                break;
            case "3":
                rst = AllocateOperationSource.Move;
                break;
        }
        return rst;
    }

    /**
     * 候选人类型
     */
    enum AllocateMemberType{
        /**
         * 员工
         */
        Employee("1"),
        /**
         * 部门
         */
        Circle("2"),
        /**
         * 外部企业
         */
        OutTenant("3"),
        /**
         * 外部人员
         */
        OutEmployee("4"),
        /**
         * 用户组
         */
        UserGroup("5"),
        /**
         * 用户角色
         */
        UserRole("6");

        private String value;
        AllocateMemberType(String _value){
            value = _value;
        }
        public String getValue() {
            return this.value;
        }

        public static AllocateMemberType memberTypeOf(String memberType) {
            for (AllocateMemberType value : values()) {
                if (value.getValue().equals(memberType)) {
                    return value;
                }
            }
            return null;
        }
    }
    enum AllocateOperationSource{
        Add("1","新增"),Back("2","退回"),Move("3","转移");
        private String value;
        private String text;
        AllocateOperationSource(String _value,String _text){
            value = _value;
            text = _text;
        }
        public String getText(){return this.text;}
        public String getValue() {
            return this.value;
        }
    }
    /**
     * 线索自动分配模式
     */
    enum AllocatePatternType{
        /**
         * 单人分配
         */
        SingleAllocate("1"),
        /**
         * 循环分配
         */
        CycleAllocate("2"),
        /**
         * 权重分配
         */
        WeightAllocate("3");
        private String value;
        public String getValue() {
            return this.value;
        }
        AllocatePatternType(String _value){
            value = _value;
        }
    }

    enum AllocateOperationSourceType{
        /**
         * 新增
         */
        Add("1"),
        /**
         * 退回
         */
        Back("2"),
        /**
         * 转移
         */
        Move("3");
        private String value;
        public String getValue() {
            return this.value;
        }
        AllocateOperationSourceType(String _value){
            value = _value;
        }
    }

    enum CompareType{
        NONE("0"),
        /**
         * 等于
         */
        Equal("1"),
        /**
         * 不等于
         */
        NotEqual("2"),
        /**
         * 包含
         */
        Contain("3"),
        /**
         * 不包含
         */
        NotContain("4"),
        /**
         * 早于
         */
        Early("5"),
        /**
         * 晚于
         */
        Later("6"),
        /**
         * 自定义
         */
        UserDefined("7"),
        /**
         * 大于
         */
        GreaterThan("8"),
        /**
         * 大于等于
         */
        GreaterThanOrEqual("9"),
        /**
         * 小于
         */
        LessThan("10"),
        /**
         * 小于等于
         */
        LessThanOrEquan("11"),
        /**
         * 为空
         */
        IsEmpty("12"),
        /**
         * 不为空
         */
        IsNotEmpty("13"),
        /**
         * 属于
         */
        BelongTo("14"),
        /**
         * 不属于
         */
        NotBelongTo("15");
        private String _value;CompareType(String value){
            this._value = value;
        }
        public String getValue(){
            return _value;
        }

        public static CompareType getType(String value){
            CompareType rst = NONE;
            switch (value){
                case "0":
                    rst = NONE;
                    break;
                case "1":
                    rst = Equal;
                    break;
                case "2":
                    rst = Contain;
                    break;
                case "3":
                    rst = Contain;
                    break;
                case "4":
                    rst = NotContain;
                    break;
                case "5":
                    rst = Early;
                    break;
                case "6":
                    rst = Later;
                    break;
                case "7":
                    rst = UserDefined;
                    break;
                case "8":
                    rst = GreaterThan;
                    break;
                case "9":
                    rst = GreaterThanOrEqual;
                    break;
                case "10":
                    rst = LessThan;
                    break;
                case "11":
                    rst = LessThanOrEquan;
                    break;
                case "12":
                    rst = IsEmpty;
                    break;
                case "13":
                    rst = IsNotEmpty;
                    break;
                case "14":
                    rst = BelongTo;
                    break;
                case "15":
                    rst = NotBelongTo;
                    break;
            }
            return rst;
        }
    }

    enum NewCompareType{
        NONE("0"),
        /**
         * 等于
         */
        Equal("1"),
        /**
         * 不等于
         */
        NotEqual("2"),
        /**
         * 大于
         */
        GreaterThan("3"),
        /**
         * 大于等于
         */
        GreaterThanOrEqual("4"),
        /**
         * 小于
         */
        LessThan("5"),
        /**
         * 小于等于
         */
        LessThanOrEquan("6"),
        /**
         * 包含
         */
        Contain("7"),
        /**
         * 不包含
         */
        NotContain("8"),
        /**
         * 为空
         */
        IsEmpty("9"),
        /**
         * 不为空
         */
        IsNotEmpty("10"),
        /**
         * 早于
         */
        Early("11"),
        /**
         * 晚于
         */
        Later("12"),
        /**
         * 属于
         */
        BelongTo("13"),
        /**
         * 不属于
         */
        NotBelongTo("14"),
        /**
         * 自定义
         */
        UserDefined("19");
        private String _value;
        NewCompareType(String value){
            this._value = value;
        }
        public String getValue(){
            return _value;
        }
        public static NewCompareType getType(String value){
            NewCompareType rst = NewCompareType.NONE;
            switch (value){
                case "0":
                    break;
                case "1":
                    rst = NewCompareType.Equal;
                    break;
                case "2":
                    rst = NewCompareType.NotEqual;
                    break;
                case "3":
                    rst = NewCompareType.GreaterThan;
                    break;
                case "4":
                    rst = NewCompareType.GreaterThanOrEqual;
                    break;
                case "5":
                    rst = NewCompareType.LessThan;
                    break;
                case "6":
                    rst = NewCompareType.LessThanOrEquan;
                    break;
                case "7":
                    rst = NewCompareType.Contain;
                    break;
                case "8":
                    rst = NewCompareType.NotContain;
                    break;
                case "9":
                    rst = NewCompareType.IsEmpty;
                    break;
                case "10":
                    rst = NewCompareType.IsNotEmpty;
                    break;
                case "11":
                    rst = NewCompareType.Early;
                    break;
                case "12":
                    rst = NewCompareType.Later;
                    break;
                case "13":
                    rst = NewCompareType.BelongTo;
                    break;
                case "14":
                    rst = NewCompareType.NotBelongTo;
                    break;
                case "19":
                    rst = NewCompareType.UserDefined;
                    break;
            }
            return rst;
        }
    }

    enum SFAFieldType{
        None("0"),
        /**
         * 分割线
         */
        CutoffRule("1"),
        /**
         * 单行文本
         */
        SingleLineText("2"),
        /**
         * 多行文本
         */
        MultiLineText("3"),
        /**
         * 整数
         */
        Integer("4"),
        /**
         * 小数
         */
        Decimal("5"),
        /**
         * 金额
         */
        Money("6"),
        /**
         * 日期型
         */
        Date("7"),
        /**
         * 单选
         */
        SingleSelection("8"),
        /**
         * 多选
         */
        MultiSelection("9"),
        /**
         * 图像
         */
        Image("10"),
        /**
         * 地址
         */
        Address("11"),
        /**
         * 生日
         */
        Birthday("12"),
        /**
         * 布尔型
         */
        BoolType("13"),
        /**
         * 级联单选
         */
        CascadeSSel("14"),
        /**
         * 日期时间型
         */
        DateTime("15"),
        /**
         * 查找类型
         */
        LookUp("16"),
        /**
         * 附件
         */
        Attach("17"),
        /**
         * 电话
         */
        Tel("18"),
        /**
         * 邮件
         */
        Mail("19"),
        /**
         * 计算
         */
        Calculation("21"),
        /**
         * 业务类型
         */
        RecordType("22"),
        /**
         *统计字段
         */
        Aggregate("23"),
        /**
         * 引用字段
         */
        Relation("24"),
        /**
         * 地区定位
         */
        Area("25"),
        /**
         * 国家
         */
        Country("26"),
        /**
         * 省
         */
        Province("27"),
        /**
         * 市
         */
        City("28"),
        /**
         * 区
         */
        District("29"),
        /**
         * 定位
         */
        Location("30"),
        /**
         *详细地址
         */
        DetailAddress("31");
        private String _value;SFAFieldType(String value){
            this._value = value;
        }
        public String getValue(){
            return _value;
        }
        public static SFAFieldType getType(String value){
            SFAFieldType rst = None;
            switch (value){
                case "1":
                    rst = CutoffRule;
                    break;
                case "2":
                    rst = SingleLineText;
                    break;
                case "3":
                    rst = MultiLineText;
                    break;
                case "4":
                    rst = Integer;
                    break;
                case "5":
                    rst = Decimal;
                    break;
                case "6":
                    rst = Money;
                    break;
                case "7":
                    rst = Date;
                    break;
                case "8":
                    rst = SingleSelection;
                    break;
                case "9":
                    rst = MultiSelection;
                    break;
                case "10":
                    rst = Image;
                    break;
                case "11":
                    rst = Address;
                    break;
                case "12":
                    rst = Birthday;
                    break;
                case "13":
                    rst = BoolType;
                    break;
                case "14":
                    rst = CascadeSSel;
                    break;
                case "15":
                    rst = DateTime;
                    break;
                case "16":
                    rst = LookUp;
                    break;
                case "17":
                    rst = Attach;
                    break;
                case "18":
                    rst = Tel;
                    break;
                case "19":
                    rst = Mail;
                    break;
                case "21":
                    rst = Calculation;
                    break;
                case "22":
                    rst = RecordType;
                    break;
                case "23":
                    rst = Aggregate;
                    break;
                case "24":
                    rst = Relation;
                    break;
                case "25":
                    rst = Area;
                    break;
                case "26":
                    rst = Country;
                    break;
                case "27":
                    rst = Province;
                    break;
                case "28":
                    rst = City;
                    break;
                case "29":
                    rst = District;
                    break;
                case "30":
                    rst = Location;
                    break;
                case "31":
                    rst = DetailAddress;
                    break;
            }
            return rst;
        }
    }

    class Field{
        public static final String  IS_ALL_SALES_CLUE = "is_all_sales_clue";
    }

    enum ObjectPoolLimitType {
        PERSONAL("personal","个人级"),
        ENTERPRISE("enterprise","企业级");
        private String value;
        private String label;

        ObjectPoolLimitType(String value, String label) {
            this.value = value;
            this.label = label;
        }
        public String getValue() {
            return value;
        }
    }
}
