package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.rest.dto.FileParseText;
import com.facishare.rest.core.annotation.GET;
import com.facishare.rest.core.annotation.QueryParam;
import com.facishare.rest.core.annotation.RestResource;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/16 11:15
 * @description:
 */
@RestResource(
        value = "FileParseTextResource",
        desc = "Word2Text",
        contentType = "application/json",
        codec = "com.facishare.rest.core.codec.DefaultRestCodec"
)
public interface FileParseTextProxy {

    @GET(value = "/api/parse", desc = "AI Agent execute")
    FileParseText.Result execute(@QueryParam("ea") String ea, @QueryParam("path") String path, @QueryParam("generateTxt") String generateTxt);

}
