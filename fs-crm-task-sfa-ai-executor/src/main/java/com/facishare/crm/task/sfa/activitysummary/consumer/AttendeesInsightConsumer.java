package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.AttendeesInsightService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.UUID;

@Component
@Slf4j
public class AttendeesInsightConsumer implements ApplicationListener<ContextRefreshedEvent> {
    private AutoConfMQPushConsumer consumer;

    @Autowired
    private AttendeesInsightService attendeesInsightService;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "sfa-activity-attendee-insights-consumer", (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    consumeResponse(msg);
                } catch (Exception e) {
                    log.error("AttendeesInsightConsumer error :{}", msg, e);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    private void consumeResponse(MessageExt message) {
        TraceContext.get().setTraceId("attendees-insight-" + UUID.randomUUID().toString().replace("-", ""));
        String body = new String(message.getBody());
        log.info("AttendeesInsightConsumer receive message: {}", body);
        AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage = JSON.parseObject(body, AttendeesInsightModel.AttendeesInsightMessage.class);
        attendeesInsightService.insight(attendeesInsightMessage);
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    @PreDestroy
    public void close() {
        consumer.close();
    }
}
