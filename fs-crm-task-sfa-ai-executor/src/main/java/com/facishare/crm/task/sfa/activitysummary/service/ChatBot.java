package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.OpenAIChatComplete;
import com.facishare.ai.api.model.service.Openai;
import com.facishare.crm.task.sfa.activitysummary.model.ChatHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 通用AI对话机器人类
 * 负责基础的对话历史管理和AI调用逻辑，不包含任何业务相关逻辑
 */
@Component
@Slf4j
public class ChatBot {

    @Autowired
    private Openai openai;

    private String openai_model = "Alicloud@deepseek-v3";


    /**
     * 创建新的对话历史
     */
    public ChatHistory createChatHistory() {
        return new ChatHistory();
    }

    /**
     * 使用OpenAI进行对话，带入历史上下文
     *
     * @param tenantId    租户ID
     * @param chatHistory 对话历史
     * @return 大模型响应
     */
    @SuppressWarnings("deprecation") // 使用了已废弃的chatComplete方法
    public String chat(String tenantId, ChatHistory chatHistory) {
        BaseArgument baseArgument = new BaseArgument();
        baseArgument.setTenantId(tenantId);
        baseArgument.setBusiness("sfa_ai");
        
        OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
        arg.setModel(openai_model);
        arg.setMessages(chatHistory.getMessages());
        arg.setUser_id("-10000");
        arg.setStream(Boolean.FALSE);
        
        OpenAIChatComplete.Result result = null;
        try {
            result = openai.chatComplete(baseArgument, arg);
        } catch (Exception e) {
            log.error("AI对话调用失败: {}", e.getMessage());
            return "";
        }
        
        log.info("AI对话结果: {}", result);
        return result.getMessage();
    }

    /**
     * 执行一轮完整的对话（添加消息、获取AI响应并记录）
     *
     * @param tenantId      租户ID
     * @param chatHistory   对话历史
     * @param systemPrompt  系统提示词（如果为null则不添加）
     * @param userInput     用户输入（如果为null则不添加）
     * @return AI响应
     */
    public String executeRound(String tenantId, ChatHistory chatHistory, String systemPrompt, String userInput) {
        if (systemPrompt != null) {
            chatHistory.addSystemMessage(systemPrompt);
        }
        if (userInput != null) {
            chatHistory.addUserMessage(userInput);
        }
        
        String response = chat(tenantId, chatHistory);
        chatHistory.addAssistantMessage(response);
        
        return response;
    }

    /**
     * 继续对话（只添加用户输入）
     *
     * @param tenantId    租户ID
     * @param chatHistory 对话历史
     * @param userInput   用户输入
     * @return AI响应
     */
    public String continueChat(String tenantId, ChatHistory chatHistory, String userInput) {
        return executeRound(tenantId, chatHistory, null, userInput);
    }

    /**
     * 开始新对话（只添加系统提示词）
     *
     * @param tenantId     租户ID
     * @param chatHistory  对话历史
     * @param systemPrompt 系统提示词
     * @return AI响应
     */
    public String startChat(String tenantId, ChatHistory chatHistory, String systemPrompt) {
        return executeRound(tenantId, chatHistory, systemPrompt, null);
    }

    /**
     * 设置模型名称
     */
    public void setModel(String model) {
        this.openai_model = model;
    }

    /**
     * 获取当前模型名称
     */
    public String getModel() {
        return this.openai_model;
    }
} 