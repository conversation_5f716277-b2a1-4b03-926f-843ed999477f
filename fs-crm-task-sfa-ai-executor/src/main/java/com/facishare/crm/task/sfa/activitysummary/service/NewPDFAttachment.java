package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.lto.activity.enums.TaskStatusEnum;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.service.ActivityTaskStateService;
import com.facishare.crm.task.sfa.activitysummary.enums.FileTypeEnum;
import com.facishare.crm.task.sfa.rest.FsFileToContentResource;
import com.facishare.crm.task.sfa.rest.dto.FileToContentJobSubmit;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 新版PDF解析实现
 */
@Component
@Slf4j
public class NewPDFAttachment extends AbstractFile2Text implements File2Text {

    @Resource
    private FsFileToContentResource fsFileToContentResource;


    @Resource
    private ActivityTaskStateService activityTaskStateService;

    @Override
    public boolean support(FileTypeEnum fileType) {
        return fileType == FileTypeEnum.DOC ||
                fileType == FileTypeEnum.TXT ||
                fileType == FileTypeEnum.DOCX ||
                fileType == FileTypeEnum.PPT ||
                fileType == FileTypeEnum.PPTX ||
                fileType == FileTypeEnum.PDF;
    }

    @Override
    public void execute(ActivityMessage message, IObjectData activityData) {
        submitJob(message, activityData);

    }

    /**
     * 提交任务（异步方式，保留用于兼容）
     */
    public void submitJob(ActivityMessage message, IObjectData activityData) {
        String owner = activityData.getOwner() == null ? "-10000" : activityData.getOwner().get(0);
        List<String> pathWithExt = getPathWithExt(message);
        String ea = gdsHandler.getEAByEI(message.getTenantId());
        for (String path : pathWithExt) {
            FileToContentJobSubmit.JobSubmitData submitData = submitJobAndGetContent(ea, Integer.parseInt(owner), path);
            activityTaskStateService.insOrUpdate(message.getTenantId(), "file_parse", activityData.getId(), TaskStatusEnum.ONGOING, submitData.getJobId());
            sendActivityMessage(message);
        }
    }

    /**
     * 提交文件解析任务并获取内容
     */
    private FileToContentJobSubmit.JobSubmitData submitJobAndGetContent(String ea, Integer employeeId, String path) {
        FileToContentJobSubmit.JobSubmitArg submitArg = new FileToContentJobSubmit.JobSubmitArg();
        submitArg.setEa(ea);
        submitArg.setEmployeeId(employeeId != null ? employeeId : 0);
        submitArg.setPath(path);

        // 从path中提取文件名和扩展名
        String fileName = path.substring(path.lastIndexOf("/") + 1);
        submitArg.setFileName(fileName);
        String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

        // 设置正确的参数
        submitArg.setSourceType(fileExtension);
        submitArg.setTargetType("md"); // 目标类型设置为md
        submitArg.setOcr(true); // 使用ocr而不是imageOcr
        submitArg.setLlmEnhancement(false);

        // 设置业务相关参数
        submitArg.setBusiness("sfa");
        submitArg.setApiName("sfa_activity__c");
        submitArg.setDisplayName("Activity 文件解析");
        submitArg.setAsyncCallbackMqTag("sfa_activity");

        String userInfo = "{\"userId\":" + employeeId + "}";
        FileToContentJobSubmit.JobSubmitResult submitResult = new FileToContentJobSubmit.JobSubmitResult();
        try {
            // 1. 提交任务
            submitResult = fsFileToContentResource.submitJob(ea, userInfo,
                    submitArg);
        } catch (Exception e) {
            log.error("Error in submitJobAndGetContent, path: {}", path, e);
        }

        if (submitResult == null || !submitResult.isSuccess() || submitResult.getData() == null || submitResult.getData().getRequestId() == null) {
            log.error("Failed to submit file processing job, path: {}, result: {}", path, submitResult);
            return null;
        }
        return submitResult.getData();
    }
}
