package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 节点特征关联常量
 *
 * <AUTHOR>
 */
public interface NodeFeatureConstants {
	/**
     * 节点
     */
	String NODE_ID = "node_id";
	/**
     * 特征
     */
	String FEATURE_ID = "feature_id";
	/**
     * 是否必做
     */
	String MUST_DO = "must_do";
	/**
     * 状态
     */
	String STATUS = "status";
	enum StatusType {
		/**
		 * 禁用
		 */
		DISABLED("0"),

		/**
		 * 启用
		 */
		ENABLED("1");

		private final String status;

		public String getStatusType() {
			return status;
		}

		StatusType(String status) {
			this.status = status;
		}
	}
	/**
     * 方法论
     */
	String METHODOLOGY_ID = "methodology_id";
	/**
     * 特征对象
     */
	String FEATURE_OBJECT_API_NAME = "feature_object_api_name";
	/**
     * 关联对象
     */
	String RELATED_OBJECT_API_NAME = "related_object_api_name";
	/**
     * 关联字段
     */
	String RELATED_FIELD = "related_field";
	/**
     * 负责人所在部门
     */
	String OWNER_DEPARTMENT = "owner_department";
	/**
     * 相关团队
     */
	String RELEVANT_TEAM = "relevant_team";
}