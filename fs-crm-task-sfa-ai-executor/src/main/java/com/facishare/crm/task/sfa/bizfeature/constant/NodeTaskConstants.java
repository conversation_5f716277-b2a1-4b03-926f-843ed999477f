package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 节点任务关系常量
 *
 * <AUTHOR>
 */
public interface NodeTaskConstants {
     String API_NAME = "NodeTaskObj";
	/**
     * 任务
     */
	String TASK_ID = "task_id";
	/**
     * 方法论
     */
	String METHODOLOGY_ID = "methodology_id";
	/**
     * 节点
     */
	String NODE_ID = "node_id";
	/**
     * 是否必做
     */
	String MUST_DO = "must_do";
	/**
     * 顺序
     */
	String TASK_ORDER = "task_order";
	/**
     * 任务完成阶段是否推进
     */
	String STEP_BY_STEP = "step_by_step";
	/**
     * 负责人所在部门
     */
	String OWNER_DEPARTMENT = "owner_department";
	/**
     * 相关团队
     */
	String RELEVANT_TEAM = "relevant_team";
}