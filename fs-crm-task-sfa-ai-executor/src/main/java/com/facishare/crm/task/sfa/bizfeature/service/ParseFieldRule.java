package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import com.facishare.crm.task.sfa.bizfeature.constant.ParseRuleConstants;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.HashMap;
import java.util.Map;

@Component
public class ParseFieldRule extends AbsParseRuleService {

    @Override
    public String getRuleType() {
        return ParseRuleConstants.CalcMethodType.FIELD.getCalcMethodType();
    }

    @Override
    protected FeatureModel.ParseValueData getValue(User user, IObjectData feature, IObjectData rule, IObjectData afterData, IObjectDescribe dataDescribe) {
        String[] fields = getFields(feature);
        Object value = afterData.get(fields[0]);
        FeatureModel.ParseValueData ret = new FeatureModel.ParseValueData();
        ret.setValue(value);
        Map<String,Object> srcValue =new HashMap<>();
        srcValue.put(fields[0],value);
        ret.setTriggerValue(srcValue);
        ret.setObjectApiName(afterData.getDescribeApiName());
        ret.setObjectId(afterData.getId());
        return ret;
    }
}
