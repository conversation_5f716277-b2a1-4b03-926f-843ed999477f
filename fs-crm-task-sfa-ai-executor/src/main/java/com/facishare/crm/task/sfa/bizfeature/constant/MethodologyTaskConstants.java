package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 任务常量
 *
 * <AUTHOR>
 */
public interface MethodologyTaskConstants {
	String API_NAME = "MethodologyTaskObj";
	/**
     * 顺序
     */
	String TASK_ORDER = "task_order";
	/**
     * 任务完成阶段是否推进
     */
	String STEP_BY_STEP = "step_by_step";
	/**
     * 描述
     */
	String NOTE = "note";
	/**
     * 流程节点
     */
	String NODE_ID = "node_id";
	/**
     * 是否必做
     */
	String MUST_DO = "must_do";

	/**
     * 简称
     */
	String SHORT_NAME = "short_name";

}