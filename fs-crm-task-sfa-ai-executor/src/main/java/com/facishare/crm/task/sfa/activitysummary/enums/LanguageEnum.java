package com.facishare.crm.task.sfa.activitysummary.enums;

import org.apache.commons.lang3.ObjectUtils;

public enum LanguageEnum {
    ZH_CN("zh-C<PERSON>", "简体中文"),
    ZH_TW("zh-TW", "繁体中文"),
    EN("en", "英文"),
    JA("ja", "日文"),;

    private String code;
    private String desc;

    LanguageEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getByCode(String code) {
        if(ObjectUtils.isEmpty(code)){
            return "简体中文";
        }
        for (LanguageEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return "简体中文";
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
