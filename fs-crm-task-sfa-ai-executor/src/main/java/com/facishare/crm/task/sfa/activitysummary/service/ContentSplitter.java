package com.facishare.crm.task.sfa.activitysummary.service;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 内容分割器
 * 负责将长文本智能分割成适合处理的片段
 *
 * <AUTHOR> Assistant
 * @since 2024
 */
@Component
@Slf4j
public class ContentSplitter {

    // 默认配置-deepseek v3 根据官网介绍，理论上支持 57344/0.6 = 95,573个汉字
    private static final int DEFAULT_MAX_SEGMENT_LENGTH = 50000; // 5w字
    private static final int DEFAULT_MIN_OVERLAP_LENGTH = 200; // 最小叠加200字
    private static final int DEFAULT_MAX_OVERLAP_LENGTH = 800; // 最大叠加800字
    private static final int DEFAULT_SEARCH_RANGE = 100; // 边界搜索范围
    private static final int MIN_SEGMENT_LENGTH = 5000; // 最小段落长度，避免产生过短的段落

    /**
     * 对内容进行智能分割
     *
     * @param content 待分割的内容
     * @return 分割后的内容列表
     */
    public List<String> splitContent(String content) {
        return splitContent(content, DEFAULT_MAX_SEGMENT_LENGTH, DEFAULT_MIN_OVERLAP_LENGTH,
                DEFAULT_MAX_OVERLAP_LENGTH);
    }

    /**
     * 对内容进行智能分割（自定义参数）
     * 智能分段策略：优先段落边界、动态叠加、语义完整性、避免短段落
     *
     * @param content          待分割的内容
     * @param maxSegmentLength 最大分段长度
     * @param minOverlapLength 最小叠加长度
     * @param maxOverlapLength 最大叠加长度
     * @return 分割后的内容列表
     */
    public List<String> splitContent(String content, int maxSegmentLength, int minOverlapLength, int maxOverlapLength) {
        // 参数校验
        if (content == null || content.trim().isEmpty()) {
            return Lists.newArrayList();
        }
        
        // 参数合理性检查
        if (maxSegmentLength <= 0 || minOverlapLength < 0 || maxOverlapLength < minOverlapLength) {
            throw new IllegalArgumentException("参数不合法: maxSegmentLength=" + maxSegmentLength + 
                ", minOverlapLength=" + minOverlapLength + ", maxOverlapLength=" + maxOverlapLength);
        }

        List<String> segments = Lists.newArrayList();

        // 如果内容长度小于等于最大分段长度，直接返回
        if (content.length() <= maxSegmentLength) {
            segments.add(content);
            return segments;
        }

        int currentStart = 0;
        while (currentStart < content.length()) {
            int remainingLength = content.length() - currentStart;

            if (remainingLength <= maxSegmentLength * 1.2) {
                segments.add(content.substring(currentStart));
                log.info("剩余内容适中({}字符)，直接作为最后一段", remainingLength);
                break;
            }

            // 计算当前段的目标结束位置
            int targetEnd = currentStart + maxSegmentLength;
            
            // 策略2: 预判下一段，如果下一段会太短，适当调整当前段长度
            if (targetEnd < content.length()) {
                int nextSegmentLength = content.length() - targetEnd + maxOverlapLength; // 考虑最大叠加
                if (nextSegmentLength < MIN_SEGMENT_LENGTH) {
                    // 下一段会太短，将当前段缩短一些，让剩余内容更合理
                    int adjustment = (MIN_SEGMENT_LENGTH - nextSegmentLength + 1) / 2;
                    targetEnd = Math.max(currentStart + maxSegmentLength / 2, targetEnd - adjustment);
                    log.info("预判下一段过短，调整当前段目标长度为{}字符", targetEnd - currentStart);
                }
            }
            
            // 确保目标结束位置不超过内容长度
            targetEnd = Math.min(targetEnd, content.length());

            // 查找最优的分段位置
            SegmentBoundary boundary = findOptimalSegmentBoundary(content, currentStart, targetEnd, 
                minOverlapLength, maxOverlapLength, segments.isEmpty());

            // 添加当前段
            segments.add(content.substring(boundary.start, boundary.end));
            
            // 更新下一段的起始位置
            currentStart = boundary.end;
            
            // 安全检查：防止无限循环
            if (boundary.end <= boundary.start) {
                log.error("检测到无效的段落边界: start={}, end={}, 强制结束分割", boundary.start, boundary.end);
                break;
            }
        }

        log.info("智能内容分割完成，原始长度: {}字符, 分割段数: {}, 段落长度分布: {}",
                content.length(), segments.size(),
                segments.stream().map(String::length).collect(Collectors.toList()));
        return segments;
    }

    /**
     * 段落边界信息
     */
    private static class SegmentBoundary {
        final int start;
        final int end;
        
        SegmentBoundary(int start, int end) {
            this.start = start;
            this.end = end;
        }
    }

    /**
     * 查找最优的分段边界（起始和结束位置）
     *
     * @param content          内容
     * @param currentStart     当前起始位置
     * @param targetEnd        目标结束位置
     * @param minOverlapLength 最小叠加长度
     * @param maxOverlapLength 最大叠加长度
     * @param isFirstSegment   是否是第一段
     * @return 分段边界信息
     */
    private SegmentBoundary findOptimalSegmentBoundary(String content, int currentStart, int targetEnd,
                                                       int minOverlapLength, int maxOverlapLength, boolean isFirstSegment) {
        int segmentStart;
        int segmentEnd;

        // 确定起始位置
        if (isFirstSegment) {
            // 第一段从头开始
            segmentStart = 0;
        } else {
            // 后续段落：寻找叠加位置
            segmentStart = findOptimalStartPosition(content, currentStart, minOverlapLength, maxOverlapLength);
        }

        // 确定结束位置
        segmentEnd = findOptimalEndPosition(content, segmentStart, targetEnd);

        // 边界校验和修正
        segmentStart = Math.max(0, Math.min(segmentStart, content.length()));
        segmentEnd = Math.max(segmentStart + 1, Math.min(segmentEnd, content.length()));

        return new SegmentBoundary(segmentStart, segmentEnd);
    }

    /**
     * 寻找最优的分段起始位置
     * 在当前位置向前查找合适的边界位置，实现内容叠加
     *
     * @param content      内容
     * @param currentStart 当前起始位置
     * @param minOverlap   最小叠加长度
     * @param maxOverlap   最大叠加长度
     * @return 最优起始位置
     */
    private int findOptimalStartPosition(String content, int currentStart, int minOverlap, int maxOverlap) {
        int searchStart = Math.max(0, currentStart - maxOverlap);
        int searchEnd = Math.max(0, currentStart - minOverlap);

        // 如果搜索范围无效，直接返回最小叠加位置
        if (searchStart >= searchEnd) {
            return Math.max(0, currentStart - minOverlap);
        }

        // 按优先级查找边界位置
        BoundaryResult result = findBoundaryInRange(content, searchStart, searchEnd, true);
        
        if (result.found) {
            return result.position;
        }

        // 如果没找到合适边界，返回最小叠加位置
        return Math.max(0, currentStart - minOverlap);
    }

    /**
     * 寻找最优的分段结束位置
     * 在目标位置附近寻找合适的边界
     *
     * @param content   内容
     * @param start     起始位置
     * @param targetEnd 目标结束位置
     * @return 最优结束位置
     */
    private int findOptimalEndPosition(String content, int start, int targetEnd) {
        if (targetEnd >= content.length()) {
            return content.length();
        }

        int searchStart = Math.max(start, targetEnd - DEFAULT_SEARCH_RANGE);
        int searchEnd = Math.min(content.length(), targetEnd + DEFAULT_SEARCH_RANGE);

        // 优先向后搜索边界
        BoundaryResult forwardResult = findBoundaryInRange(content, targetEnd, searchEnd, false);
        if (forwardResult.found) {
            return forwardResult.position;
        }

        // 向前搜索边界
        BoundaryResult backwardResult = findBoundaryInRange(content, searchStart, targetEnd, true);
        if (backwardResult.found) {
            return backwardResult.position;
        }

        return targetEnd;
    }

    /**
     * 边界查找结果
     */
    private static class BoundaryResult {
        final boolean found;
        final int position;
        
        BoundaryResult(boolean found, int position) {
            this.found = found;
            this.position = position;
        }
    }

    /**
     * 在指定范围内查找边界位置
     * 统一的边界查找逻辑，支持正向和反向搜索
     *
     * @param content   内容
     * @param start     搜索起始位置
     * @param end       搜索结束位置
     * @param backward  是否为反向搜索
     * @return 边界查找结果
     */
    private BoundaryResult findBoundaryInRange(String content, int start, int end, boolean backward) {
        // 参数校验
        if (start < 0 || end > content.length() || start >= end) {
            return new BoundaryResult(false, -1);
        }

        // 存储不同优先级的边界位置
        int paragraphBoundary = -1;
        int sentenceEnd = -1;
        int punctuation = -1;
        int whitespace = -1;

        // 根据搜索方向确定循环参数
        int from = backward ? end - 1 : start;
        int to = backward ? start - 1 : end;
        int step = backward ? -1 : 1;

        // 搜索边界
        for (int i = from; backward ? i > to : i < to; i += step) {
            if (i < 0 || i >= content.length()) continue;
            
            char ch = content.charAt(i);

            // 1. 段落边界（最高优先级）
            if (isParagraphBoundary(content, i)) {
                paragraphBoundary = adjustBoundaryPosition(i, backward);
                break; // 找到最高优先级，立即返回
            }

            // 2. 句子结束
            if (sentenceEnd == -1 && isCompleteSentenceEnd(content, i)) {
                sentenceEnd = adjustBoundaryPosition(i, backward);
            }

            // 3. 标点符号
            if (punctuation == -1 && isPunctuation(ch)) {
                punctuation = adjustBoundaryPosition(i, backward);
            }

            // 4. 空格
            if (whitespace == -1 && Character.isWhitespace(ch)) {
                whitespace = adjustBoundaryPosition(i, backward);
            }
        }

        // 按优先级返回结果
        if (paragraphBoundary != -1) return new BoundaryResult(true, paragraphBoundary);
        if (sentenceEnd != -1) return new BoundaryResult(true, sentenceEnd);
        if (punctuation != -1) return new BoundaryResult(true, punctuation);
        if (whitespace != -1) return new BoundaryResult(true, whitespace);

        return new BoundaryResult(false, -1);
    }

    /**
     * 判断是否是段落边界
     * 统一的段落边界检测逻辑
     *
     * @param content 内容
     * @param index   位置索引
     * @return 是否是段落边界
     */
    private boolean isParagraphBoundary(String content, int index) {
        if (index < 0 || index >= content.length()) {
            return false;
        }

        char ch = content.charAt(index);
        if (ch != '\n') {
            return false;
        }

        // 检查是否是段落分隔（连续换行或文档开头/结尾的换行）
        boolean isStart = (index == 0);
        boolean isEnd = (index == content.length() - 1);
        boolean hasPrevNewline = (index > 0 && content.charAt(index - 1) == '\n');
        boolean hasNextNewline = (index < content.length() - 1 && content.charAt(index + 1) == '\n');

        return isStart || isEnd || hasPrevNewline || hasNextNewline;
    }

    /**
     * 根据搜索方向调整边界位置
     *
     * @param position 原始位置
     * @param backward 是否为反向搜索
     * @return 调整后的位置
     */
    private int adjustBoundaryPosition(int position, boolean backward) {
        if (backward) {
            // 反向搜索时，边界位置应该是分隔符之后
            return position + 1;
        } else {
            // 正向搜索时，边界位置应该是分隔符本身
            return position;
        }
    }

    /**
     * 判断是否是完整句子的结束
     *
     * @param content 内容
     * @param index   位置索引
     * @return 是否是句子结束
     */
    private boolean isCompleteSentenceEnd(String content, int index) {
        if (index < 0 || index >= content.length()) {
            return false;
        }

        char ch = content.charAt(index);
        
        // 中文句号、问号、感叹号
        if (ch == '。' || ch == '？' || ch == '！') {
            return true;
        }

        // 英文句号、问号、感叹号（后面跟空格、换行或大写字母）
        if ((ch == '.' || ch == '?' || ch == '!') && index < content.length() - 1) {
            char next = content.charAt(index + 1);
            return Character.isWhitespace(next) || Character.isUpperCase(next);
        }

        return false;
    }

    /**
     * 判断是否是标点符号
     *
     * @param ch 字符
     * @return 是否是标点符号
     */
    private boolean isPunctuation(char ch) {
        String punctuations = "。！？；：，、.!?;:,（）()[]【】《》<>";
        return punctuations.indexOf(ch) >= 0;
    }

    /**
     * 查找最后一个标点符号的位置（保持向下兼容）
     *
     * @param text 文本
     * @return 标点符号位置，未找到返回-1
     */
    public int findLastPunctuation(String text) {
        if (text == null || text.isEmpty()) {
            return -1;
        }
        
        for (int i = text.length() - 1; i >= 0; i--) {
            if (isPunctuation(text.charAt(i))) {
                return i;
            }
        }
        return -1;
    }
}