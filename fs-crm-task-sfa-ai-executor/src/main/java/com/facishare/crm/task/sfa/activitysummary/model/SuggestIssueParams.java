package com.facishare.crm.task.sfa.activitysummary.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface SuggestIssueParams {
    @Data
    @Builder
    class AIQuestion {
        private String index;
        private String question;
        private String answer1;
        private String answer2;
    }

    @Data
    class AIAnswer {
        private String index;
        private String question;
        private String answer;
        private String status;
        @JSONField(name = "doc_id")
        private String docIdRaw;
        private List<String> docIds;
    }
}
