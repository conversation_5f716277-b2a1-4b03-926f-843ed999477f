package com.facishare.crm.task.sfa.bizfeature.util;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureScoreConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.TaskFeatureConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class NodeFinishUtil {
    public static double NODE_FINISH_SCORE = 0.8;
    public static double TASK_FINISH_SCORE = 0.6;
    public static BigDecimal standardScore = BigDecimal.valueOf(6);

    /**
     * 计算任务是否完成
     *
     * @param finishScore     完成标准
     * @param allFeature      任务/节点下所有特征
     * @param allFeatureScore 特征分
     * @return
     */
    public static boolean finishNode(double finishScore, List<IObjectData> allFeature, List<IObjectData> allFeatureScore) {
        if (CollectionUtils.empty(allFeatureScore)) {
            return false;
        }
        Map<String, IObjectData> featureScoreMap = allFeatureScore.stream()
                .collect(Collectors.toMap(x -> x.get(FeatureScoreConstants.FEATURE_ID, String.class),
                        Function.identity(), (existing, replacement) -> existing));
        return finishNode(finishScore, allFeature, featureScoreMap);
    }

    public static boolean finishNode(double finishScore, List<IObjectData> allFeature, Map<String, IObjectData> featureScoreMap) {
        int count = 0;
        if (CollectionUtils.empty(featureScoreMap)) {
            return false;
        }

        for (IObjectData featureData : allFeature) {
            String featureId = featureData.get(TaskFeatureConstants.FEATURE_ID, String.class);
            IObjectData featureScore = featureScoreMap.get(featureId);
            Boolean mustDo = featureData.get(TaskFeatureConstants.MUST_DO, Boolean.class);
            if (featureScore == null) {
                if (mustDo) {
                    count = count - allFeature.size();
                }
                continue;
            }
            BigDecimal score = featureScore.get(FeatureScoreConstants.SCORE, BigDecimal.class);
            if (score.compareTo(standardScore) >= 0) {
                count++;
            } else if (mustDo) {
                count = count - allFeature.size();
            }
        }
        return (double) count / allFeature.size() >= finishScore;
    }
}
