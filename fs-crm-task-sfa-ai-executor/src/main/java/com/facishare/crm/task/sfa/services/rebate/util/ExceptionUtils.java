package com.facishare.crm.task.sfa.services.rebate.util;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

@Slf4j
public class ExceptionUtils {
    /**
     * 参数错误
     */
    public static String SFA_PARAMET_ERERROR = "sfa.paramet.ererror";

    /**
     * 异常提供者,输出日志
     *
     * @param i18nCode  i18n代码
     * @param format    格式
     * @param arguments 参数
     * @return {@code Supplier<ValidateException>}
     */
    public static Supplier<RuntimeException> supplier(String i18nCode, String format, Object... arguments) {
        return () -> new ValidateException(I18N.text(i18nCode));
    }


    /**
     * 异常提供者
     *
     * @param i18nCode i18n代码
     * @return {@code Supplier<ValidateException>}
     */
    public static Supplier<RuntimeException> supplier(String i18nCode) {
        return () -> new ValidateException(I18N.text(i18nCode));
    }

    /**
     * 处理json解析的异常
     *
     * @param supplier 供应商
     * @return {@code T}
     */
    public static <T> T trySupplier(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            throw supplier(SFA_PARAMET_ERERROR, e.getMessage()).get();
        }
    }

    /**
     * 处理json解析的异常
     *
     * @param supplier 供应商
     * @return {@code T}
     */
    public static <T> T tryDefault(Supplier<T> supplier,T t) {
        try {
            return supplier.get();
        } catch (Exception e) {
            return t;
        }
    }

    /**
     * 处理json解析的异常
     */
    public static <T> T trySupplier(Supplier<T> supplier, String i18nCode) {
        try {
            return supplier.get();
        } catch (Exception e) {
            throw new ValidateException(String.format("%s%s", I18N.text(i18nCode), I18N.text(SFA_PARAMET_ERERROR)));
        }
    }
}
