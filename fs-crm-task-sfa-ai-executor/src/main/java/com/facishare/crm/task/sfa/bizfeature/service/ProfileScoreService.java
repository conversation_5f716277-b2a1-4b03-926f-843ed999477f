package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.crm.task.sfa.bizfeature.enums.ProfileScoreTypeEnum;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileSearchModel;
import com.facishare.crm.task.sfa.util.FeatureBaseDataUtils;
import com.facishare.crm.task.sfa.util.ProfileUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.license.message.OverviewChangeMessage;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ProfileScoreService implements IProfileScoreService {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    BaseDataMergeManager baseDataMergeManager;
    @Resource
    SpecialTableMapper specialTableMapper;
    @Resource
    ProfileAdviceProducer profileAdviceProducer;
    @Resource
    ProfileCommonService profileCommonService;
    @Resource
    private RedissonServiceImpl redissonService;
    @Resource(name = "licenseParaChange")
    private AutoConfMQProducer producer;

    private static final BigDecimal SWITCH_FEATURE_SCORE = BigDecimal.valueOf(6);

    private static final int SUB_DECIMAL_PLACES = 4;
    private static final int ROOT_DECIMAL_PLACES = 0;

    private static final String PROFILE_QUOTA_KEY = "sales_portrait_insight_agent_100portraits_limit";

    public void scoreCalc(ProfileScoreModel profileScoreModel) {
        User user = User.systemUser(profileScoreModel.getTenantId());
        if (StringUtils.isNotEmpty(profileScoreModel.getUserId())) {
            user = new User(profileScoreModel.getTenantId(), profileScoreModel.getUserId());
        }
        SearchTemplateQueryPlus methodologyInstanceQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(methodologyInstanceQuery).addFilter(Operator.EQ,
                        ProfileUtil.parseFilterFiled(profileScoreModel.getObjectApiName()), profileScoreModel.getObjectId())
                .addFilter(Operator.EQ, MethodologyInstanceConstants.STATUS,
                        MethodologyInstanceConstants.StatusType.ENABLE.getStatusType());
        List<IObjectData> methodologyInstanceList = serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.METHODOLOGY_INSTANCE, methodologyInstanceQuery).getData();
        if (CollectionUtils.isEmpty(methodologyInstanceList)) {
            log.info("methodologyInstanceList is empty, tenantId:{}", user.getTenantId());
            return;
        }
        Map<String, List<String>> methodologyToObjectApiNameMap = Maps.newHashMap();
        for (IObjectData methodologyInstance : methodologyInstanceList) {
            List<String> objectApiNames = ProfileUtil.methodologyToObjectApiName(methodologyInstance);
            methodologyToObjectApiNameMap.put(methodologyInstance.getId(), objectApiNames);
        }
        commonScoreCalc(user, methodologyToObjectApiNameMap, methodologyInstanceList);
    }

    public void commonScoreCalc(User user, Map<String, List<String>> methodologyToObjectApiNameMap,
                                List<IObjectData> methodologyInstanceList) {
        StopWatch stopWatch = StopWatch.create("commonScoreCalc");
        log.info("commonScoreCalc, user:{} methodologyToObjectApiNameMap:{} ", user, methodologyToObjectApiNameMap);
        List<String> methodologyIds = methodologyInstanceList.stream()
                .map(methodologyInstance -> methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_ID, String.class))
                .distinct().collect(Collectors.toList());
        // 获取所有方法论
        List<IObjectData> methodologyList = baseDataMergeManager.findObjectDataByIdsIgnoreAll(user, methodologyIds, FeatureConstants.METHODOLOGY);
        stopWatch.lap("fetchMethodologyList");
        // 方法论类型
        Map<String, String> methodologyTypeMap = fetchMethodologyList(methodologyList);
        // 方法论展示层级
        Map<String, Integer> methodologyShowLevelMap = fetchMethodologyShowLevel(methodologyList);
        // 获取方法论实例下的所有节点实例
        List<String> filterNodeInstanceIds = Lists.newArrayList();
        Map<String, List<IObjectData>> methodologyNodeInstanceMap = fetchAndFilterNodeInstances(user,
                methodologyInstanceList, filterNodeInstanceIds, methodologyTypeMap);
        stopWatch.lap("fetchAndFilterNodeInstances");
        // 获取实例特征关系数据
        List<IObjectData> instanceFeatureList = fetchInstanceFeatures(user, methodologyInstanceList);
        stopWatch.lap("fetchInstanceFeatures");
        if (CollectionUtils.isEmpty(instanceFeatureList)) {
            log.info("instanceFeatureList is empty, tenantId:{}", user.getTenantId());
            return;
        }
        // 处理任务特征数据
        Map<String, List<IObjectData>> instanceFeatureMap = processInstanceFeatureMap(instanceFeatureList);
        Map<String, List<String>> methodologyFeatureIdMap = processMethodologyFeatureIdMap(instanceFeatureList);
        // 获取特征ID列表
        List<String> featureIds = extractFeatureIds(instanceFeatureList);
        // 查询所有特征
        List<IObjectData> featureList = fetchFeatures(user, featureIds);
        stopWatch.lap("fetchFeatures");
        if (CollectionUtils.isEmpty(featureList)) {
            log.info("featureList is empty, tenantId:{}", user.getTenantId());
            return;
        }
        // 查询所有特征权重
        List<IObjectData> featureWeightList = fetchFeatureWeights(user, featureIds, methodologyIds);
        stopWatch.lap("fetchFeatureWeights");
        Map<String, List<IObjectData>> methodologyWeightMap = filterMethodologyWeights(featureWeightList);
        Map<String, List<IObjectData>> methodologyNodeWeightMap = filterNodeWeights(featureWeightList);
        Map<String, BigDecimal> recentFeatureScoreMap = Maps.newHashMap();
        Map<String, String> recentFeatureOriValueMap = Maps.newHashMap();
        // 查询特征分数
        Map<String, BigDecimal> featureScoreMap = fetchFeatureScores(user, methodologyInstanceList,
                instanceFeatureMap, recentFeatureScoreMap, recentFeatureOriValueMap);
        stopWatch.lap("fetchFeatureScores");
        // 生成和保存画像数据
        List<IObjectData> addProfileList = Lists.newArrayList();
        List<IObjectData> updateProfileList = Lists.newArrayList();
        List<IObjectData> addProfileItemScoreList = Lists.newArrayList();
        // 生成画像数据
        generateSaveProfileDate(user, addProfileList, updateProfileList, addProfileItemScoreList,
                methodologyInstanceList, methodologyToObjectApiNameMap, methodologyTypeMap,
                methodologyFeatureIdMap, featureList, featureScoreMap, methodologyWeightMap,
                methodologyNodeInstanceMap, instanceFeatureMap, methodologyNodeWeightMap,
                recentFeatureOriValueMap, methodologyShowLevelMap);
        stopWatch.lap("generateSaveProfileDate");
        // 保存画像数据
        saveProfileDate(user, addProfileList, updateProfileList, addProfileItemScoreList);
        stopWatch.lap("saveProfileDate");
        // 发送计算优劣势建议消息
        sendProfileAdvice(user, addProfileList, updateProfileList);
        stopWatch.lap("sendProfileAdvice");
        stopWatch.logSlow(1000);
    }

    /**
     * 获取所有方法论
     */
    private Map<String, String> fetchMethodologyList(List<IObjectData> methodologyList) {
        return methodologyList.stream()
                .collect(Collectors.toMap(methodology -> methodology.get(DBRecord.ID, String.class), methodology ->
                        methodology.get(MethodologyConstants.TYPE, String.class, MethodologyConstants.Type.FLOW.getType())));
    }

    /**
     * 获取方法论展示层级
     */
    private Map<String, Integer> fetchMethodologyShowLevel(List<IObjectData> methodologyList) {
        return methodologyList.stream()
                .collect(Collectors.toMap(methodology -> methodology.get(DBRecord.ID, String.class), methodology ->
                        methodology.get(MethodologyConstants.SHOW_LEVEL, Integer.class, 1)));
    }

    /**
     * 获取并过滤节点实例
     */
    private Map<String, List<IObjectData>> fetchAndFilterNodeInstances(User user,
                                                                       List<IObjectData> methodologyInstanceList,
                                                                       List<String> filterNodeInstanceIds,
                                                                       Map<String, String> methodologyTypeMap) {
        Map<String, Integer> methodologyInstanceStageLevelMap = methodologyInstanceList.stream()
                .collect(Collectors.toMap(DBRecord::getId, methodologyInstance ->
                        methodologyInstance.get(MethodologyInstanceConstants.STAGE_LEVEL, Integer.class, 1)));
        Map<String, String> methodologyInstanceIdMap = methodologyInstanceList.stream()
                .collect(Collectors.toMap(DBRecord::getId, methodologyInstance ->
                        methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_ID, String.class)));
        SearchTemplateQueryPlus nodeInstanceQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(nodeInstanceQuery).addFilter(Operator.IN, NodeInstanceConstants.METHODOLOGY_INSTANCE_ID, methodologyInstanceList.stream()
                .map(DBRecord::getId).collect(Collectors.toList()));
        List<IObjectData> nodeInstanceList = serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.NODE_INSTANCE, nodeInstanceQuery).getData();
        return filterNodeInstanceMap(nodeInstanceList, filterNodeInstanceIds, methodologyTypeMap, methodologyInstanceIdMap, methodologyInstanceStageLevelMap);
    }

    private List<IObjectData> fetchInstanceFeatures(User user, List<IObjectData> methodologyInstanceList) {
        SearchTemplateQueryPlus instanceFeatureQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(instanceFeatureQuery).addFilter(Operator.IN, InstanceFeatureConstants.METHODOLOGY_INSTANCE_ID, methodologyInstanceList.stream()
                .map(DBRecord::getId).collect(Collectors.toList()));
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.INSTANCE_FEATURE, instanceFeatureQuery).getData();
    }

    /**
     * 处理任务特征数据，按方法论ID分组
     */
    private Map<String, List<IObjectData>> processInstanceFeatureMap(List<IObjectData> instanceFeatureList) {
        return instanceFeatureList.stream()
                .collect(Collectors.groupingBy(instanceFeature -> instanceFeature.get(InstanceFeatureConstants.METHODOLOGY_INSTANCE_ID, String.class)));
    }

    /**
     * 处理方法论特征ID映射
     */
    private Map<String, List<String>> processMethodologyFeatureIdMap(List<IObjectData> instanceFeatureList) {
        return instanceFeatureList.stream()
                .collect(Collectors.groupingBy(instanceFeature -> instanceFeature.get(InstanceFeatureConstants.METHODOLOGY_INSTANCE_ID, String.class),
                        Collectors.mapping(instanceFeature -> instanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class), Collectors.toList())));
    }

    /**
     * 提取特征ID列表
     */
    private List<String> extractFeatureIds(List<IObjectData> instanceFeatureList) {
        return instanceFeatureList.stream()
                .map(instanceFeature -> instanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取特征列表
     */
    private List<IObjectData> fetchFeatures(User user, List<String> featureIds) {
        SearchTemplateQueryPlus featureQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(featureQuery).addFilter(Operator.IN, DBRecord.ID, featureIds);
        SearchTemplateQueryExt.of(featureQuery).addFilter(Operator.EQ, FeatureConstants.STATUS, FeatureConstants.StatusType.ENABLED.getStatusType());
        return baseDataMergeManager.findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE, featureQuery);
    }

    /**
     * 获取特征权重
     */
    private List<IObjectData> fetchFeatureWeights(User user, List<String> featureIds, List<String> methodologyIds) {
        SearchTemplateQueryPlus featureWeightQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(featureWeightQuery).addFilter(Operator.IN, FeatureWeightConstants.FEATURE_ID, featureIds);
        SearchTemplateQueryExt.of(featureWeightQuery).addFilter(Operator.IN, FeatureWeightConstants.METHODOLOGY_ID, methodologyIds);
        return baseDataMergeManager.findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE_WEIGHT, featureWeightQuery);
    }

    /**
     * 过滤方法论权重
     */
    private Map<String, List<IObjectData>> filterMethodologyWeights(List<IObjectData> featureWeightList) {
        return featureWeightList.stream()
                .filter(featureWeight -> featureWeight.get(FeatureWeightConstants.TYPE, String.class).equals(FeatureWeightConstants.Type.DIMENSION.getValue()))
                .collect(Collectors.groupingBy(featureWeight -> featureWeight.get(FeatureWeightConstants.METHODOLOGY_ID, String.class)));
    }

    /**
     * 过滤节点权重
     */
    private Map<String, List<IObjectData>> filterNodeWeights(List<IObjectData> featureWeightList) {
        return featureWeightList.stream()
                .filter(featureWeight -> featureWeight.get(FeatureWeightConstants.TYPE, String.class).equals(FeatureWeightConstants.Type.FLOW.getValue()))
                .collect(Collectors.groupingBy(featureWeight -> featureWeight.get(FeatureWeightConstants.METHODOLOGY_ID, String.class)));
    }

    public List<IObjectData> getTaskFeatureScores(User user, String methodologyId, String objectApiName, String objectId, List<IObjectData> taskFeatureList) {
        IObjectData sourceObject = serviceFacade.findObjectDataIgnoreAll(user, objectId, objectApiName);
        Map<String, IObjectData> sourceObjectMap = Maps.newHashMap();
        sourceObjectMap.put(objectId, sourceObject);
        Set<String> objectApiNameSet = Sets.newHashSet();
        Set<String> objectIdSet = Sets.newHashSet();
        Set<String> featureIdSet = Sets.newHashSet();
        collectFeatureData(taskFeatureList, objectApiName, objectId, sourceObjectMap, objectApiNameSet, objectIdSet, featureIdSet);
        if (objectApiNameSet.isEmpty() || objectIdSet.isEmpty() || featureIdSet.isEmpty()) {
            return Lists.newArrayList();
        }
        List<IObjectData> allFeatureScoreList = queryFeatureScores(user, objectApiNameSet, objectIdSet, featureIdSet);
        Map<String, List<IObjectData>> featureScoreGroupMap = groupFeatureScores(allFeatureScoreList);
        // 获取特征评分规则设置
        Map<String, IObjectData> featureScoreRuleMap = getFeatureScoreRuleMap(user, Lists.newArrayList(methodologyId), Lists.newArrayList(featureIdSet));
        // 匹配并处理特征分数
        List<IObjectData> taskFeatureScoreList = Lists.newArrayList();
        taskFeatureList.forEach(taskFeature -> {
            String featureId = taskFeature.get(TaskFeatureConstants.FEATURE_ID, String.class);
            IObjectData featureScoreRule = featureScoreRuleMap.get(methodologyId + "_" + featureId);
            List<IObjectData> matchedScoreList = featureScoreGroupMap.get(
                    (featureScoreRule != null ? methodologyId : "") + "_" + featureId);
            if (CollectionUtils.isNotEmpty(matchedScoreList)) {
                taskFeatureScoreList.add(matchedScoreList.get(0));
            }
        });
        return taskFeatureScoreList;
    }

    /**
     * 获取特征分数
     */
    public Map<String, BigDecimal> fetchFeatureScores(User user, List<IObjectData> methodologyInstanceList,
                                                      Map<String, List<IObjectData>> instanceFeatureMap) {
        return fetchFeatureScores(user, methodologyInstanceList, instanceFeatureMap, null, null);
    }

    /**
     * 获取特征分数
     */
    public Map<String, BigDecimal> fetchFeatureScores(User user, List<IObjectData> methodologyInstanceList,
                                                      Map<String, List<IObjectData>> instanceFeatureMap,
                                                      Map<String, BigDecimal> recentFeatureScoreMap,
                                                      Map<String, String> recentFeatureOriValueMap) {
        // 获取源对象与特征对象不相等的源对象数据
        Map<String, IObjectData> sourceObjectMap = getSourceObjectMap(user, methodologyInstanceList, instanceFeatureMap);
        Map<String, BigDecimal> recentAllFeatureScoreMap = recentFeatureScoreMap != null ? Maps.newHashMap() : null;
        Map<String, String> recentAllFeatureOriValueMap = recentFeatureOriValueMap != null ? Maps.newHashMap() : null;
        // 获取所有特征分数
        Map<String, BigDecimal> allFeatureScoreMap = getAllFeatureScores(user, methodologyInstanceList, sourceObjectMap,
                instanceFeatureMap, recentAllFeatureScoreMap, recentAllFeatureOriValueMap);
        // 匹配特征分数
        return matchFeatureScore(methodologyInstanceList, instanceFeatureMap, sourceObjectMap, allFeatureScoreMap,
                recentAllFeatureScoreMap, recentFeatureScoreMap, recentAllFeatureOriValueMap,
                recentFeatureOriValueMap);
    }

    private Map<String, IObjectData> getSourceObjectMap(User user, List<IObjectData> methodologyInstanceList,
                                                        Map<String, List<IObjectData>> instanceFeatureMap) {
        Map<String, IObjectData> sourceObjectMap = Maps.newHashMap();
        Map<String, Set<String>> objectIdsByType = Maps.newHashMap();
        objectIdsByType.put(Utils.NEW_OPPORTUNITY_API_NAME, Sets.newHashSet());
        objectIdsByType.put(Utils.ACCOUNT_API_NAME, Sets.newHashSet());
        objectIdsByType.put(Utils.LEADS_API_NAME, Sets.newHashSet());
        // 收集需要查询的对象ID
        methodologyInstanceList.stream()
                .filter(methodologyInstance -> {
                    String methodologyInstanceId = methodologyInstance.get(DBRecord.ID, String.class);
                    return CollectionUtils.isNotEmpty(instanceFeatureMap.get(methodologyInstanceId));
                })
                .forEach(methodologyInstance -> {
                    String methodologyInstanceId = methodologyInstance.get(DBRecord.ID, String.class);
                    List<IObjectData> instanceFeatureList = instanceFeatureMap.get(methodologyInstanceId);
                    List<String> objectApiNames = ProfileUtil.methodologyToObjectApiName(methodologyInstance);
                    objectApiNames.forEach(objectApiName -> {
                        String objectId = methodologyInstance.get(ProfileUtil.parseFilterFiled(objectApiName), String.class);
                        instanceFeatureList.stream()
                                .filter(instanceFeature -> {
                                    String relatedObjectApiName = instanceFeature.get(InstanceFeatureConstants.RELATED_OBJECT_API_NAME, String.class);
                                    String featureObjectApiName = instanceFeature.get(InstanceFeatureConstants.FEATURE_OBJECT_API_NAME, String.class);
                                    return objectApiName.equals(relatedObjectApiName) && !relatedObjectApiName.equals(featureObjectApiName);
                                })
                                .findFirst()
                                .ifPresent(instanceFeature -> objectIdsByType.get(objectApiName).add(objectId));
                    });
                });
        // 批量查询对象数据
        objectIdsByType.forEach((apiName, ids) -> {
            if (CollectionUtils.isNotEmpty(ids)) {
                List<IObjectData> objectList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(ids), apiName);
                objectList.forEach(obj -> sourceObjectMap.put(obj.getId(), obj));
            }
        });
        return sourceObjectMap;
    }

    private Map<String, BigDecimal> getAllFeatureScores(User user, List<IObjectData> methodologyInstanceList,
                                                        Map<String, IObjectData> sourceObjectMap,
                                                        Map<String, List<IObjectData>> instanceFeatureMap,
                                                        Map<String, BigDecimal> recentAllFeatureScoreMap,
                                                        Map<String, String> recentAllFeatureOriValueMap) {
        // 收集需要查询的数据
        Set<String> objectApiNameSet = Sets.newHashSet();
        Set<String> objectIdSet = Sets.newHashSet();
        Set<String> featureIdSet = Sets.newHashSet();
        List<String> methodologyIds = Lists.newArrayList();
        // 遍历收集数据
        methodologyInstanceList.stream()
                .filter(methodologyInstance -> CollectionUtils.isNotEmpty(instanceFeatureMap.get(
                        methodologyInstance.get(DBRecord.ID, String.class))))
                .forEach(methodologyInstance -> {
                    String methodologyId = methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_ID, String.class);
                    methodologyIds.add(methodologyId);
                    String methodologyInstanceId = methodologyInstance.get(DBRecord.ID, String.class);
                    List<IObjectData> instanceFeatureList = instanceFeatureMap.get(methodologyInstanceId);
                    ProfileUtil.methodologyToObjectApiName(methodologyInstance).forEach(objectApiName -> {
                        String objectId = methodologyInstance.get(ProfileUtil.parseFilterFiled(objectApiName), String.class);
                        collectFeatureData(instanceFeatureList, objectApiName, objectId, sourceObjectMap,
                                objectApiNameSet, objectIdSet, featureIdSet);
                    });
                });
        // 无数据直接返回
        if (objectApiNameSet.isEmpty() || objectIdSet.isEmpty() || featureIdSet.isEmpty()) {
            return Maps.newHashMap();
        }
        // 查询特征分数
        List<IObjectData> featureScoreList = queryFeatureScores(user, objectApiNameSet, objectIdSet, featureIdSet);
        Long oneWeekAgoTimestamp = ProfileUtil.getOneWeekAgoTimestamp();
        // 查询并处理特征值
        processFeatureValues(user, featureScoreList, oneWeekAgoTimestamp, recentAllFeatureOriValueMap);
        // 处理特征分数
        Map<String, BigDecimal> featureScoreMap = Maps.newHashMap();
        Map<String, List<IObjectData>> featureScoreGroupMap = groupFeatureScores(featureScoreList);
        // 获取特征评分规则设置
        Map<String, IObjectData> featureScoreRuleMap = getFeatureScoreRuleMap(user, methodologyIds, Lists.newArrayList(featureIdSet));
        // 匹配并处理特征分数
        processMatchedFeatureScores(methodologyInstanceList, instanceFeatureMap, featureScoreRuleMap,
                featureScoreGroupMap, featureScoreMap, recentAllFeatureScoreMap, oneWeekAgoTimestamp);
        return featureScoreMap;
    }

    private List<IObjectData> queryFeatureScores(User user, Set<String> objectApiNameSet,
                                                 Set<String> objectIdSet, Set<String> featureIdSet) {
        SearchTemplateQueryPlus featureScoreQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(featureScoreQuery)
                .addFilter(Operator.IN, FeatureScoreConstants.FEATURE_ID, Lists.newArrayList(featureIdSet))
                .addFilter(Operator.IN, FeatureScoreConstants.OBJECT_ID, Lists.newArrayList(objectIdSet))
                .addFilter(Operator.IN, FeatureScoreConstants.OBJECT_API_NAME, Lists.newArrayList(objectApiNameSet));
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE_SCORE, featureScoreQuery).getData();
    }

    private void processFeatureValues(User user, List<IObjectData> featureScoreList,
                                      Long oneWeekAgoTimestamp, Map<String, String> recentAllFeatureOriValueMap) {
        List<String> featureValueIds = featureScoreList.stream()
                .map(score -> score.get(FeatureScoreConstants.FEATURE_VALUE_ID, String.class))
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(featureValueIds)) {
            SearchTemplateQueryPlus featureValueQuery = SearchUtil.buildBaseSearchQuery();
            SearchTemplateQueryExt.of(featureValueQuery)
                    .addFilter(Operator.IN, DBRecord.ID, featureValueIds)
                    .addFilter(Operator.GTE, FeatureValueConstants.LAST_MODIFIED_TIME, String.valueOf(oneWeekAgoTimestamp));
            List<IObjectData> featureValueList = serviceFacade.findBySearchQueryIgnoreAll(user,
                    FeatureConstants.FEATURE_VALUE, featureValueQuery).getData();
            Optional.ofNullable(recentAllFeatureOriValueMap).ifPresent(map -> map.putAll(featureValueList.stream()
                    .filter(value -> value.get(FeatureValueConstants.TRIGGER_VALUE, String.class) != null)
                    .collect(Collectors.toMap(
                            this::buildFeatureScoreKey,
                            value -> value.get(FeatureValueConstants.TRIGGER_VALUE, String.class),
                            (s1, s2) -> s1
                    ))));
        }
    }

    private Map<String, List<IObjectData>> groupFeatureScores(List<IObjectData> featureScoreList) {
        return featureScoreList.stream()
                .collect(Collectors.groupingBy(
                        score -> {
                            String methodologyId = score.get(FeatureScoreConstants.METHODOLOGY_ID, String.class);
                            String featureId = score.get(FeatureScoreConstants.FEATURE_ID, String.class);
                            return (StringUtils.isEmpty(methodologyId) ? "" : methodologyId) + "_" + featureId;
                        }
                ));
    }

    private Map<String, IObjectData> getFeatureScoreRuleMap(User user, List<String> methodologyIds, List<String> featureIds) {
        Map<String, IObjectData> featureScoreRuleMap = Maps.newHashMap();
        SearchTemplateQueryPlus featureScoreRuleQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(featureScoreRuleQuery).addFilter(Operator.IN, FeatureScoreRuleConstants.FEATURE_ID, featureIds);
        SearchTemplateQueryExt.of(featureScoreRuleQuery).addFilter(Operator.IN, FeatureScoreRuleConstants.METHODOLOGY_ID, methodologyIds);
        List<IObjectData> featureScoreRuleList = baseDataMergeManager.findBySearchQueryIgnoreAll(user,
                FeatureConstants.FEATURE_SCORE_RULE, featureScoreRuleQuery);
        featureScoreRuleMap.putAll(featureScoreRuleList.stream()
                .collect(Collectors.toMap(featureScoreRule -> featureScoreRule.get(FeatureScoreRuleConstants.METHODOLOGY_ID, String.class)
                        + "_" + featureScoreRule.get(FeatureScoreRuleConstants.FEATURE_ID, String.class), scoringRule -> scoringRule)));
        return featureScoreRuleMap;
    }

    private void processMatchedFeatureScores(List<IObjectData> methodologyInstanceList,
                                             Map<String, List<IObjectData>> instanceFeatureMap,
                                             Map<String, IObjectData> featureScoreRuleMap,
                                             Map<String, List<IObjectData>> featureScoreGroupMap,
                                             Map<String, BigDecimal> featureScoreMap,
                                             Map<String, BigDecimal> recentAllFeatureScoreMap,
                                             Long oneWeekAgoTimestamp) {
        methodologyInstanceList.stream()
                .filter(methodologyInstance -> CollectionUtils.isNotEmpty(instanceFeatureMap.get(
                        methodologyInstance.get(DBRecord.ID, String.class))))
                .forEach(methodologyInstance -> {
                    String methodologyId = methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_ID, String.class);
                    String methodologyInstanceId = methodologyInstance.get(DBRecord.ID, String.class);
                    List<IObjectData> instanceFeatureList = instanceFeatureMap.get(methodologyInstanceId);
                    instanceFeatureList.forEach(instanceFeature -> {
                        String featureId = instanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class);
                        IObjectData featureScoreRule = featureScoreRuleMap.get(methodologyId + "_" + featureId);
                        List<IObjectData> matchedScoreList = featureScoreGroupMap.get(
                                (featureScoreRule != null ? methodologyId : "") + "_" + featureId);
                        if (CollectionUtils.isNotEmpty(matchedScoreList)) {
                            matchedScoreList.forEach(matchedScore -> {
                                String key = buildFeatureScoreKey(matchedScore);
                                BigDecimal score = matchedScore.get(FeatureScoreConstants.SCORE, BigDecimal.class);
                                if (score != null) {
                                    featureScoreMap.put(key, score);
                                    if (matchedScore.get(FeatureScoreConstants.CALC_TIME, Long.class, 0L) > oneWeekAgoTimestamp
                                            && recentAllFeatureScoreMap != null) {
                                        recentAllFeatureScoreMap.put(key, score);
                                    }
                                }
                            });
                        }
                    });

                });
    }

    private void collectFeatureData(List<IObjectData> instanceFeatureList, String objectApiName, String objectId,
                                    Map<String, IObjectData> sourceObjectMap, Set<String> objectApiNameSet,
                                    Set<String> objectIdSet, Set<String> featureIdSet) {
        for (IObjectData instanceFeature : instanceFeatureList) {
            if (!objectApiName.equals(instanceFeature.get(InstanceFeatureConstants.RELATED_OBJECT_API_NAME, String.class))) {
                continue;
            }
            String featureObjectApiName = instanceFeature.get(InstanceFeatureConstants.FEATURE_OBJECT_API_NAME, String.class);
            String featureId = instanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class);
            if (objectApiName.equals(featureObjectApiName)) {
                // 特征对象相等
                objectApiNameSet.add(objectApiName);
                objectIdSet.add(objectId);
                featureIdSet.add(featureId);
            } else {
                // 特征对象不相等
                IObjectData sourceObject = sourceObjectMap.get(objectId);
                if (sourceObject != null) {
                    String relatedField = instanceFeature.get(InstanceFeatureConstants.RELATED_FIELD, String.class);
                    String relatedId = sourceObject.get(relatedField, String.class);
                    if (StringUtils.isNotEmpty(relatedId)) {
                        objectApiNameSet.add(featureObjectApiName);
                        objectIdSet.add(relatedId);
                        featureIdSet.add(featureId);
                    }
                }
            }
        }
    }

    /**
     * 构建特征分数键
     */
    private String buildFeatureScoreKey(IObjectData featureScore) {
        return featureScore.get(FeatureScoreConstants.OBJECT_API_NAME, String.class) + "_" +
                featureScore.get(FeatureScoreConstants.OBJECT_ID, String.class) + "_" +
                featureScore.get(FeatureScoreConstants.FEATURE_ID, String.class);
    }

    /**
     * 匹配特征分数
     */
    private Map<String, BigDecimal> matchFeatureScore(List<IObjectData> methodologyInstanceList,
                                                      Map<String, List<IObjectData>> instanceFeatureMap,
                                                      Map<String, IObjectData> sourceObjectMap,
                                                      Map<String, BigDecimal> allFeatureScoreMap,
                                                      Map<String, BigDecimal> recentAllFeatureScoreMap,
                                                      Map<String, BigDecimal> recentFeatureScoreMap,
                                                      Map<String, String> recentAllFeatureOriValueMap,
                                                      Map<String, String> recentFeatureOriValueMap) {
        Map<String, BigDecimal> featureScoreMap = Maps.newHashMap();
        methodologyInstanceList.stream()
                .filter(methodologyInstance -> {
                    String methodologyInstanceId = methodologyInstance.get(DBRecord.ID, String.class);
                    return CollectionUtils.isNotEmpty(instanceFeatureMap.get(methodologyInstanceId));
                })
                .forEach(methodologyInstance -> {
                    String methodologyInstanceId = methodologyInstance.getId();
                    List<IObjectData> instanceFeatureList = instanceFeatureMap.get(methodologyInstanceId);
                    List<String> objectApiNames = ProfileUtil.methodologyToObjectApiName(methodologyInstance);
                    objectApiNames.forEach(objectApiName -> {
                        String objectId = methodologyInstance.get(ProfileUtil.parseFilterFiled(objectApiName), String.class);
                        instanceFeatureList.forEach(instanceFeature -> {
                            String relatedObjectApiName = instanceFeature.get(InstanceFeatureConstants.RELATED_OBJECT_API_NAME, String.class);
                            String featureObjectApiName = instanceFeature.get(InstanceFeatureConstants.FEATURE_OBJECT_API_NAME, String.class);
                            String featureId = instanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class);
                            if (objectApiName.equals(relatedObjectApiName)) {
                                processNodeObjectMatch(featureScoreMap, methodologyInstanceId, objectApiName, objectId,
                                        featureObjectApiName, featureId, sourceObjectMap, instanceFeature, allFeatureScoreMap,
                                        recentAllFeatureScoreMap, recentFeatureScoreMap, recentAllFeatureOriValueMap,
                                        recentFeatureOriValueMap);
                            } else if (Utils.NEW_OPPORTUNITY_API_NAME.equals(objectApiName)) {
                                String relatedObjectId = methodologyInstance.get(ProfileUtil.parseFilterFiled(relatedObjectApiName), String.class);
                                processOpportunityMatch(featureScoreMap, methodologyInstanceId, objectApiName, objectId,
                                        relatedObjectApiName, relatedObjectId, featureId, recentFeatureScoreMap, recentFeatureOriValueMap);
                            }
                        });
                    });
                });
        return featureScoreMap;
    }

    /**
     * 处理节点对象匹配
     */
    private void processNodeObjectMatch(Map<String, BigDecimal> featureScoreMap, String methodologyInstanceId,
                                        String objectApiName, String objectId, String featureObjectApiName, String featureId,
                                        Map<String, IObjectData> sourceObjectMap, IObjectData instanceFeature, Map<String, BigDecimal> allFeatureScoreMap,
                                        Map<String, BigDecimal> recentAllFeatureScoreMap, Map<String, BigDecimal> recentFeatureScoreMap,
                                        Map<String, String> recentAllFeatureOriValueMap, Map<String, String> recentFeatureOriValueMap) {
        String targetObjectApiName = objectApiName;
        String targetObjectId = objectId;
        if (!objectApiName.equals(featureObjectApiName)) {
            IObjectData sourceObject = sourceObjectMap.get(objectId);
            if (sourceObject != null) {
                String relatedField = instanceFeature.get(InstanceFeatureConstants.RELATED_FIELD, String.class);
                String relatedId = sourceObject.get(relatedField, String.class);
                if (StringUtils.isNotEmpty(relatedId)) {
                    targetObjectApiName = featureObjectApiName;
                    targetObjectId = relatedId;
                }
            }
        }
        String scoreKey = buildFeatureScoreKey(targetObjectApiName, targetObjectId, featureId);
        String resultKey = buildFeatureScoreKey(methodologyInstanceId, objectApiName, objectId, featureId);
        // 处理分数
        if (allFeatureScoreMap.get(scoreKey) != null) {
            featureScoreMap.put(resultKey, allFeatureScoreMap.get(scoreKey));
        }
        // 处理最近一周分数
        if (recentAllFeatureScoreMap != null && recentFeatureScoreMap != null && recentAllFeatureScoreMap.get(scoreKey) != null) {
            recentFeatureScoreMap.put(resultKey, recentAllFeatureScoreMap.get(scoreKey));
        }
        if (recentAllFeatureOriValueMap != null && recentFeatureOriValueMap != null && StringUtils.isNotEmpty(recentAllFeatureOriValueMap.get(scoreKey))) {
            recentFeatureOriValueMap.put(resultKey, recentAllFeatureOriValueMap.get(scoreKey));
        }
    }

    /**
     * 处理商机匹配
     */
    private void processOpportunityMatch(Map<String, BigDecimal> featureScoreMap, String methodologyInstanceId,
                                         String objectApiName, String objectId, String relatedObjectApiName,
                                         String relatedObjectId, String featureId, Map<String, BigDecimal> recentFeatureScoreMap,
                                         Map<String, String> recentFeatureOriValueMap) {
        String nodeKey = buildFeatureScoreKey(methodologyInstanceId, relatedObjectApiName, relatedObjectId, featureId);
        String targetKey = buildFeatureScoreKey(methodologyInstanceId, objectApiName, objectId, featureId);
        // 处理基础分数
        if (featureScoreMap.get(nodeKey) != null) {
            featureScoreMap.put(targetKey, featureScoreMap.get(nodeKey));
        }
        // 处理最近一周分数
        if (recentFeatureScoreMap != null && recentFeatureScoreMap.get(nodeKey) != null) {
            recentFeatureScoreMap.put(targetKey, recentFeatureScoreMap.get(nodeKey));
        }
        // 处理原始值
        if (recentFeatureOriValueMap != null && StringUtils.isNotEmpty(recentFeatureOriValueMap.get(nodeKey))) {
            recentFeatureOriValueMap.put(targetKey, recentFeatureOriValueMap.get(nodeKey));
        }
    }

    /**
     * 构建特征分数键
     */
    private String buildFeatureScoreKey(String... parts) {
        return String.join("_", parts);
    }

    private void buildProfileSearchModel(ProfileSearchModel profileSearchModel, String objectApiName, String objectId) {
        switch (objectApiName) {
            case Utils.LEADS_API_NAME:
                profileSearchModel.setLeadId(objectId);
                break;
            case Utils.ACCOUNT_API_NAME:
                profileSearchModel.setAccountId(objectId);
                break;
            case Utils.NEW_OPPORTUNITY_API_NAME:
                profileSearchModel.setOpportunityId(objectId);
                break;
            default:
                log.error("profileSearchModel error , objectApiName is {}", objectApiName);
                break;
        }
    }

    private Map<String, List<IObjectData>> filterNodeInstanceMap(List<IObjectData> nodeInstanceList, List<String> filterNodeInstanceIds,
                                                                 Map<String, String> methodologyTypeMap, Map<String, String> methodologyInstanceIdMap,
                                                                 Map<String, Integer> methodologyInstanceStageLevelMap) {
        Map<String, List<IObjectData>> methodologyNodeInstanceMap = nodeInstanceList.stream()
                .collect(Collectors.groupingBy(nodeInstance -> nodeInstance.get(NodeInstanceConstants.METHODOLOGY_INSTANCE_ID, String.class)));
        Map<String, List<IObjectData>> filterMethodologyNodeInstanceMap = Maps.newHashMap();
        methodologyNodeInstanceMap.forEach((methodologyInstanceId, methodologyNodeInstanceList) -> {
            String methodologyId = methodologyInstanceIdMap.get(methodologyInstanceId);
            String methodologyType = methodologyTypeMap.get(methodologyId);
            Integer stageLevel = methodologyInstanceStageLevelMap.get(methodologyInstanceId);
            if (MethodologyConstants.Type.PROFILE.getType().equals(methodologyType)) {
                filterMethodologyNodeInstanceMap.put(methodologyInstanceId, methodologyNodeInstanceList);
                filterNodeInstanceIds.addAll(methodologyNodeInstanceList.stream()
                        .map(DBRecord::getId).collect(Collectors.toList()));
                return;
            }
            List<IObjectData> filterNodeInstanceList = Lists.newArrayList();
            List<String> objectApiNameList = methodologyNodeInstanceList.stream()
                    .map(nodeInstance -> nodeInstance.get(NodeInstanceConstants.OBJECT_API_NAME, String.class))
                    .distinct().collect(Collectors.toList());
            objectApiNameList.forEach(objectApiName -> {
                List<IObjectData> objectNodeInstanceList = methodologyNodeInstanceList.stream()
                        .filter(nodeInstance -> objectApiName.equals(nodeInstance.get(NodeInstanceConstants.OBJECT_API_NAME, String.class)))
                        .collect(Collectors.toList());
                IObjectData currentStageNode = objectNodeInstanceList.stream()
                        .filter(nodeInstance -> NodeInstanceConstants.StatusType.PROGRESS.getStatusType()
                                .equals(nodeInstance.get(NodeInstanceConstants.STATUS, String.class))
                                && Objects.equals(stageLevel, nodeInstance.get(NodeInstanceConstants.LEVEL, Integer.class)))
                        .max(Comparator.comparing(nodeInstance -> nodeInstance.get(NodeInstanceConstants.NODE_ORDER, Integer.class, 0)))
                        .orElseGet(() -> objectNodeInstanceList.stream()
                                .filter(nodeInstance -> NodeInstanceConstants.StatusType.COMPLETED.getStatusType()
                                        .equals(nodeInstance.get(NodeInstanceConstants.STATUS, String.class))
                                        && Objects.equals(stageLevel, nodeInstance.get(NodeInstanceConstants.LEVEL, Integer.class)))
                                .max(Comparator.comparing(nodeInstance -> nodeInstance.get(NodeInstanceConstants.NODE_ORDER, Integer.class, 0)))
                                .orElse(null));
                if (currentStageNode == null) {
                    return;
                }
                List<IObjectData> stageNodes = objectNodeInstanceList.stream()
                        .filter(nodeInstance -> Objects.equals(stageLevel, nodeInstance.get(NodeInstanceConstants.LEVEL, Integer.class))
                                && nodeInstance.get(NodeInstanceConstants.NODE_ORDER, Integer.class, 0)
                                <= currentStageNode.get(NodeInstanceConstants.NODE_ORDER, Integer.class, 0))
                        .collect(Collectors.toList());
                List<String> stageNodeIds = stageNodes.stream()
                        .map(nodeInstance -> nodeInstance.get(NodeInstanceConstants.NODE_ID, String.class)).collect(Collectors.toList());
                filterNodeInstanceList.addAll(objectNodeInstanceList.stream()
                        .filter(nodeInstance -> {
                            String treePath = nodeInstance.get(NodeInstanceConstants.TREE_PATH, String.class);
                            return stageNodeIds.stream().anyMatch(treePath::contains);
                        }).collect(Collectors.toList()));
            });
            filterMethodologyNodeInstanceMap.put(methodologyInstanceId, filterNodeInstanceList);
            filterNodeInstanceIds.addAll(filterNodeInstanceList.stream()
                    .map(DBRecord::getId).collect(Collectors.toList()));
        });
        return filterMethodologyNodeInstanceMap;
    }

    private Map<String, IObjectData> findExistProfile(User user, List<IObjectData> methodologyInstanceList,
                                                      Map<String, List<String>> methodologyToObjectApiNameMap) {
        List<IObjectData> existProfileList = Lists.newArrayList();
        List<ProfileSearchModel> profileSearchModelList = Lists.newArrayList();
        methodologyInstanceList.forEach(methodologyInstance -> {
            String methodologyInstanceId = methodologyInstance.getId();
            List<String> objectApiNames = methodologyToObjectApiNameMap.get(methodologyInstanceId);
            ProfileSearchModel profileSearchModel = ProfileSearchModel.builder().methodologyInstanceId(methodologyInstanceId).build();
            objectApiNames.forEach(objectApiName -> {
                String objectId = methodologyInstance.get(ProfileUtil.parseFilterFiled(objectApiName), String.class);
                buildProfileSearchModel(profileSearchModel, objectApiName, objectId);
            });
            profileSearchModelList.add(profileSearchModel);
        });
        profileSearchModelList.forEach(profileSearchModel -> existProfileList.addAll(findProfileBySearchModel(user, profileSearchModel)));
        return existProfileList.stream().collect(Collectors.toMap(
                profile -> profile.get(ProfileConstants.METHODOLOGY_INSTANCE_ID, String.class)
                        + "_" + profile.get(ProfileUtil.reParseProfileField(profile.get(ProfileConstants.TYPE, String.class))),
                profile -> profile,
                (existing, replacement) -> {
                    // 当出现重复键时，保留计算时间新的画像数据
                    Long existingCalcTime = existing.get(ProfileConstants.CALC_TIME, Long.class, 0L);
                    Long replacementCalcTime = replacement.get(ProfileConstants.CALC_TIME, Long.class, 0L);
                    return replacementCalcTime > existingCalcTime ? replacement : existing;
                }
        ));
    }

    private List<IObjectData> findProfileBySearchModel(User user, ProfileSearchModel profileSearchModel) {
        SearchTemplateQueryPlus profileSearchQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(profileSearchQuery)
                .addFilter(Operator.EQ, ProfileConstants.METHODOLOGY_INSTANCE_ID, profileSearchModel.getMethodologyInstanceId())
                .addFilter(Operator.EQ, ProfileConstants.IS_LATEST, "true");
        List<Wheres> whereList = Lists.newArrayList();
        if (StringUtils.isNotBlank(profileSearchModel.getLeadId())) {
            Wheres where = new Wheres();
            where.setFilters(Lists.newArrayList(SearchUtil.filter(ProfileConstants.LEAD_ID, Operator.EQ, profileSearchModel.getLeadId())));
            where.setConnector(Where.CONN.OR.toString());
            whereList.add(where);
        }
        if (StringUtils.isNotBlank(profileSearchModel.getAccountId())) {
            Wheres where = new Wheres();
            where.setFilters(Lists.newArrayList(SearchUtil.filter(ProfileConstants.ACCOUNT_ID, Operator.EQ, profileSearchModel.getAccountId())));
            where.setConnector(Where.CONN.OR.toString());
            whereList.add(where);
        }
        if (StringUtils.isNotBlank(profileSearchModel.getOpportunityId())) {
            Wheres where = new Wheres();
            where.setFilters(Lists.newArrayList(SearchUtil.filter(ProfileConstants.OPPORTUNITY_ID, Operator.EQ, profileSearchModel.getOpportunityId())));
            where.setConnector(Where.CONN.OR.toString());
            whereList.add(where);
        }
        profileSearchQuery.setWheres(whereList);
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.PROFILE, profileSearchQuery).getData();
    }

    public IObjectData buildProfileData(User user, String methodologyInstanceId, String methodologyId, String objectApiName,
                                        String objectId, IObjectData existProfile) {
        IObjectData profile = new ObjectData();
        profile.setId(IdGenerator.get());
        profile.setName(FeatureBaseDataUtils.generateName());
        profile.set(ProfileConstants.METHODOLOGY_INSTANCE_ID, methodologyInstanceId);
        profile.set(ProfileConstants.METHODOLOGY_ID, methodologyId);
        profile.set(ProfileConstants.TYPE, ProfileUtil.parseProfileType(objectApiName));
        profile.set(ProfileUtil.parseFilterFiled(objectApiName), objectId);
        profile.set(ProfileConstants.IS_LATEST, true);
        profile.set(ProfileConstants.CALC_TIME, System.currentTimeMillis());
        profile.setTenantId(user.getTenantId());
        profile.setDescribeApiName(FeatureConstants.PROFILE);
        profile.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        profile.setOwner(Lists.newArrayList(user.getUserId()));
        profile.setName(FeatureBaseDataUtils.generateName());
        return profile;
    }

    /**
     * 生成要保存的画像数据
     */
    private void generateSaveProfileDate(User user, List<IObjectData> addProfileList, List<IObjectData> updateProfileList,
                                         List<IObjectData> addProfileItemScoreList, List<IObjectData> methodologyInstanceList,
                                         Map<String, List<String>> methodologyToObjectApiNameMap, Map<String, String> methodologyTypeMap,
                                         Map<String, List<String>> methodologyFeatureIdMap, List<IObjectData> featureList,
                                         Map<String, BigDecimal> featureScoreMap, Map<String, List<IObjectData>> methodologyWeightMap,
                                         Map<String, List<IObjectData>> methodologyNodeInstanceMap, Map<String, List<IObjectData>> instanceFeatureMap,
                                         Map<String, List<IObjectData>> methodologyNodeWeightMap, Map<String, String> recentFeatureOriValueMap,
                                         Map<String, Integer> methodologyShowLevelMap) {
        if (CollectionUtils.isEmpty(methodologyInstanceList)) {
            return;
        }
        Map<String, Integer> methodologyInstanceStageLevelMap = methodologyInstanceList.stream()
                .collect(Collectors.toMap(DBRecord::getId, methodologyInstance ->
                        methodologyInstance.get(MethodologyInstanceConstants.STAGE_LEVEL, Integer.class, 1)));
        Map<String, IObjectData> existProfileMap = findExistProfile(user, methodologyInstanceList, methodologyToObjectApiNameMap);
        methodologyInstanceList.forEach(methodologyInstance -> {
            String methodologyInstanceId = methodologyInstance.getId();
            String methodologyId = methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_ID, String.class);
            List<String> featureIds = methodologyFeatureIdMap.get(methodologyInstanceId);
            List<String> objectApiNames = methodologyToObjectApiNameMap.get(methodologyInstanceId);
            List<IObjectData> objectNodeInstanceList = methodologyNodeInstanceMap.get(methodologyInstanceId);
            List<IObjectData> instanceFeatureList = instanceFeatureMap.get(methodologyInstanceId);
            List<IObjectData> nodeWeightList = methodologyNodeWeightMap.get(methodologyId);
            Map<String, BigDecimal> nodeWeightMap = CollectionUtils.isEmpty(nodeWeightList) ? Maps.newHashMap() : nodeWeightList.stream()
                    .collect(Collectors.toMap(methodologyNodeWeight -> methodologyNodeWeight.get(FeatureWeightConstants.NODE_ID, String.class) + "_" +
                                    methodologyNodeWeight.get(FeatureWeightConstants.FEATURE_ID, String.class),
                            methodologyNodeWeight -> methodologyNodeWeight.get(FeatureWeightConstants.WEIGHT, BigDecimal.class, BigDecimal.ONE)));
            objectApiNames.forEach(objectApiName -> {
                String objectId = methodologyInstance.get(ProfileUtil.parseFilterFiled(objectApiName), String.class);
                IObjectData existProfile = existProfileMap.get(methodologyInstanceId + "_" + objectId);
                IObjectData profile = buildProfileData(user, methodologyInstanceId, methodologyId, objectApiName, objectId, existProfile);
                // 维度分计算
                dimensionScoreCalc(user, methodologyId, methodologyInstanceId, objectId, featureIds, featureList, objectApiName,
                        featureScoreMap, methodologyWeightMap, methodologyTypeMap, addProfileItemScoreList, profile,
                        recentFeatureOriValueMap);
                // 节点分计算
                nodeScoreCalc(user, methodologyId, methodologyInstanceId, objectApiName, objectId, objectNodeInstanceList,
                        instanceFeatureList, featureList, featureScoreMap, nodeWeightMap, methodologyTypeMap,
                        addProfileItemScoreList, profile, recentFeatureOriValueMap, methodologyInstanceStageLevelMap);
                // 计算画像阶段完成及未完成的任务
                calcProfileItemTask(addProfileItemScoreList, objectNodeInstanceList, methodologyId, methodologyTypeMap, methodologyShowLevelMap);
                // 处理新增或者编辑的画像等数据
                processAddOrUpdateProfile(profile, existProfile, addProfileList, updateProfileList);
            });
        });
    }

    /**
     * 保存画像数据
     */
    public void saveProfileDate(User user, List<IObjectData> addProfileList, List<IObjectData> updateProfileList,
                                List<IObjectData> addProfileItemScoreList) {
    }

    public void sendProfileAdvice(User user, List<IObjectData> addProfileList, List<IObjectData> updateProfileList) {

    }

    /**
     * 维度分计算
     */
    private void dimensionScoreCalc(User user, String methodologyId, String methodologyInstanceId, String objectId, List<String> featureIds,
                                    List<IObjectData> featureList, String objectApiName, Map<String, BigDecimal> featureScoreMap,
                                    Map<String, List<IObjectData>> methodologyWeightMap, Map<String, String> methodologyTypeMap,
                                    List<IObjectData> addProfileItemScoreList, IObjectData profile, Map<String, String> recentFeatureOriValueMap) {
        // 如果状态为方法论则不计算维度分
        String methodologyType = methodologyTypeMap.get(methodologyId);
        if (MethodologyConstants.Type.PROFILE.getType().equals(methodologyType)) {
            return;
        }
        List<IObjectData> methodologyFeatureList = featureList.stream()
                .filter(feature -> featureIds.contains(feature.getId())
                        && (Utils.NEW_OPPORTUNITY_API_NAME.equals(objectApiName)
                        || objectApiName.equals(feature.get(FeatureConstants.MASTER_OBJECT_API_NAME, String.class))))
                .collect(Collectors.toList());
        buildMethodologyFeatureData(methodologyId, methodologyInstanceId, objectApiName, objectId, methodologyFeatureList,
                featureScoreMap, methodologyWeightMap);
        // 按照特征维度进行分组
        Map<String, List<IObjectData>> featureGroupData = groupFeaturesByDimension(methodologyFeatureList);
        // 计算末级维度得分
        Map<String, BigDecimal> dimensionScoreMap = Maps.newHashMap();
        finalDimensionScore(featureGroupData, dimensionScoreMap, ProfileScoreTypeEnum.DIMENSION);
        Map<String, List<String>> dimension1ToSubDimensions = Maps.newHashMap();
        Map<String, List<String>> dimension2ToSubDimensions = Maps.newHashMap();
        // 二级维度得分
        Map<String, BigDecimal> dimension2ScoreMap = Maps.newHashMap();
        // 一级维度得分
        Map<String, BigDecimal> dimension1ScoreMap = Maps.newHashMap();
        // 收集维度层级关系
        collectDimensionHierarchy(featureGroupData, dimension1ToSubDimensions, dimension2ToSubDimensions);
        // 计算二级维度得分 (二级维度得分 = 其下三级维度的平均值)
        dimension2ScoreCalc(dimension2ToSubDimensions, dimensionScoreMap, dimension2ScoreMap);
        // 计算一级维度得分 (一级维度得分 = 其下二级维度的平均值)
        dimension1ScoreCalc(dimension1ToSubDimensions, dimensionScoreMap, dimension2ScoreMap, dimension1ScoreMap);
        // 创建维度得分项
        createDimensionProfileItemScores(user, profile, addProfileItemScoreList, dimension1ScoreMap);
        // 处理变化特征原始值
        Map<String, List<IObjectData>> dimension1ToFeature = methodologyFeatureList.stream()
                .collect(Collectors.groupingBy(feature -> feature.get("feature_dimension_1", String.class)));
        processFeatureOriginalValue(addProfileItemScoreList, methodologyInstanceId, objectApiName, objectId,
                dimension1ToFeature, recentFeatureOriValueMap, ProfileScoreTypeEnum.DIMENSION, methodologyType);
    }

    /**
     * 按照特征维度进行分组
     *
     * @param featureList 特征列表
     * @return 按维度分组的特征Map
     */
    private Map<String, List<IObjectData>> groupFeaturesByDimension(List<IObjectData> featureList) {
        return featureList.stream()
                .collect(Collectors.groupingBy(feature -> {
                    String dimension1 = feature.get("feature_dimension_1", String.class, "");
                    String dimension2 = feature.get("feature_dimension_2", String.class, "");
                    String dimension3 = feature.get("feature_dimension_3", String.class, "");
                    if (!StringUtils.isEmpty(dimension3)) {
                        return dimension1 + "_" + dimension2 + "_" + dimension3;
                    } else if (!StringUtils.isEmpty(dimension2)) {
                        return dimension1 + "_" + dimension2;
                    } else {
                        return dimension1;
                    }
                }));
    }

    private void buildMethodologyFeatureData(String methodologyId, String methodologyInstanceId, String objectApiName, String objectId,
                                             List<IObjectData> methodologyFeatureList, Map<String, BigDecimal> featureScoreMap,
                                             Map<String, List<IObjectData>> methodologyWeightMap) {
        List<IObjectData> methodologyFeatureWeightList = methodologyWeightMap != null ?
                methodologyWeightMap.getOrDefault(methodologyId, Lists.newArrayList()) : Lists.newArrayList();
        Map<String, BigDecimal> methodologyFeatureWeightMap = CollectionUtils.isNotEmpty(methodologyFeatureWeightList) ?
                methodologyFeatureWeightList.stream()
                        .collect(Collectors.toMap(featureWeight -> featureWeight.get(FeatureWeightConstants.FEATURE_ID, String.class),
                                featureWeight -> featureWeight.get(FeatureWeightConstants.WEIGHT, BigDecimal.class))) : Maps.newHashMap();
        methodologyFeatureList.forEach(methodologyFeature -> {
            String featureId = methodologyFeature.getId();
            BigDecimal defaultScore = methodologyFeature.get(FeatureConstants.DEFAULT_SCORE, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal featureWeight = methodologyFeatureWeightMap.get(featureId);
            BigDecimal featureScore = featureScoreMap.getOrDefault(methodologyInstanceId + "_" + objectApiName + "_"
                    + objectId + "_" + featureId, defaultScore);
            methodologyFeature.set(FeatureScoreConstants.SCORE, featureScore == null ? defaultScore : featureScore);
            methodologyFeature.set(FeatureWeightConstants.WEIGHT, featureWeight == null ? BigDecimal.ONE : featureWeight);
        });
    }

    /**
     * 末级维度/节点得分计算
     */
    private void finalDimensionScore(Map<String, List<IObjectData>> featureGroupData, Map<String, BigDecimal> dimensionScoreMap,
                                     ProfileScoreTypeEnum profileScoreTypeEnum) {
        featureGroupData.forEach((dimension, groupFeatureList) -> {
            BigDecimal totalWeight = groupFeatureList.stream()
                    .map(feature -> feature.get(FeatureWeightConstants.WEIGHT, BigDecimal.class, BigDecimal.ONE))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 计算权重总和
            BigDecimal weightedScoreSum = BigDecimal.ZERO;
            for (IObjectData feature : groupFeatureList) {
                String switchField = null;
                if (profileScoreTypeEnum == ProfileScoreTypeEnum.DIMENSION) {
                    switchField = FeatureConstants.SWITCH_FEATURE;
                } else if (profileScoreTypeEnum == ProfileScoreTypeEnum.NODE) {
                    switchField = FeatureConstants.NODE_SWITCH_FEATURE;
                }
                boolean switchFeature = feature.get(switchField, Boolean.class, false);
                BigDecimal weight = feature.get(FeatureWeightConstants.WEIGHT, BigDecimal.class, BigDecimal.ONE);
                BigDecimal score = feature.get(FeatureScoreConstants.SCORE, BigDecimal.class, BigDecimal.ZERO);
                if (switchFeature && score.compareTo(SWITCH_FEATURE_SCORE) > 0) {
                    weightedScoreSum = BigDecimal.ZERO;
                    break;
                }
                // 计算权重占比并累加
                BigDecimal weightRatio = weight.divide(totalWeight, SUB_DECIMAL_PLACES, RoundingMode.HALF_UP);
                weightedScoreSum = weightedScoreSum.add(score.multiply(weightRatio));
            }
            String dimensionKey = dimension.contains("_") ? dimension.substring(dimension.lastIndexOf("_") + 1) : dimension;
            dimensionScoreMap.put(dimensionKey, weightedScoreSum);
        });
    }

    private void collectDimensionHierarchy(Map<String, List<IObjectData>> featureGroupData,
                                           Map<String, List<String>> dimension1ToSubDimensions,
                                           Map<String, List<String>> dimension2ToSubDimensions) {
        featureGroupData.keySet().forEach(dimension -> {
            String[] dimensions = dimension.split("_");
            if (dimensions.length >= 1) {
                String dimension1 = dimensions[0];
                dimension1ToSubDimensions.computeIfAbsent(dimension1, k -> Lists.newArrayList());
                if (dimensions.length >= 2) {
                    String dimension2 = dimensions[1];
                    // 添加dimension2到dimension1的子维度列表
                    if (!dimension1ToSubDimensions.get(dimension1).contains(dimension2)) {
                        dimension1ToSubDimensions.get(dimension1).add(dimension2);
                    }
                    dimension2ToSubDimensions.computeIfAbsent(dimension2, k -> Lists.newArrayList());
                    if (dimensions.length >= 3) {
                        String dimension3 = dimensions[2];
                        // 添加dimension3到dimension2的子维度列表
                        if (!dimension2ToSubDimensions.get(dimension2).contains(dimension3)) {
                            dimension2ToSubDimensions.get(dimension2).add(dimension3);
                        }
                    }
                }
            }
        });
    }

    private void dimension2ScoreCalc(Map<String, List<String>> dimension2ToSubDimensions, Map<String, BigDecimal> dimensionScoreMap,
                                     Map<String, BigDecimal> dimension2ScoreMap) {
        dimension2ToSubDimensions.forEach((dimension2, subDimensions) -> {
            if (CollectionUtils.isEmpty(subDimensions)) {
                dimension2ScoreMap.put(dimension2, dimensionScoreMap.getOrDefault(dimension2, BigDecimal.ZERO));
                return;
            }
            BigDecimal scoreSum = subDimensions.stream()
                    .map(subDimension -> dimensionScoreMap.getOrDefault(subDimension, BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 计算平均分
            BigDecimal avgScore = scoreSum.divide(new BigDecimal(subDimensions.size()), SUB_DECIMAL_PLACES, RoundingMode.HALF_UP);
            dimension2ScoreMap.put(dimension2, avgScore);
        });
    }

    private void dimension1ScoreCalc(Map<String, List<String>> dimension1ToSubDimensions, Map<String, BigDecimal> dimensionScoreMap,
                                     Map<String, BigDecimal> dimension2ScoreMap, Map<String, BigDecimal> dimension1ScoreMap) {
        dimension1ToSubDimensions.forEach((dimension1, subDimensions) -> {
            // 如果没有子维度，直接使用维度自身的分数
            if (CollectionUtils.isEmpty(subDimensions)) {
                dimension1ScoreMap.put(dimension1, dimensionScoreMap.getOrDefault(dimension1, BigDecimal.ZERO)
                        .multiply(BigDecimal.TEN).setScale(ROOT_DECIMAL_PLACES, RoundingMode.HALF_UP));
                return;
            }
            // 有子维度的情况，计算子维度的平均分
            BigDecimal scoreSum = subDimensions.stream()
                    .map(subDimension -> dimension2ScoreMap.getOrDefault(subDimension, BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 计算平均分
            BigDecimal avgScore = scoreSum.divide(new BigDecimal(subDimensions.size()), SUB_DECIMAL_PLACES, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.TEN).setScale(ROOT_DECIMAL_PLACES, RoundingMode.HALF_UP);
            dimension1ScoreMap.put(dimension1, avgScore);
        });
    }

    private void createDimensionProfileItemScores(User user, IObjectData profile,
                                                  List<IObjectData> addProfileItemScoreList,
                                                  Map<String, BigDecimal> dimension1ScoreMap) {
        if (!dimension1ScoreMap.isEmpty()) {
            AtomicReference<BigDecimal> scoreSum = new AtomicReference<>(BigDecimal.ZERO);
            dimension1ScoreMap.forEach((dimension1, score) -> {
                scoreSum.set(scoreSum.get().add(score));
                IObjectData profileItemScore = new ObjectData();
                profileItemScore.setName(FeatureBaseDataUtils.generateName());
                profileItemScore.set(ProfileItemScoreConstants.TYPE, ProfileItemScoreConstants.Type.DIMENSION.getValue());
                profileItemScore.set(ProfileItemScoreConstants.PROFILE_ID, profile.getId());
                profileItemScore.set(ProfileItemScoreConstants.FEATURE_DIMENSION_ID, dimension1);
                profileItemScore.set(ProfileItemScoreConstants.SCORE, score);
                profileItemScore.setTenantId(user.getTenantId());
                profileItemScore.setDescribeApiName(FeatureConstants.PROFILE_ITEM_SCORE);
                profileItemScore.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
                profileItemScore.setOwner(Lists.newArrayList(user.getUserId()));
                profileItemScore.setName(FeatureBaseDataUtils.generateName());
                addProfileItemScoreList.add(profileItemScore);
            });
            BigDecimal avgScore = scoreSum.get().divide(new BigDecimal(dimension1ScoreMap.size()), ROOT_DECIMAL_PLACES, RoundingMode.HALF_UP);
            profile.set(ProfileConstants.INTEGRATED_SCORE, avgScore);
        }
    }

    /**
     * 节点分计算
     */
    private void nodeScoreCalc(User user, String methodologyId, String methodologyInstanceId, String objectApiName, String objectId,
                               List<IObjectData> objectNodeInstanceList, List<IObjectData> instanceFeatureList,
                               List<IObjectData> featureList, Map<String, BigDecimal> featureScoreMap,
                               Map<String, BigDecimal> nodeWeightMap, Map<String, String> methodologyTypeMap,
                               List<IObjectData> addProfileItemScoreList, IObjectData profile,
                               Map<String, String> recentFeatureOriValueMap, Map<String, Integer> methodologyInstanceStageLevelMap) {
        String methodologyType = methodologyTypeMap.get(methodologyId);
        Integer stageLevel = methodologyInstanceStageLevelMap.get(methodologyInstanceId);
        // 获取当前对象的节点实例
        List<IObjectData> nodeInstanceList = getFilteredNodeInstances(objectApiName, objectId, objectNodeInstanceList, methodologyType);
        if (CollectionUtils.isEmpty(nodeInstanceList)) {
            return;
        }
        List<String> nodeInstanceIds = nodeInstanceList.stream().map(DBRecord::getId).collect(Collectors.toList());
        Map<String, IObjectData> featureMap = featureList.stream().collect(Collectors.toMap(DBRecord::getId, feature -> feature));
        // 构建节点特征数据
        Map<String, List<IObjectData>> nodeToFeatureMap = buildNodeFeatureData(instanceFeatureList, nodeInstanceIds, featureMap,
                featureScoreMap, nodeWeightMap, methodologyInstanceId, objectApiName, objectId);
        Map<String, BigDecimal> nodeScoreMap = Maps.newHashMap();
        // 计算末级节点得分
        finalDimensionScore(nodeToFeatureMap, nodeScoreMap, ProfileScoreTypeEnum.NODE);
        // 赋值末级节点得分
        finalNodeScore(nodeInstanceList, nodeScoreMap);
        // 计算根节点得分
        rootNodeScore(nodeInstanceList, stageLevel);
        // 添加一级节点到画像得分项
        createNodeProfileItemScores(user, nodeInstanceList, profile, addProfileItemScoreList, methodologyType, stageLevel);
        // 计算根节点关联的所有特征
        Map<String, List<IObjectData>> stageLevelNodeToFeatureMap = buildStageLevelNodeFeatureData(nodeInstanceList, nodeToFeatureMap, stageLevel);
        // 处理特征原始值
        processFeatureOriginalValue(addProfileItemScoreList, methodologyInstanceId, objectApiName, objectId,
                stageLevelNodeToFeatureMap, recentFeatureOriValueMap, ProfileScoreTypeEnum.NODE, methodologyType);
    }

    private void calcProfileItemTask(List<IObjectData> addProfileItemScoreList, List<IObjectData> objectNodeInstanceList, String methodologyId,
                                     Map<String, String> methodologyTypeMap, Map<String, Integer> methodologyShowLevelMap) {
        if (MethodologyConstants.Type.FLOW.getType().equals(methodologyTypeMap.get(methodologyId))
                || CollectionUtils.isEmpty(addProfileItemScoreList)
                || CollectionUtils.isEmpty(objectNodeInstanceList)) {
            return;
        }
        Integer showLevel = methodologyShowLevelMap.get(methodologyId);
        // 取objectNodeInstanceList的字段level的值等于showLevel的数据
        List<IObjectData> showLevelNodeInstances = objectNodeInstanceList.stream()
                .filter(node -> showLevel != null && showLevel.equals(node.get(NodeInstanceConstants.LEVEL, Integer.class)))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(showLevelNodeInstances)) {
            return;
        }
        // 获取方法论各阶段的完成任务及未完成任务
        addProfileItemScoreList.stream()
                .filter(score -> ProfileItemScoreConstants.Type.NODE.getValue().equals(
                        score.get(ProfileItemScoreConstants.TYPE, String.class)))
                .forEach(score -> {
                    String nodeId = score.get(ProfileItemScoreConstants.NODE_ID, String.class);
                    // showLevelNodeInstances过滤tree_path包含nodeId的数据
                    List<IObjectData> filteredNodeInstances = showLevelNodeInstances.stream()
                            .filter(node -> {
                                String treePath = node.get(NodeInstanceConstants.TREE_PATH, String.class);
                                return treePath != null && treePath.contains(nodeId);
                            })
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filteredNodeInstances)) {
                        // 提取已完成和未完成的任务ID
                        Map<Boolean, List<String>> nodeIdsByStatus = filteredNodeInstances.stream().collect(Collectors.partitioningBy(
                                node -> NodeInstanceConstants.StatusType.COMPLETED.getStatusType().equals(node.get(NodeInstanceConstants.STATUS, String.class)),
                                Collectors.mapping(node -> node.get(NodeInstanceConstants.NODE_ID, String.class),
                                        Collectors.toList())));
                        // 设置已完成和未完成的任务ID
                        score.set(ProfileItemScoreConstants.COMPLETED_TASK_IDS, nodeIdsByStatus.get(true));
                        score.set(ProfileItemScoreConstants.INCOMPLETE_TASK_IDS, nodeIdsByStatus.get(false));
                    }
                });
    }

    private void processFeatureOriginalValue(List<IObjectData> addProfileItemScoreList, String methodologyInstanceId,
                                             String objectApiName, String objectId, Map<String, List<IObjectData>> nodeToFeature,
                                             Map<String, String> recentFeatureOriValueMap,
                                             ProfileScoreTypeEnum profileScoreTypeEnum, String methodologyType) {
        if (CollectionUtils.isEmpty(addProfileItemScoreList)) {
            return;
        }
        if (MethodologyConstants.Type.FLOW.getType().equals(methodologyType)
                && ProfileScoreTypeEnum.NODE.getType().equals(profileScoreTypeEnum.getType())) {
            return;
        }
        addProfileItemScoreList.stream().filter(score -> profileScoreTypeEnum.getType().equals(score.get(ProfileItemScoreConstants.TYPE, String.class)))
                .forEach(score -> {
                    String nodeId = null;
                    if (ProfileScoreTypeEnum.DIMENSION.getType().equals(score.get(ProfileItemScoreConstants.TYPE, String.class))) {
                        nodeId = score.get(ProfileItemScoreConstants.FEATURE_DIMENSION_ID, String.class);
                    } else if (ProfileScoreTypeEnum.NODE.getType().equals(score.get(ProfileItemScoreConstants.TYPE, String.class))) {
                        nodeId = score.get(ProfileItemScoreConstants.NODE_ID, String.class);
                    }
                    List<IObjectData> features = nodeToFeature.get(nodeId);
                    Map<String, String> featureTriggerValuesMap = Maps.newHashMap();
                    if (CollectionUtils.isNotEmpty(features)) {
                        features.forEach(feature -> {
                            String originalValue = recentFeatureOriValueMap.get(buildFeatureScoreKey(methodologyInstanceId,
                                    objectApiName, objectId, feature.getId()));
                            if (StringUtils.isNotEmpty(originalValue)) {
                                featureTriggerValuesMap.put(feature.getId(), originalValue);
                            }
                        });
                    }
                    if (!featureTriggerValuesMap.isEmpty()) {
                        score.set(ProfileItemScoreConstants.FEATURE_TRIGGER_VALUES, JSON.toJSONString(featureTriggerValuesMap));
                    }
                });
    }

    /**
     * 整理要新增或者编辑的画像等数据
     */
    public void processAddOrUpdateProfile(IObjectData profile, IObjectData existProfile,
                                          List<IObjectData> addProfileList, List<IObjectData> updateProfileList) {

    }

    private List<IObjectData> getFilteredNodeInstances(String objectApiName, String objectId, List<IObjectData> methodologyNodeInstanceList, String methodologyType) {
        if (MethodologyConstants.Type.PROFILE.getType().equals(methodologyType)) {
            return methodologyNodeInstanceList;
        }
        return methodologyNodeInstanceList.stream()
                .filter(nodeInstance -> objectApiName.equals(nodeInstance.get(NodeInstanceConstants.OBJECT_API_NAME, String.class))
                        && objectId.equals(nodeInstance.get(NodeInstanceConstants.OBJECT_ID, String.class)))
                .collect(Collectors.toList());
    }

    private void createNodeProfileItemScores(User user, List<IObjectData> nodeInstanceList, IObjectData profile,
                                             List<IObjectData> addProfileItemScoreList, String methodologyType,
                                             Integer stageLevel) {
        AtomicReference<BigDecimal> scoreSum = new AtomicReference<>(BigDecimal.ZERO);
        AtomicInteger nodeCount = new AtomicInteger(0);
        nodeInstanceList.stream()
                .filter(node -> Objects.equals(stageLevel, node.get(NodeInstanceConstants.LEVEL, Integer.class)))
                .forEach(node -> {
                    IObjectData profileItemScore = new ObjectData();
                    profileItemScore.setName(FeatureBaseDataUtils.generateName());
                    profileItemScore.set(ProfileItemScoreConstants.TYPE, ProfileItemScoreConstants.Type.NODE.getValue());
                    profileItemScore.set(ProfileItemScoreConstants.PROFILE_ID, profile.getId());
                    profileItemScore.set(ProfileItemScoreConstants.NODE_ID, node.get(NodeInstanceConstants.NODE_ID, String.class));
                    profileItemScore.set(ProfileItemScoreConstants.SCORE, node.get(FeatureScoreConstants.SCORE, BigDecimal.class, BigDecimal.ZERO));
                    profileItemScore.setTenantId(user.getTenantId());
                    profileItemScore.setDescribeApiName(FeatureConstants.PROFILE_ITEM_SCORE);
                    profileItemScore.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
                    profileItemScore.setOwner(Lists.newArrayList(user.getUserId()));
                    profileItemScore.setName(FeatureBaseDataUtils.generateName());
                    scoreSum.set(scoreSum.get().add(node.get(FeatureScoreConstants.SCORE, BigDecimal.class, BigDecimal.ZERO)));
                    nodeCount.incrementAndGet();
                    addProfileItemScoreList.add(profileItemScore);
                });
        // 计算平均值
        BigDecimal averageScore = nodeCount.get() > 0
                ? scoreSum.get().divide(BigDecimal.valueOf(nodeCount.get()), ROOT_DECIMAL_PLACES, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;
        profile.set(ProfileConstants.PHASE_CUMULATIVE_SCORE, averageScore);
        if (MethodologyConstants.Type.PROFILE.getType().equals(methodologyType)) {
            profile.set(ProfileConstants.INTEGRATED_SCORE, averageScore);
        }
    }

    private Map<String, List<IObjectData>> buildStageLevelNodeFeatureData(List<IObjectData> nodeInstanceList, Map<String, List<IObjectData>> nodeToFeatureMap, Integer stageLevel) {
        Map<String, List<IObjectData>> stageLevelNodeToFeatureMap = Maps.newHashMap();
        List<IObjectData> stageLevelNodes = nodeInstanceList.stream()
                .filter(node -> stageLevel != null && stageLevel.equals(node.get(NodeInstanceConstants.LEVEL, Integer.class)))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stageLevelNodes)) {
            return stageLevelNodeToFeatureMap;
        }
        for (IObjectData stageLevelNode : stageLevelNodes) {
            String stageLevelNodeId = stageLevelNode.get(NodeInstanceConstants.NODE_ID, String.class);
            Set<String> relatedNodeIds = nodeInstanceList.stream()
                    .filter(node -> {
                        String treePath = node.get(NodeInstanceConstants.TREE_PATH, String.class);
                        return treePath != null && treePath.contains(stageLevelNodeId);
                    })
                    .map(node -> node.get(NodeInstanceConstants.NODE_ID, String.class))
                    .collect(Collectors.toSet());
            relatedNodeIds.add(stageLevelNodeId);
            List<IObjectData> relatedFeatures = relatedNodeIds.stream()
                    .filter(nodeToFeatureMap::containsKey)
                    .flatMap(nodeId -> nodeToFeatureMap.get(nodeId).stream())
                    .collect(Collectors.toList());
            stageLevelNodeToFeatureMap.put(stageLevelNodeId, relatedFeatures);
        }
        return stageLevelNodeToFeatureMap;
    }

    private Map<String, List<IObjectData>> buildNodeFeatureData(List<IObjectData> instanceFeatureList, List<String> nodeInstanceIds,
                                                                Map<String, IObjectData> featureMap, Map<String, BigDecimal> featureScoreMap,
                                                                Map<String, BigDecimal> nodeWeightMap, String methodologyInstanceId,
                                                                String objectApiName, String objectId) {
        Map<String, List<IObjectData>> nodeToFeatureMap = Maps.newHashMap();
        instanceFeatureList.stream()
                .filter(instanceFeature -> nodeInstanceIds.contains(instanceFeature.get(InstanceFeatureConstants.NODE_INSTANCE_ID, String.class)))
                .forEach(instanceFeature -> {
                    String nodeId = instanceFeature.get(InstanceFeatureConstants.NODE_ID, String.class);
                    String featureId = instanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class);
                    IObjectData feature = featureMap.get(featureId);
                    if (feature == null) {
                        return;
                    }
                    BigDecimal featureWeight = nodeWeightMap.getOrDefault(nodeId + "_" + featureId, BigDecimal.ONE);
                    BigDecimal defaultScore = feature.get(FeatureConstants.DEFAULT_SCORE, BigDecimal.class, BigDecimal.ZERO);
                    BigDecimal featureScore = featureScoreMap.getOrDefault(methodologyInstanceId
                            + "_" + objectApiName + "_" + objectId + "_" + featureId, defaultScore);
                    feature.set(FeatureScoreConstants.SCORE, featureScore);
                    feature.set(FeatureWeightConstants.WEIGHT, featureWeight);
                    nodeToFeatureMap.computeIfAbsent(nodeId, k -> Lists.newArrayList()).add(feature);
                });
        return nodeToFeatureMap;
    }

    private void finalNodeScore(List<IObjectData> nodeInstanceList, Map<String, BigDecimal> nodeScoreMap) {
        nodeInstanceList.forEach(nodeInstance -> {
            String nodeId = nodeInstance.get(NodeInstanceConstants.NODE_ID, String.class);
            nodeInstance.set(FeatureScoreConstants.SCORE, nodeScoreMap.getOrDefault(nodeId, BigDecimal.ZERO));
        });
    }

    private void rootNodeScore(List<IObjectData> nodeInstanceList, Integer stageLevel) {
        // 构建节点树结构
        Map<String, List<IObjectData>> parentToChildrenMap = Maps.newHashMap();
        // 将节点按照父节点ID分组，并创建节点ID到节点的映射
        for (IObjectData node : nodeInstanceList) {
            String parentId = node.get(NodeInstanceConstants.PARENT_ID, String.class);
            if (parentId != null) {
                parentToChildrenMap.computeIfAbsent(parentId, k -> Lists.newArrayList()).add(node);
            }
        }
        // 找出所有根节点
        List<IObjectData> rootNodes = nodeInstanceList.stream()
                .filter(node -> Objects.equals(stageLevel, node.get(NodeInstanceConstants.LEVEL, Integer.class)))
                .collect(Collectors.toList());
        // 按照层级从底向上计算分数
        List<IObjectData> sortedNodes = nodeInstanceList.stream()
                .sorted(Comparator.comparing(node -> node.get(NodeInstanceConstants.LEVEL, Integer.class), Comparator.reverseOrder()))
                .collect(Collectors.toList());
        // 从叶子节点开始，向上计算每个父节点的分数
        for (IObjectData node : sortedNodes) {
            String nodeId = node.get(NodeInstanceConstants.NODE_ID, String.class);
            List<IObjectData> children = parentToChildrenMap.get(nodeId);
            // 如果有子节点，计算当前节点的分数为子节点分数的平均值
            if (CollectionUtils.isNotEmpty(children)) {
                BigDecimal totalScore = BigDecimal.ZERO;
                for (IObjectData child : children) {
                    BigDecimal childScore = child.get(FeatureScoreConstants.SCORE, BigDecimal.class, BigDecimal.ZERO);
                    totalScore = totalScore.add(childScore);
                }
                // 计算平均分
                BigDecimal avgScore = totalScore.divide(new BigDecimal(children.size()), SUB_DECIMAL_PLACES, RoundingMode.HALF_UP);
                // 设置当前节点的分数
                node.set(FeatureScoreConstants.SCORE, avgScore);
            }
        }
        // 确保根节点的分数已经计算完成
        for (IObjectData rootNode : rootNodes) {
            String rootId = rootNode.get(NodeInstanceConstants.NODE_ID, String.class);
            if (rootNode.get(FeatureScoreConstants.SCORE) == null) {
                List<IObjectData> children = parentToChildrenMap.get(rootId);
                if (children != null && !children.isEmpty()) {
                    BigDecimal totalScore = BigDecimal.ZERO;
                    for (IObjectData child : children) {
                        BigDecimal childScore = child.get(FeatureScoreConstants.SCORE, BigDecimal.class, BigDecimal.ZERO);
                        totalScore = totalScore.add(childScore);
                    }
                    BigDecimal avgScore = totalScore.divide(new BigDecimal(children.size()), SUB_DECIMAL_PLACES, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.TEN).setScale(ROOT_DECIMAL_PLACES, RoundingMode.HALF_UP);
                    rootNode.set(FeatureScoreConstants.SCORE, avgScore);
                } else {
                    rootNode.set(FeatureScoreConstants.SCORE, BigDecimal.ZERO);
                }
            } else {
                rootNode.set(FeatureScoreConstants.SCORE, rootNode.get(FeatureScoreConstants.SCORE, BigDecimal.class)
                        .multiply(BigDecimal.TEN).setScale(ROOT_DECIMAL_PLACES, RoundingMode.HALF_UP));
            }
        }
    }

    public void addProfileQuotaLimit(User user, List<IObjectData> addProfileList) {
        RLock rLock = redissonService.tryLock(2, 2, TimeUnit.MINUTES, String.format("feature_profile_quota_lock_%s", user.getTenantId()));
        if (rLock == null) {
            log.warn("failed to try lock, tenantId: {}", user.getTenantId());
            return;
        }
        try {
            Map<String, List<IObjectData>> noProfileDataMap = getNoProfileDataList(user, addProfileList);
            List<IObjectData> hasProfileList = noProfileDataMap.get("hasProfile");
            List<IObjectData> noProfileList = noProfileDataMap.get("noProfile");
            int maxCount = profileCommonService.getProfileCount(user.getTenantId());
            int usedProfileCount = profileCommonService.getUsedProfileCount(user.getTenantId());
            int remainProfileCount = Math.max(maxCount - usedProfileCount, 0);
            serviceFacade.bulkSaveObjectData(hasProfileList, user);
            if (remainProfileCount >= noProfileList.size()) {
                serviceFacade.bulkSaveObjectData(noProfileList, user);
                int consumedCount = recordProfileConsumption(user, noProfileList);
                remainProfileCount -= consumedCount;
            } else {
                List<IObjectData> limitedProfileList = noProfileList.subList(0, remainProfileCount);
                serviceFacade.bulkSaveObjectData(limitedProfileList, user);
                int consumedCount = recordProfileConsumption(user, limitedProfileList);
                remainProfileCount -= consumedCount;
            }
            if (Math.max(maxCount - usedProfileCount, 0) != remainProfileCount) {
                OverviewChangeMessage overviewChangeMessage = new OverviewChangeMessage();
                overviewChangeMessage.setTenantId(user.getTenantId());
                overviewChangeMessage.setParaKey(PROFILE_QUOTA_KEY);
                int usedCount = maxCount - remainProfileCount;
                overviewChangeMessage.setUsedValue(String.valueOf(usedCount));
                Message message = new Message();
                message.setTopic("overview-change-topic");
                message.setTags("overview");
                message.setBody(JSON.toJSONBytes(overviewChangeMessage));
                producer.send(message);
                log.info("send overview change success, tenantId: {}, usedCount: {}", user.getTenantId(), usedCount);
            }
        } catch (Exception e) {
            log.error("failed to add profile quota limit, tenantId: {}", user.getTenantId());
            throw new RuntimeException(e);
        } finally {
            redissonService.unlock(rLock);
        }
    }

    public Map<String, List<IObjectData>> getNoProfileDataList(User user, List<IObjectData> addProfileList) {
        if (CollectionUtils.isEmpty(addProfileList)) {
            return Maps.newHashMap();
        }
        Set<String> leadsSet = Sets.newHashSet();
        Set<String> accountsSet = Sets.newHashSet();
        Set<String> opportunitiesSet = Sets.newHashSet();
        addProfileList.forEach(profile -> {
            String leadId = profile.get(ProfileConstants.LEAD_ID, String.class);
            if (StringUtils.isNotBlank(leadId)) {
                leadsSet.add(leadId);
            }
            String accountId = profile.get(ProfileConstants.ACCOUNT_ID, String.class);
            if (StringUtils.isNotBlank(accountId)) {
                accountsSet.add(accountId);
            }
            String opportunityId = profile.get(ProfileConstants.OPPORTUNITY_ID, String.class);
            if (StringUtils.isNotBlank(opportunityId)) {
                opportunitiesSet.add(opportunityId);
            }
        });
        // 构建查询条件
        SearchTemplateQueryPlus profileSearchQuery = SearchUtil.buildBaseSearchQuery();
        List<Wheres> whereList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(leadsSet)) {
            whereList.add(buildWhere(ProfileConstants.LEAD_ID, leadsSet));
        }
        if (CollectionUtils.isNotEmpty(accountsSet)) {
            whereList.add(buildWhere(ProfileConstants.ACCOUNT_ID, accountsSet));
        }
        if (CollectionUtils.isNotEmpty(opportunitiesSet)) {
            whereList.add(buildWhere(ProfileConstants.OPPORTUNITY_ID, opportunitiesSet));
        }
        profileSearchQuery.setWheres(whereList);
        List<IObjectData> profileList = serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.PROFILE, profileSearchQuery).getData();
        Set<String> existingProfileKeys = profileList.stream()
                .map(this::buildProfileKey)
                .collect(Collectors.toSet());
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        List<IObjectData> hasProfileList = Lists.newArrayList();
        List<IObjectData> noProfileList = Lists.newArrayList();
        addProfileList.forEach(addProfile -> {
            String profileKey = buildProfileKey(addProfile);
            if (existingProfileKeys.contains(profileKey)) {
                hasProfileList.add(addProfile);
            } else {
                noProfileList.add(addProfile);
            }
        });
        result.put("hasProfile", hasProfileList);
        result.put("noProfile", noProfileList);
        return result;
    }

    /**
     * 构建查询条件
     */
    private Wheres buildWhere(String field, Set<String> values) {
        Wheres where = new Wheres();
        where.setFilters(Lists.newArrayList(SearchUtil.filter(field, Operator.IN, values)));
        where.setConnector(Where.CONN.OR.toString());
        return where;
    }

    /**
     * 构建画像唯一标识
     */
    private String buildProfileKey(IObjectData profile) {
        String leadId = profile.get(ProfileConstants.LEAD_ID, String.class);
        String accountId = profile.get(ProfileConstants.ACCOUNT_ID, String.class);
        String opportunityId = profile.get(ProfileConstants.OPPORTUNITY_ID, String.class);
        if (StringUtils.isNotBlank(leadId)) {
            return "LEAD_" + leadId;
        }
        if (StringUtils.isNotBlank(accountId)) {
            return "ACCOUNT_" + accountId;
        }
        if (StringUtils.isNotBlank(opportunityId)) {
            return "OPPORTUNITY_" + opportunityId;
        }
        return "";
    }

    public int recordProfileConsumption(User user, List<IObjectData> profileList) {
        if (CollectionUtils.isEmpty(profileList)) {
            return 0;
        }
        Map<String, String> idToNameMap = Maps.newHashMap();
        List<String> leadIds = Lists.newArrayList();
        List<String> accountIds = Lists.newArrayList();
        List<String> opportunityIds = Lists.newArrayList();
        for (IObjectData profile : profileList) {
            String leadId = profile.get(ProfileConstants.LEAD_ID, String.class);
            if (StringUtils.isNotEmpty(leadId)) {
                leadIds.add(leadId);
            }
            String accountId = profile.get(ProfileConstants.ACCOUNT_ID, String.class);
            if (StringUtils.isNotEmpty(accountId)) {
                accountIds.add(accountId);
            }
            String opportunityId = profile.get(ProfileConstants.OPPORTUNITY_ID, String.class);
            if (StringUtils.isNotEmpty(opportunityId)) {
                opportunityIds.add(opportunityId);
            }
        }
        if (CollectionUtils.isNotEmpty(leadIds)) {
            List<IObjectData> leadList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), leadIds, Utils.LEADS_API_NAME);
            leadList.forEach(lead -> idToNameMap.put(lead.getId(), lead.getName()));
        }
        if (CollectionUtils.isNotEmpty(accountIds)) {
            List<IObjectData> accountList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), accountIds, Utils.ACCOUNT_API_NAME);
            accountList.forEach(account -> idToNameMap.put(account.getId(), account.getName()));
        }
        if (CollectionUtils.isNotEmpty(opportunityIds)) {
            List<IObjectData> opportunityList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), opportunityIds, Utils.NEW_OPPORTUNITY_API_NAME);
            opportunityList.forEach(opportunity -> idToNameMap.put(opportunity.getId(), opportunity.getName()));
        }
        Set<String> uniqueKeys = Sets.newHashSet();
        List<IObjectData> uniqueRecordList = Lists.newArrayList();
        for (IObjectData profile : profileList) {
            String type = profile.get(ProfileConstants.TYPE, String.class);
            String objectApiName = ProfileUtil.reParseProfileType(type);
            String objectId = profile.get(ProfileUtil.reParseProfileField(type), String.class);
            String uniqueKey = objectApiName + "_" + objectId;
            if (!uniqueKeys.contains(uniqueKey)) {
                IObjectData objectData = new ObjectData();
                objectData.set(SalesCoachRecordConstants.OBJECT_API_NAME, objectApiName);
                objectData.set(SalesCoachRecordConstants.OBJECT_ID, objectId);
                objectData.set(SalesCoachRecordConstants.OBJECT_NAME, idToNameMap.get(objectId));
                objectData.set(SalesCoachRecordConstants.USED_COUNT, 1);
                objectData.setTenantId(user.getTenantId());
                objectData.setDescribeApiName(FeatureConstants.SALES_COACH_RECORD);
                objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
                objectData.setOwner(Lists.newArrayList(user.getUserId()));
                objectData.setName(FeatureBaseDataUtils.generateName());
                uniqueKeys.add(uniqueKey);
                uniqueRecordList.add(objectData);
            }
        }
        serviceFacade.bulkSaveObjectData(uniqueRecordList, user);
        return uniqueRecordList.size();
    }
}
