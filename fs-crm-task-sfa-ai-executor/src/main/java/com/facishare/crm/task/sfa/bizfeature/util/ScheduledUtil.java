package com.facishare.crm.task.sfa.bizfeature.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureTaskConstants;
import com.facishare.crm.task.sfa.common.util.DateUtils;
import com.facishare.crm.task.sfa.services.rebate.util.ExceptionUtils;

import java.util.Calendar;
import java.util.Date;

public class ScheduledUtil {

    /**
     * 是否周期性执行日期执行
     *
     * @return
     */
    public static boolean isScheduledExecDate(String scheduledInfo) {

        JSONObject jsonObject = ExceptionUtils.trySupplier(() -> JSON.parseObject(scheduledInfo));
        String type = jsonObject.getString("type");
        if (FeatureTaskConstants.ScheduledType.DAY.getValue().equals(type)) {
            return true;
        }

        int month = jsonObject.getIntValue("month");
        int day = jsonObject.getIntValue("day");

        Date execDate = new Date();
        Calendar.getInstance();
         if (FeatureTaskConstants.ScheduledType.WEEK.getValue().equals(type)) {
            execDate = DateUtils.getWeekOfDates(day);
        } else if (FeatureTaskConstants.ScheduledType.MONTH.getValue().equals(type)) {
            execDate = DateUtils.getMonthOfDates(day);
        } else if (FeatureTaskConstants.ScheduledType.QUARTER.getValue().equals(type)) {
            execDate = DateUtils.getQuarterOfDates(month, day);
        } else if (FeatureTaskConstants.ScheduledType.YEAR.getValue().equals(type)) {
            execDate = DateUtils.getYearOfDates(month, day);
        }

        return DateUtils.isSameDate(new Date(), execDate);

    }
}
