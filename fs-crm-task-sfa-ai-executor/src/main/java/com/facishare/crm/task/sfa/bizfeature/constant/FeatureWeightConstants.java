package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 特征权重
 */
public interface FeatureWeightConstants {
    /**
     * 特征
     */
    String FEATURE_ID = "feature_id";
    /**
     * 权重
     */
    String WEIGHT = "weight";
    /**
     * 节点流程
     */
    String NODE_ID = "node_id";
    /**
     * 方法论
     */
    String METHODOLOGY_ID = "methodology_id";
    /**
     * 类型
     */
    String TYPE = "type";

    enum Type {
        /**
         * 流程
         */
        FLOW("flow"),
        /**
         * 维度
         */
        DIMENSION("dimension");

        private final String value;

        public String getValue() {
            return value;
        }

        Type(String value) {
            this.value = value;
        }
    }
}