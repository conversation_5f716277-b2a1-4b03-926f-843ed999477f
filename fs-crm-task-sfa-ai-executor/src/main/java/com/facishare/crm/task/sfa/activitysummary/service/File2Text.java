package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.enums.FileTypeEnum;
import com.facishare.paas.metadata.api.IObjectData;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/17 10:44
 * @description: 文件转换接口
 */
public interface File2Text {

    /**
     * 判断是否支持该文件类型
     * @param fileType 文件类型
     * @return true-支持 false-不支持
     */
    boolean support(FileTypeEnum fileType);

    /**
     * 执行文件转换
     * @param message 消息体
     */
    void execute(ActivityMessage message, IObjectData activityData);
}
