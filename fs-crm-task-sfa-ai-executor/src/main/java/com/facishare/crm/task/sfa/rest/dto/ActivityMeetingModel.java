package com.facishare.crm.task.sfa.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

public interface ActivityMeetingModel {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UpdateByScheduleArg {
        private String objectId;
        private Long startTime;
        private Long overTime;
    }

    @Data
    class Result<T> {
        private String code;
        private String errorMsg;
        private T data;

        public static <T> Result<T> error(String errorMsg) {
            Result<T> result = new Result<>();
            result.setCode("500");
            result.setErrorMsg(errorMsg);
            return result;
        }

        public static <T> Result<T> success(T data) {
            Result<T> result = new Result<>();
            result.setCode("200");
            result.setData(data);
            return result;
        }

        public boolean isSuccess() {
            return "200".equals(code);
        }
    }

}
