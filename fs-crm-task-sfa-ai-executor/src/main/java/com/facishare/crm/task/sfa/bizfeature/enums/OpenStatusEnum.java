package com.facishare.crm.task.sfa.bizfeature.enums;

import java.util.Optional;

/**
 * 应收开关状态
 */
public enum OpenStatusEnum {
    NOT_OPEN("0", "未开启"),
    OPENED("1", "开启成功"),
    OPENING("2", "开启中"),
    FAILED("3", "开启失败");

    private String status;
    private String message;

    OpenStatusEnum(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }
}
