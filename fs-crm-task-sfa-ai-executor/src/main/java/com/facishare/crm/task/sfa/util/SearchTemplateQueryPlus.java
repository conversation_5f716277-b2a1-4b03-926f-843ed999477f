package com.facishare.crm.task.sfa.util;

import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.List;

public class SearchTemplateQueryPlus extends SearchTemplateQuery {

    public SearchTemplateQueryPlus addFilter(String fieldName, Operator operator, List<String> fieldValues){
        IFilter filter = getFilter(fieldName, operator, fieldValues);
        getFilters().add(filter);
        return this;
    }

    public SearchTemplateQueryPlus addSubFilter(String fieldName, Operator operator, List<String> fieldValues){
        IFilter filter = getFilter(fieldName, operator, fieldValues);
        filter.setValueType(10);
        getFilters().add(filter);
        return this;
    }

    public SearchTemplateQueryPlus addFilter(String fieldName, Operator operator, String fieldValue){
        return addFilter(fieldName,operator, Lists.newArrayList(fieldValue));
    }

    public SearchTemplateQueryPlus addWheres(List<IFilter> filters){
        List<Wheres> wheres = getWheres();
        Wheres where = new Wheres();
        where.setFilters(filters);
        wheres.add(where);
        return this;
    }

    public SearchTemplateQueryPlus addWheres(IFilter filter){
        return addWheres(Lists.newArrayList(filter));
    }

    public SearchTemplateQueryPlus addWheres(String fieldName, Operator operator, List<String> fieldValues){
        return addWheres(getFilter(fieldName,operator,fieldValues));
    }

    public SearchTemplateQueryPlus addWheres(String fieldName, Operator operator, String fieldValue){
        return addWheres(getFilter(fieldName,operator,fieldValue));
    }

    public static IFilter getFilter(String fieldName, Operator operator, List<String> fieldValues) {
        Filter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(operator);
        filter.setFieldValues(fieldValues);
        return filter;
    }
    public static IFilter getFilter(String fieldName, Operator operator, String fieldValue) {
        return getFilter(fieldName,operator,Lists.newArrayList(fieldValue));
    }

}
