package com.facishare.crm.task.sfa.bizfeature.model;

import java.util.List;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProfileScoreModel {

    String tenantId;

    String objectApiName;

    String objectId;

    String userId;

    String methodologyInstanceId;

    List<String> methodologyInstanceIds;

    public String toJSONString() {
        return JSON.toJSONString(this);
    }
}
