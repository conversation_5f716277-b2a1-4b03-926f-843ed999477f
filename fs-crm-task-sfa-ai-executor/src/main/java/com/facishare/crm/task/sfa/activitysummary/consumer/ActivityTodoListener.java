package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityTodoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Component
@Slf4j
public class ActivityTodoListener extends AbstractActivityCommonListener{

    @Resource
    private ActivityTodoService activityTodoService;

    @Override
    String getSection() {
        return "sfa-ai-activity-todo";
    }

    @Override
    void consume(ActivityMessage activityMessage) {
        log.info("activityTodo mq:{}", activityMessage);
        if (ObjectUtils.isEmpty(activityMessage.getTenantId()) || ObjectUtils.isEmpty(activityMessage.getObjectId())) {
            return;
        }
        activityTodoService.execute(activityMessage, false, false);
    }

}
