package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.NodeInstanceConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NodeInstanceDao {

    @Autowired
    private ServiceFacade serviceFacade;

    /**
     * 查询进行中的阶段实例
     */
    public List<IObjectData> fetchNodeInstancesByMethodologyAndObjectIdWithOrder(User user, IObjectData methodology,String methodologyInstanceId, String objectId) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, NodeInstanceConstants.METHODOLOGY_INSTANCE_ID, methodologyInstanceId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, NodeInstanceConstants.LEVEL, methodology.get(MethodologyConstants.STAGE_LEVEL, String.class));
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, NodeInstanceConstants.OBJECT_ID, objectId);
        query.setOrders(Lists.newArrayList(new OrderBy(NodeInstanceConstants.NODE_ORDER, true)));

        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.NODE_INSTANCE, query).getData();
    }

    public List<IObjectData> fetchNodeInstancesByMethodologyInstanceId(User user, String methodologyInstanceId) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, NodeInstanceConstants.METHODOLOGY_INSTANCE_ID, methodologyInstanceId);
        //SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, NodeInstanceConstants.LEVEL, methodology.get(MethodologyConstants.LEVEL, String.class));
        //SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, NodeInstanceConstants.NODE_ID, nodeId);

        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.NODE_INSTANCE, query).getData();
    }
}
