package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.activity.enums.AttendeesInsightType;
import com.facishare.crm.task.sfa.activitysummary.constant.AttendeesInsightConstants;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SOPCoverageInsightHandler extends AbstractInsightHandler<AttendeesInsightModel.InsightResult> {

    private static final String PROMPT = "prompt_attendee_insight_sop_coverage";

    private List<IObjectData> suggestions;

    @Override
    public String getInsightType() {
        return AttendeesInsightType.SOP_COVERAGE;
    }

    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        String tenantId = attendeesInsightMessage.getTenantId();
        String activeRecordId = attendeesInsightMessage.getActiveRecordId();
        suggestions = this.querySuggestion(tenantId, attendeesInsightMessage.getExtendData().getActiveRecord());
        int suggestionsCount = suggestions.size();
        List<IObjectData> questionList = attendeesInsightMessage.getExtendData().getQuestionList();
        if (Safes.isEmpty(questionList)) {
            log.warn("No question list found for activeRecordId: {}", attendeesInsightMessage.getActiveRecordId());
            return;
        }
        if (Safes.isEmpty(suggestions)) {
            log.warn("No suggestions found for activeRecordId: {}", activeRecordId);
            return;
        }
        String completionRst = completion(attendeesInsightMessage, PROMPT);
        completionRst = fixJSONFormatService.fixJSON(completionRst, true);
        List<AttendeesInsightModel.InsightSOPCoverageResult> insightResultList = fixJSONFormatService.getDataListFixedInvalidJSON(User.systemUser(tenantId), "", completionRst, AttendeesInsightModel.InsightSOPCoverageResult.class, true);

        AttendeesInsightModel.AttendeesInsightExtendData extendData = attendeesInsightMessage.getExtendData();
        Map<String, String> userNameMap = getUserNameMap(extendData.getActivityUserList());
        List<IObjectData> addRecordList = Lists.newArrayList();
        for (AttendeesInsightModel.InsightSOPCoverageResult insightSOPCoverageResult : insightResultList) {
            String userName = insightSOPCoverageResult.getUserName();
            String userId = userNameMap.get(userName);
            IObjectData insightRecord = super.buildBaseInsightRecord(tenantId, activeRecordId);
            insightRecord.set(AttendeesInsightConstants.ACTIVITY_USER_ID, userId);
            insightRecord.set(AttendeesInsightConstants.INSIGHT_RESULT, JSON.toJSONString(insightSOPCoverageResult));
            if (insightSOPCoverageResult.getConfirmedCount() != null) {
                BigDecimal coverage = new BigDecimal(insightSOPCoverageResult.getConfirmedCount()).divide(new BigDecimal(suggestionsCount), 2, RoundingMode.HALF_UP);
                if (BigDecimal.ONE.compareTo(coverage) < 0) {
                    coverage = BigDecimal.ONE;
                }
                insightRecord.set(AttendeesInsightConstants.SOP_COVERAGE_RATE, coverage.multiply(BigDecimal.valueOf(100)));
            }
            addRecordList.add(insightRecord);
        }

        if (!addRecordList.isEmpty()) {
            serviceFacade.bulkSaveObjectData(addRecordList, User.systemUser(tenantId));
        }
    }


    @Override
    protected String getCorpus(AttendeesInsightModel.AttendeesInsightExtendData extendData) {
        return super.getQuestionCorpus(extendData);
    }

    @Override
    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return super.getOurSideNames(insightMessage);
    }

    @Override
    protected void addSceneVariables(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage,
                                     Map<String, Object> sceneVariables) {
        StringBuilder sb = new StringBuilder();
        for (IObjectData suggestion : suggestions) {
            String library = suggestion.get("library_id__r", String.class);
            if (Safes.isNotEmpty(library)) {
                sb.append(library).append("\n");
            }
        }
        sceneVariables.put("suggestions", sb.toString());
    }

    public List<IObjectData> querySuggestion(String tenantId, IObjectData activeRecord) {
        String accountId = activeRecord.get("account_id", String.class);
        if (Safes.isEmpty(accountId)) {
            return Collections.emptyList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), "account_id", accountId) ;
        SearchUtil.fillFilterEq(query.getFilters(), "question_type", "2");
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        query.setLimit(AppFrameworkConfig.getMaxQueryLimit());

        List<IObjectData> dataList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), CommonConstant.ACTIVITY_QUESTION_API_NAME, query).getData();
        if (!dataList.isEmpty()) {
            IObjectDescribe describe = serviceFacade.findObject(tenantId, CommonConstant.ACTIVITY_QUESTION_API_NAME);
            serviceFacade.fillObjectDataWithRefObject(describe, dataList, User.systemUser(tenantId));
        }
        return dataList;
    }
}
