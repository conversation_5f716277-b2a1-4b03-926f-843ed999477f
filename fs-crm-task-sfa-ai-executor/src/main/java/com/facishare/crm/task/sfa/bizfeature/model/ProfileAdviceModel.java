package com.facishare.crm.task.sfa.bizfeature.model;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProfileAdviceModel {

    String tenantId;

    String profileId;

    String refreshType;

    String receiverId;

    IObjectData profile;

    String objectId;

    String objectDescribeApiName;

    public String toJSONString() {
        return JSON.toJSONString(this);
    }
}
