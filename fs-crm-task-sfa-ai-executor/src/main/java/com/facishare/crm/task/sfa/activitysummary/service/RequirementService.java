package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.task.sfa.activitysummary.constant.ActivityQuestionConstants;
import com.facishare.crm.task.sfa.activitysummary.model.InteractionModel;
import com.facishare.crm.task.sfa.activitysummary.model.RequirementModel;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureValueProducer;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.common.constants.RequirementConstants;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.util.AccountPathUtil;
import com.facishare.crm.task.sfa.util.InheritRecordUtil;
import com.facishare.crm.task.sfa.util.LogUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.i18n.client.I18nClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RequirementService {

    @Autowired
    protected ServiceFacade serviceFacade;
    @Resource
    private ActivityMongoDao activityMongoDao;
    @Autowired
    public ActivitySummaryService activitySummaryService;
    @Autowired
    private AIKnowledgeBaseService aiKnowledgeBaseService;
    @Resource
    private FeatureValueProducer featureValueProducer;

    public static Map<String, String> selectFieldMap = Maps.newHashMap(RequirementConstants.Field.category, "category",
            RequirementConstants.Field.four_quadrant, "fourQuadrant",
            RequirementConstants.Field.kano, "kano",
            RequirementConstants.Field.assessment_dimension, "assessmentDimension",
            RequirementConstants.Field.response_status, "responseStatus",
            RequirementConstants.Field.clearance_status, "clearanceStatus");

    public void execute(ActivityMessage message) {
        log.warn("RequirementService execute message {}", JSONObject.toJSONString(message));
        try {
            RequirementModel.Arg arg = handleParam(message);
            String userId = CommonConstant.SUPER_USER;
            if (ObjectUtils.isNotEmpty(arg.getUserId())) {
                userId = arg.getUserId();
            }
            User user = new User(arg.getTenantId(), userId);
            IActionContext actionContext = ActionContextExt.of(user).getContext();
            actionContext.put(ActionContextKey.SEARCH_RICH_TEXT_EXTRA, true);
            IObjectData activeRecordData = serviceFacade.findObjectData(actionContext, arg.getActiveRecordId(), CommonConstant.ACTIVE_RECORD_API_NAME);
            if (ObjectUtils.isEmpty(activeRecordData)) {
                log.warn("RequirementService execute activeRecordData is null tenantId:{},activeRecordId:{}", user.getTenantId(), arg.getActiveRecordId());
                return;
            }
            String accountId = InheritRecordUtil.getStringValue(activeRecordData, CommonConstant.ACCOUNT_ID, "");
            String newOpportunityId = InheritRecordUtil.getStringValue(activeRecordData, CommonConstant.NEW_OPPORTUNITY_ID, "");
            String leadsId = InheritRecordUtil.getStringValue(activeRecordData, CommonConstant.LEADS_ID, "");
            if (ObjectUtils.isNotEmpty(accountId)) {
                arg.setLinkAccountDataId(accountId);
                arg.setLinkAccountFieldApiName(RequirementConstants.Field.account_id);
            }
            if (ObjectUtils.isNotEmpty(newOpportunityId)) {
                arg.setLinkOpportunityDataId(newOpportunityId);
                arg.setLinkOpportunityFieldApiName(RequirementConstants.Field.opportunity_id);
            }
            if (ObjectUtils.isNotEmpty(leadsId)) {
                arg.setLinkLeadsDataId(leadsId);
                arg.setLinkLeadsFieldApiName(RequirementConstants.Field.leads_id);
            }
            handleMainProcess(user, arg, activeRecordData);
        } catch (Exception e) {
            log.error("RequirementService execute e:", e);
        }
    }

    /**
     * 处理新建、导入需求获取知识库中得解决方案
     *
     * @param message
     */
    public void executeBatchAddRequirement(RequirementModel.RequirementBatchMsg message) {
        try {
            if (CollectionUtil.isEmpty(message.getRequirementIds())) {
                log.error("RequirementService executeBatchAddRequirement requirementIds is null ");
                return;
            }
            User user = new User(message.getTenantId(), CommonConstant.SUPER_USER);
            IObjectDescribe describe = AccountPathUtil.getObjectDescribe(user, CommonConstant.REQUIREMENT_OBJ);
            StopWatch stopWatch = StopWatch.create("handleMainProcess");
            List<IObjectData> list = serviceFacade.findObjectDataByIdsIgnoreAll(message.getTenantId(), message.getRequirementIds(), CommonConstant.REQUIREMENT_OBJ);
            stopWatch.lap("findObjectDataByIdsIgnoreAll");
            handleAiKnowledgeBase(user, list);
            stopWatch.lap("handleAiKnowledgeBase");
            handleSelectOneFieldOfValue(list, describe);
            stopWatch.lap("handleSelectOneFieldOfValue");
            updateData(user, list);
            stopWatch.lap("updateData");
            stopWatch.logSlow(1000);
        } catch (Exception e) {
            log.error("RequirementService executeBatchAddRequirement e:", e);
        }
    }

    /**
     * 组件arg
     *
     * @param message
     * @return
     */
    public RequirementModel.Arg handleParam(ActivityMessage message) {
        RequirementModel.Arg arg = new RequirementModel.Arg();
        arg.setTenantId(message.getTenantId());
        arg.setActiveRecordId(message.getObjectId());
        arg.setLanguage(message.getLanguage());
        arg.setUserId(message.getOpId());
        return arg;
    }

    public void handleMainProcess(User user, RequirementModel.Arg arg, IObjectData activeRecordData) {
        String content = InheritRecordUtil.getStringValue(activeRecordData, CommonConstant.INTERACTIVE_CONTENT, "");
        if (ObjectUtils.isEmpty(content) || content.length()<=150) {
            log.info("RequirementService handleMainProcess interactiveContent is null dataId:{}", activeRecordData.getId());
            return;
        }
        StopWatch stopWatch = StopWatch.create("handleMainProcess");

        List<InteractiveDocument> interactiveList = activityMongoDao.queryListByActiveRecordId(user.getTenantId(), arg.getActiveRecordId(), 0, CompletionsService.maxNum);
        if (CollectionUtil.isNotEmpty(interactiveList)) {
            content = activityMongoDao.montageMongoContent(interactiveList, true, arg.getLanguage());
        }
        stopWatch.lap("queryListByActiveRecordId");
        IObjectDescribe describe = AccountPathUtil.getObjectDescribe(user, CommonConstant.REQUIREMENT_OBJ);
        List<IObjectData> resultList = handleFragmentationSummary(user, arg, content, "prompt_sfa_activity_demand_insight", describe);
        if (CollectionUtil.isEmpty(resultList)) {
            log.warn("RequirementService handleMainProcess resultList is null");
            return;
        }
        stopWatch.lap("handleFragmentationSummary");
        handleLinkField(user, resultList, arg, interactiveList);
        stopWatch.lap("handleLinkField");
        handleAiKnowledgeBase(user, resultList);
        stopWatch.lap("handleAiKnowledgeBase");
        handleSelectOneFieldOfValue(resultList, describe);
        stopWatch.lap("handleSelectOneFieldOfValue");
        saveData(user, resultList, describe, activeRecordData.getOwner());
        stopWatch.lap("saveData");

        featureValueProducer.sendRequirementMessage(FeatureMqModel.Message.builder()
                .tenantId(user.getTenantId())
                .userId(User.SUPPER_ADMIN_USER_ID)
                .objectId(arg.getActiveRecordId())
                .objectApiName(Utils.ACTIVE_RECORD_API_NAME)
                .build());

        stopWatch.logSlow(1000);
    }

    /**
     * 分段提取需求
     *
     * @param user
     * @param arg
     * @param corpus      语料
     * @param propApiName 提问词ApiName
     * @param describe    描述
     * @return
     */
    public List<IObjectData> handleFragmentationSummary(User user, RequirementModel.Arg arg, String corpus,
                                                        String propApiName, IObjectDescribe describe) {
        List<String> chunks = new ArrayList<>();
        if (corpus == null || corpus.isEmpty()) {
            return null; // 空字符串返回空列表
        }

        int length = corpus.length();
        for (int i = 0; i < length; i += ActivityInteractionService.NOT_REALTIME_FRAGMENTATION_BYTE_SIZE) {
            int end = Math.min(i + ActivityInteractionService.NOT_REALTIME_FRAGMENTATION_BYTE_SIZE, length);
            chunks.add(corpus.substring(i, end));
        }
        List<IObjectData> allRequirementList = new ArrayList<>();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        for (String str : chunks) {
            parallelTask.submit(() -> {
                Map<String, Object> sceneParamMap = new HashMap<>();
                sceneParamMap.put(ActivityQuestionConstants.CORPORA_FROM_MONGGO, str);
                sceneParamMap = activitySummaryService.handleAiCompleteLanguage(sceneParamMap, null, arg.getLanguage());
                handleSelectOneFieldLabel(describe, sceneParamMap);
                String resultAi = activitySummaryService.getAiComplete(user, propApiName, sceneParamMap, arg.getActiveRecordId());
                resultAi = activitySummaryService.captureAiResult(resultAi);
                if (ObjectUtils.isEmpty(resultAi)) {
                    log.warn("handleFragmentationSummaryNotRealTime result is null content:{}", resultAi);
                    return;
                }
                List<Map> resultList = JSONObject.parseArray(resultAi, Map.class);
                resultList.stream().forEach(x -> {
                    allRequirementList.add(ObjectDataExt.of(x));
                });
            });
        }
        try {
            parallelTask.await(600, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("handleFragmentationSummaryNotRealTime error e:", e);
        }
        return allRequirementList;
    }

    /**
     * 将单选多选字段的选项值放入到提问词中
     *
     * @param describe
     * @param sceneParamMap
     */
    public void handleSelectOneFieldLabel(IObjectDescribe describe, Map<String, Object> sceneParamMap) {
        Map<String, List<Map>> optionsMap = extractSelectOneFieldOption(describe);
        selectFieldMap.keySet().stream().forEach(f -> {
                    List<Map> options = optionsMap.get(f);
                    StringBuilder sb = new StringBuilder();
                    options.stream().forEach(x -> {
                        sb.append(x.get("label")).append("/");
                    });
                    sceneParamMap.put(selectFieldMap.get(f), sb.toString());
                }
        );
    }


    public Map<String, List<Map>> extractSelectOneFieldOption(IObjectDescribe describe) {
        Map<String, List<Map>> result = new HashMap<>();
        selectFieldMap.keySet().stream().forEach(f -> {
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(f);
            result.put(f, (List<Map>) fieldDescribe.get("options"));
        });
        return result;
    }

    /**
     * 将ai总结出来的单选，多选字段的值转换为value
     *
     * @param list
     * @param describe
     */
    public void handleSelectOneFieldOfValue(List<IObjectData> list, IObjectDescribe describe) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Map<String, List<Map>> optionsMap = extractSelectOneFieldOption(describe);
        list.stream().forEach(d -> {
            selectFieldMap.keySet().stream().forEach(f -> {
                if (ObjectUtils.isEmpty(d.get(f))) {
                    return;
                }
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(f);
                if ("select_many".equals(fieldDescribe.getType())) {
                    String labelStr = d.get(f).toString();
                    List<String> labelList = new ArrayList<>();
                    if (labelStr.contains(";")) {
                        String[] labelParts = labelStr.split(";");
                        labelList = Arrays.asList(labelParts);
                    } else {
                        labelList.add(labelStr);
                    }
                    List<Map> options = optionsMap.get(f);
                    List<String> valueList = new ArrayList<>();
                    for (Map o : options) {
                        if (labelList.contains(o.get("label").toString())) {
                            valueList.add(o.get("value").toString());
                        }
                    }
                    d.set(f, valueList);
                    d.set(f + "__r", labelList);
                } else if ("select_one".equals(fieldDescribe.getType())) {
                    String labelStr = d.get(f).toString();
                    List<Map> options = optionsMap.get(f);
                    String value = "";
                    for (Map o : options) {
                        if (labelStr.contains(o.get("label").toString())) {
                            value = o.get("value").toString();
                            break;
                        }
                    }
                    if (ObjectUtils.isNotEmpty(value)) {
                        d.set(f, value);
                        d.set(f + "__r", labelStr);
                    } else {
                        log.warn("handleSelectOneFieldOfValue select_one error:f:{},labelStr:{}", f, labelStr);
                    }
                } else {
                    log.warn("handleSelectOneFieldOfValue Type is not set f:{}", f);
                }
            });
        });
    }

    /**
     * 处理关联的对象，以及该提出人关联的联系人
     *
     * @param list
     * @param arg
     */
    public void handleLinkField(User user, List<IObjectData> list, RequirementModel.Arg arg, List<InteractiveDocument> interactiveList) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "active_record_id", arg.getActiveRecordId());
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, "ActivityUserObj", searchTemplateQuery);
        Map<String, IObjectData> userMap = new HashMap<>();
        if (ObjectUtils.isNotEmpty(queryResult) && CollectionUtil.isNotEmpty(queryResult.getData())) {
            String speaker = I18nClient.getInstance().getOrDefault("sfa.activity.corpus.list_item_user_label", 0, arg.getLanguage(), "发言人");// ignoreI18n
            userMap = queryResult.getData().stream().collect(Collectors.toMap(x -> handleUserNameConvert(x.getName(), speaker), Function.identity(), (v1, v2) -> v1));
        }
        Map<String, String> seqOfUserMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(interactiveList)) {
            seqOfUserMap = interactiveList.stream().collect(Collectors.toMap(x -> String.valueOf(x.getSeq()), InteractiveDocument::getActivityUserId, (v1, v2) -> v1));
        }
        for (IObjectData data : list) {
            data.set(RequirementConstants.Field.active_record_id, arg.getActiveRecordId());
            if (ObjectUtils.isNotEmpty(arg.getLinkAccountFieldApiName())) {
                data.set(arg.getLinkAccountFieldApiName(), arg.getLinkAccountDataId());
            }
            if (ObjectUtils.isNotEmpty(arg.getLinkOpportunityFieldApiName())) {
                data.set(arg.getLinkOpportunityFieldApiName(), arg.getLinkOpportunityDataId());
            }
            if (ObjectUtils.isNotEmpty(arg.getLinkLeadsFieldApiName())) {
                data.set(arg.getLinkLeadsFieldApiName(), arg.getLinkLeadsDataId());
            }
            if (ObjectUtils.isNotEmpty(data.get(RequirementConstants.Field.initiator))) {
                String initiatorValue = data.get(RequirementConstants.Field.initiator).toString();
                if (userMap.containsKey(initiatorValue)) {
                    IObjectData userData = userMap.get(initiatorValue);
                    data.set(RequirementConstants.Field.initiator, userData.getId());
                    data.set(RequirementConstants.Field.initiator + "__r", initiatorValue);
                    if (ObjectUtils.isNotEmpty(userData.get(RequirementConstants.Field.contact_id))) {
                        data.set(RequirementConstants.Field.contact_id, userData.get(RequirementConstants.Field.contact_id));
                    }
                } else {
                    String seq = data.get(RequirementConstants.Field.seq_num).toString();
                    if (seqOfUserMap.containsKey(seq)) {
                        data.set(RequirementConstants.Field.initiator, seqOfUserMap.get(seq));
                    } else {
                        log.warn("handleLinkField userMap is not exist initiatorValue :{}", initiatorValue);
                        data.set(RequirementConstants.Field.initiator, "");
                        data.set(RequirementConstants.Field.initiator + "__r", initiatorValue);
                    }
                }
            }
        }
    }

    public String handleUserNameConvert(String userName, String speaker) {
        if (userName.contains("user_")) {
            String[] userNameArr = userName.split("_");
            return speaker + userNameArr[1];
        }
        return userName;
    }

    public void handleAiKnowledgeBase(User user, List<IObjectData> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.warn("handleAiKnowledgeBase list is null");
            return;
        }
        if (!aiKnowledgeBaseService.hasLicense(user)) {
            log.warn("no AiKnowledge license");
            return;
        }
        try {
            log.info("handleAiKnowledgeBase start list size is {}", list.size());
            List<List<IObjectData>> partList = Lists.partition(list, 5);
            log.info("handleAiKnowledgeBase start partList size is {}", partList.size());
            for (List<IObjectData> partList2 : partList) {
                ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
                partList2.forEach(x ->
                    parallelTask.submit(() -> {
                        String question = x.get(RequirementConstants.Field.content).toString();
                        log.info("handleAiKnowledgeBase question is {}", question);
                        RequirementModel.RequirementSolve sole = aiKnowledgeBaseService.queryKnowledgeBaseByAISummary(user, question);
                        if (ObjectUtils.isNotEmpty(sole)) {
                            x.set(RequirementConstants.Field.requirement_solve, sole.getRequirementSolve());
                            List<String> wordIds = sole.getHits().stream().map(AiRestProxyModel.Hit::getId).collect(Collectors.toList());
                            x.set(RequirementConstants.Field.knowledge_base_word_id, wordIds);
                            x.set(RequirementConstants.Field.assessment_dimension, sole.getAssessmentDimension());
                        }
                    })
                );
                parallelTask.await(300, TimeUnit.SECONDS);
            }

        } catch (Exception e) {
            log.error("handleAiKnowledgeBase e", e);
        }
    }

    public void saveData(User user, List<IObjectData> resultList, IObjectDescribe describe, List<String> owners) {
        List<IObjectData> newList = new ArrayList<>();
        resultList.stream().forEach(iObjectData -> {
            ObjectDataDocument objectDataDocument = ObjectDataDocument.of(iObjectData);
            objectDataDocument.put("object_describe_api_name", CommonConstant.REQUIREMENT_OBJ);
            objectDataDocument.put("object_describe_id", describe.getId());
            objectDataDocument.put(Tenantable.TENANT_ID, user.getTenantId());
            objectDataDocument.put("owner", owners);
            objectDataDocument.put("record_type", "default__c");
            newList.add(objectDataDocument.toObjectData());
        });
        try {
            serviceFacade.bulkSaveObjectData(newList, user);
            LogUtil.recordLogs(user, Lists.newArrayList(resultList), describe, EventType.ADD, ActionType.Add);
        } catch (Exception e) {
            log.error("RequirementService saveTopic error :", e);
        }
    }

    public void updateData(User user, List<IObjectData> resultList) {
        try {
            serviceFacade.batchUpdateByFields(user, resultList, Lists.newArrayList(RequirementConstants.Field.requirement_solve,
                    RequirementConstants.Field.knowledge_base_word, RequirementConstants.Field.assessment_dimension));
            //LogUtil.recordLogs(user,Lists.newArrayList(resultList),describe, EventType.ADD, ActionType.Add);
        } catch (Exception e) {
            log.error("RequirementService updateData error :", e);
        }
    }

    public void handleChangeSpeaker(InteractionModel.ChangeSpeakerMsg msg, List<InteractiveDocument> documents) {
        User user = new User(msg.getTenantId(), CommonConstant.SUPER_USER);
        List<String> modifiedSeqList = documents.stream().map(y -> String.valueOf(y.getSeq())).collect(Collectors.toList());

        //根据被修改人的id查询有哪些需求需要更改人员
        List<IObjectData> needUpdateList = new ArrayList<>();
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(500);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, RequirementConstants.Field.active_record_id, msg.getObjectId());
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, CommonConstant.REQUIREMENT_OBJ, searchTemplateQuery);
        if (ObjectUtils.isNotEmpty(queryResult) || CollectionUtils.notEmpty(queryResult.getData())) {
            queryResult.getData().stream().forEach(x -> {
                if (ObjectUtils.isNotEmpty(x.get(RequirementConstants.Field.seq_num))) {
                    String seqNum = x.get(RequirementConstants.Field.seq_num).toString();
                    if (modifiedSeqList.contains(seqNum)) {
                        needUpdateList.add(x);
                    }
                }
            });
        }

        if (CollectionUtil.isEmpty(needUpdateList)) {
            log.warn("RequirementService handleChangeSpeaker needUpdateList is empty");
            return;
        }
        Map<String, InteractiveDocument> seqDocMap = documents.stream().collect(Collectors.toMap(x -> String.valueOf(x.getSeq()), x -> x));
        List<String> userIds = documents.stream().map(InteractiveDocument::getActivityUserId).collect(Collectors.toList());
        List<IObjectData> userList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), userIds, CommonConstant.ActivityUserObj);
        Map<String, String> contactIdMap = userList.stream().filter(x -> ObjectUtils.isNotEmpty(x.get("contact_id")))
                .collect(Collectors.toMap(IObjectData::getId, x -> x.get("contact_id").toString()));
        needUpdateList.stream().forEach(x -> {
            String seqNum = x.get(RequirementConstants.Field.seq_num).toString();
            if (seqDocMap.containsKey(seqNum)) {
                InteractiveDocument document = seqDocMap.get(seqNum);
                x.set(RequirementConstants.Field.initiator, document.getActivityUserId());
                if (contactIdMap.containsKey(document.getActivityUserId())) {
                    x.set(RequirementConstants.Field.contact_id, contactIdMap.get(document.getActivityUserId()));
                }
            } else {
                log.warn("RequirementService handleChangeSpeaker seqDocMap is not have  seqNum:{}", seqNum);
            }
        });
        serviceFacade.batchUpdateByFields(user, needUpdateList, Lists.newArrayList(RequirementConstants.Field.initiator, RequirementConstants.Field.contact_id));
    }
}
