package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.task.sfa.activitysummary.model.InteractionModel;
import com.facishare.crm.task.sfa.common.constants.AIQuestionConstants;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lik
 * @date : 2024/12/3 17:03
 */

@Service
@Slf4j
public class AccountInteractionService extends ActivityInteractionService{

    @Override
    public String getObjectApiName() {
        return CommonConstant.ACCOUNT_API_NAME;
    }

    @Override
    public List<IObjectData> getSuggestionTopicList(User user,String id,String apiName){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(500);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, CommonConstant.ACCOUNT_ID, id);
        SearchUtil.fillFilterEq(filters, AIQuestionConstants.Field.question_type, AIQuestionConstants.QuestionTypeEnum.SUGGESTION.getValue());
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQueryWithFieldsIgnoreAll(user, CommonConstant.ACTIVITY_QUESTION_API_NAME, searchTemplateQuery,Lists.newArrayList("_id",AIQuestionConstants.Field.question_content,AIQuestionConstants.Field.library_id));
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            return Lists.newArrayList();
        }
        List<String> libraryIds = queryResult.getData().stream().map(x->x.get(AIQuestionConstants.Field.library_id,String.class)).collect(Collectors.toList());
        List<IObjectData> libraryList = metaDataService.findObjectDataByIds(user.getTenantId(),libraryIds,CommonConstant.SALES_TOPIC_LIBRARY_API_NAME);
        Map<String,String> libraryMap = libraryList.stream().collect(Collectors.toMap(x->x.getId(),x->x.get("content",String.class)));
        List<IObjectData> returnList = queryResult.getData();
        returnList.stream().forEach(x->{
            if(libraryMap.containsKey(x.get(AIQuestionConstants.Field.library_id,String.class))){
                x.set(AIQuestionConstants.Field.question_content,libraryMap.get(x.get(AIQuestionConstants.Field.library_id,String.class)));
            }
        });
        return returnList;
    }
    @Override
    public String getLinkFieldApiName(String apiName){
        return  CommonConstant.ACCOUNT_ID;
    }
    @Override
    public Integer getDataVersion(User user, InteractionModel.Arg arg){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, CommonConstant.ACCOUNT_ID, arg.getLinkAccountDataId());
        SearchUtil.fillFilterEq(filters, AIQuestionConstants.Field.active_record_id, arg.getActiveRecordId());
        SearchUtil.fillFilterEq(filters, AIQuestionConstants.Field.question_type, AIQuestionConstants.QuestionTypeEnum.INTERACTION.getValue());
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy(AIQuestionConstants.Field.answer_version, false)));
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQueryWithFieldsIgnoreAll(user, CommonConstant.ACTIVITY_QUESTION_API_NAME, searchTemplateQuery,Lists.newArrayList("_id",AIQuestionConstants.Field.answer_version));
        if(ObjectUtils.isEmpty(queryResult)){
            return 0;
        }
        return queryResult.getData().get(0).get(AIQuestionConstants.Field.answer_version,Integer.class);
    }
}
