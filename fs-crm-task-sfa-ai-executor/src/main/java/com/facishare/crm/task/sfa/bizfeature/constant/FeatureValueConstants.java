package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 特征值常量
 *
 * <AUTHOR>
 */
public interface FeatureValueConstants {
     /**
      * 特征
      */
     String FEATURE_ID = "feature_id";
     /**
      * 匹配对象
      */
     String OBJECT_API_NAME = "object_api_name";
     /**
      * 对象 ID
      */
     String OBJECT_ID = "object_id";
     /**
      * 返回数据类型
      */
     String RETURN_DATA_TYPE = "return_data_type";

     /**
      * 原始值-文本
      */
     String ORIGINAL_VALUE_TEXT = "original_value_text";
     /**
      * 原始值-布尔
      */
     String ORIGINAL_VALUE_BOOL = "original_value_bool";
     /**
      * 原始值-数值
      */
     String ORIGINAL_VALUE_NUMBER = "original_value_number";
     /**
      * 计算时间
      */
     String CALC_TIME = "calc_time";
     /**
      * 促发原始值
      */
     String TRIGGER_VALUE = "trigger_value";
     /**
      * 特征主对象
      */
     String MASTER_OBJECT_API_NAME = "master_object_api_name";
     /**
      * 特征主对象 ID
      */
     String MASTER_OBJECT_ID = "master_object_id";

     /**
      * 触发对象API名称
      */
     String TRIGGER_OBJECT_API_NAME = "trigger_object_api_name";
     /**
      * 触发对象 ID
      */
     String TRIGGER_OBJECT_ID = "trigger_object_id";
     /**
      * 最后修改时间
      */
     String LAST_MODIFIED_TIME = "last_modified_time";
}