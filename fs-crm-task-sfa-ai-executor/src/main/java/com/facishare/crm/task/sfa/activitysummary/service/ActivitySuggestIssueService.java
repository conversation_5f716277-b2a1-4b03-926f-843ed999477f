package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.activity.enums.AttendeesInsightType;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.task.sfa.activitysummary.constant.ActiveRecordConstants;
import com.facishare.crm.task.sfa.activitysummary.constant.ActivityQuestionConstants;
import com.facishare.crm.task.sfa.activitysummary.constant.SalesTopicLibraryConstants;
import com.facishare.crm.task.sfa.activitysummary.model.SuggestIssueParams;
import com.facishare.crm.task.sfa.activitysummary.model.auditlog.ActivityMessageConverter;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.AttendeesInsightService;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.crmrestapi.common.data.Where;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.activitysummary.constant.ActivityQuestionConstants.*;

@Service
@Slf4j
@AllArgsConstructor
public class ActivitySuggestIssueService {
    private static final String PROMPT_SFA_SUGGEST_ISSUE_ZH = "prompt_sfa_suggest_issue_zh";
    private static final String PROMPT_SFA_SUGGEST_ISSUE_RETRY_ZH = "prompt_sfa_suggest_issue_retry_zh";
    private static final String PROMPT_SFA_SUGGEST_ISSUE = "prompt_sfa_suggest_issue";
    private static final String ANSWER_SUMMARY_TEMP = ANSWER_SUMMARY + "__temp";
    private static final String ADVICE_STATUS_PRE = ADVICE_STATUS + "__pre";
    private static final String ADVICE_STATUS_CUR = ADVICE_STATUS + "__cur";
    private static final String CLEAR = AdviceStatusType.CLEARLY_DEFINED.getAdviceStatusType();
    private static final String UNCLEAR = AdviceStatusType.UNCLEARLY_DEFINED.getAdviceStatusType();
    // SUMMARY_THRESHOLD = 35000

    private final ServiceFacade serviceFacade;
    private final CompletionsService completions;
    private final AttendeesInsightService attendeesInsightService;
    private final ActivityMongoDao activityMongoDao;

    @SFAAuditLog(bizName = "suggest_issue", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void consumer(ActivityMessage activityMessage) {
        StopWatch stopWatch = new StopWatch("consumer");
        String tenantId = activityMessage.getTenantId();
        if (StringUtils.isAnyBlank(tenantId, activityMessage.getObjectId())) {
            log.warn("ActivitySuggestIssueService.consumer tenantId or objectId is blank");
            return;
        }
        User user = User.systemUser(tenantId);
        IObjectData activeRecordData = queryActivityRecordData(user, activityMessage.getObjectId());
        stopWatch.lap("queryActivityRecordData");
        if (activeRecordData == null) {
            log.warn("ActivitySuggestIssueService.consumer activeRecordData is null");
            return;
        }
        // 因摘要组件会修改active_record_content的值，故放弃使用其做语料
        if (StringUtils.isBlank(activeRecordData.get(ActiveRecordConstants.INTERACTIVE_CONTENT_O, String.class))) {
            log.warn("ActivitySuggestIssueService.consumer activeRecordData.interactive_content__o is blank");
            return;
        }
        String activeRecordId = activeRecordData.getId();
        log.info("ActivitySuggestIssueService.consumer start, T={} AR={}", tenantId, activeRecordId);
        String accountId = activeRecordData.get(CommonConstant.ACCOUNT_ID, String.class);
        String opportunityId = activeRecordData.get(CommonConstant.NEW_OPPORTUNITY_ID, String.class);
        if (StringUtils.isBlank(accountId) && StringUtils.isBlank(opportunityId)) {
            log.warn("ActivitySuggestIssueService.consumer accountId and opportunityId is blank");
            return;
        }
        List<IObjectData> questionDataList = queryActivityQuestionData(user, accountId, opportunityId);
        stopWatch.lap("queryActivityQuestionData");
        if (CollectionUtils.empty(questionDataList)) {
            log.warn("ActivitySuggestIssueService.consumer questionDataList is empty");
            return;
        }
        List<IObjectData> libraryList = queryLibrary(user, questionDataList);
        stopWatch.lap("queryLibrary");
        if (CollectionUtils.empty(libraryList)) {
            log.warn("ActivitySuggestIssueService.consumer libraryList is empty");
            return;
        }
        // 整合数据
        setQuestionContent(questionDataList, libraryList);
        // 生成答案
        generateAnswer(new User(tenantId, activityMessage.getOpId()), questionDataList, activeRecordId);
        stopWatch.lap("generateAnswer");
        // 如果AI回答不明确就不关联销售记录
        cleanActiveRecord(questionDataList, activeRecordId);
        serviceFacade.batchUpdateByFields(user, questionDataList, Lists.newArrayList(ANSWER_SUMMARY, ANSWER_TIME, ADVICE_STATUS, ACTIVE_RECORD_ID));
        stopWatch.lap("batchUpdateByFields");
        List<String> insightTypeList = Lists.newArrayList(AttendeesInsightType.SOP_COVERAGE);
        attendeesInsightService.sendInsightMessage(activityMessage.getTenantId(), activityMessage.getObjectId(), insightTypeList);
        stopWatch.lap("sendInsightMessage");
        stopWatch.logSlow(10, TimeUnit.SECONDS);
    }

    private void generateAnswer(User user, List<IObjectData> questionDataList, String dataId) {
        StopWatch stopWatch = StopWatch.createStarted("generateAnswer");
        List<SuggestIssueParams.AIAnswer> aiAnswers;
        if (hasDocument(user, dataId)) {
            List<SuggestIssueParams.AIQuestion> questionList = suggestJSON(questionDataList);
            aiAnswers = requestAIWithDocument(user, dataId, questionList);
            stopWatch.lap("request answer with document");
        } else {
            List<SuggestIssueParams.AIQuestion> questionList = suggestJSON(questionDataList);
            aiAnswers = requestAI(user, dataId, questionList, PROMPT_SFA_SUGGEST_ISSUE_ZH);
            stopWatch.lap("request answer");
        }
        if (aiAnswers.isEmpty()) {
            return;
        }
        Map<String, IObjectData> questionsMap = questionDataList.stream().collect(Collectors.toMap(data -> data.get(ActivityQuestionConstants.LIBRARY_ID, String.class, "null"), Function.identity(), (v1, v2) -> v1));
        Map<String, IObjectData> retryQuestionsMap = new HashMap<>();
        Map<String, IObjectData> topicAnswerMap = new HashMap<>();
        for (SuggestIssueParams.AIAnswer aiAnswer : aiAnswers) {
            String answer = aiAnswer.getAnswer();
            String key = aiAnswer.getIndex();
            IObjectData question = questionsMap.get(key);
            if (question != null) {
                String status = aiAnswer.getStatus();
                if (CLEAR.equals(status)) {
                    String existAnswer = question.get(ANSWER_SUMMARY_O, String.class);
                    question.set(ANSWER_TIME, System.currentTimeMillis());
                    if (StringUtils.isNotBlank(existAnswer) && !existAnswer.equals(answer)) { // 已有多销售记录答案等待合并
                        question.set(ANSWER_SUMMARY_TEMP, answer);
                        retryQuestionsMap.put(key, question);
                    } else { // 第一次有多销售记录答案
                        question.set(ANSWER_SUMMARY, answer);
                    }
                    question.set(ADVICE_STATUS_CUR, status);
                    // 单销售记录答案
                    IObjectData topicAnswer = newTopicAnswer(question, dataId, answer, aiAnswer.getDocIds());
                    topicAnswerMap.put(topicAnswer.get(ACTIVITY_QUESTION_ID, String.class), topicAnswer);
                }
                if (!CLEAR.equals(question.get(ADVICE_STATUS, String.class)) && CLEAR.equals(status) || UNCLEAR.equals(status)) {
                    question.set(ADVICE_STATUS, status);
                }
            }
        }
        if (!topicAnswerMap.isEmpty()) {
            upsertTopicAnswer(user, new ArrayList<>(topicAnswerMap.values()), dataId);
        }
        if (!retryQuestionsMap.isEmpty()) {
            generateRetryAnswer(user, dataId, retryQuestionsMap);
            stopWatch.lap("request retry answer");
        }
        stopWatch.logSlow(10, TimeUnit.SECONDS);
    }

    private void generateRetryAnswer(User user, String dataId, Map<String, IObjectData> retryQuestionsMap) {
        List<SuggestIssueParams.AIQuestion> questionList = new ArrayList<>();
        for (Map.Entry<String, IObjectData> entry : retryQuestionsMap.entrySet()) {
            IObjectData question = entry.getValue();
            String key = entry.getKey();
            SuggestIssueParams.AIQuestion input = SuggestIssueParams.AIQuestion.builder()
                    .index(key)
                    .question(question.get(ActivityQuestionConstants.LIBRARY_CONTENT, String.class))
                    .answer1(question.get(ANSWER_SUMMARY_O, String.class))
                    .answer2(question.get(ANSWER_SUMMARY_TEMP, String.class))
                    .build();
            question.set(ANSWER_SUMMARY_TEMP, null);
            questionList.add(input);
        }
        List<SuggestIssueParams.AIAnswer> aiAnswers = requestAI(user, dataId, questionList, PROMPT_SFA_SUGGEST_ISSUE_RETRY_ZH);
        for (SuggestIssueParams.AIAnswer aiAnswer : aiAnswers) {
            IObjectData question = retryQuestionsMap.get(aiAnswer.getIndex());
            if (question != null && StringUtils.isNotBlank(aiAnswer.getAnswer())) {
                question.set(ANSWER_SUMMARY, aiAnswer.getAnswer());
            }
        }
    }

    private List<SuggestIssueParams.AIAnswer> requestAI(User user, String dataId, List<SuggestIssueParams.AIQuestion> questionList, String promptApiName) {
        AiRestProxyModel.Arg arg = getArg(dataId, "", JSON.toJSONString(questionList), promptApiName);
        return completions.requestCompletionList(user, arg, "", SuggestIssueParams.AIAnswer.class);
    }

    private List<SuggestIssueParams.AIAnswer> requestAIWithDocument(User user, String dataId, List<SuggestIssueParams.AIQuestion> questionList) {
        Pair<String, List<String>> pair = queryFormatDocument(user, dataId);
        AiRestProxyModel.Arg arg = getArg(dataId, pair.getLeft(), JSON.toJSONString(questionList), PROMPT_SFA_SUGGEST_ISSUE);
        List<SuggestIssueParams.AIAnswer> aiAnswers = completions.requestCompletionList(user, arg, "", SuggestIssueParams.AIAnswer.class);
        List<String> right = pair.getRight();
        for (SuggestIssueParams.AIAnswer aiAnswer : aiAnswers) {
            String docIdRaw = aiAnswer.getDocIdRaw();
            if (StringUtils.isNotBlank(docIdRaw)) {
                List<String> docIds = Arrays.stream(docIdRaw.split(",")).mapToInt(Integer::parseInt).mapToObj(right::get).collect(Collectors.toList());
                aiAnswer.setDocIds(docIds);
            }
        }
        return aiAnswers;
    }

    private static AiRestProxyModel.Arg getArg(String dataId, String document, String questionList, String promptApiName) {
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName(promptApiName);
        arg.setBingObjectDataId(dataId);
        Map<String, Object> customVariables = Maps.newHashMap();
        customVariables.put("document", document);
        customVariables.put("questionList", questionList);
        arg.setSceneVariables(customVariables);
        return arg;
    }

    private static List<SuggestIssueParams.AIQuestion> suggestJSON(List<IObjectData> questionDataList) {
        List<SuggestIssueParams.AIQuestion> aiQuestionList = new ArrayList<>();
        for (IObjectData objectData : questionDataList) {
            SuggestIssueParams.AIQuestion aiQuestion = SuggestIssueParams.AIQuestion.builder()
                    .index(objectData.get(ActivityQuestionConstants.LIBRARY_ID, String.class))
                    .question(objectData.get(ActivityQuestionConstants.LIBRARY_CONTENT, String.class))
                    .build();
            aiQuestionList.add(aiQuestion);
        }
        return aiQuestionList;
    }

    private static void setQuestionContent(List<IObjectData> questionDataList, List<IObjectData> libraryList) {
        Map<String, String> idToContent = libraryList.stream()
                .collect(Collectors.toMap(DBRecord::getId, library -> library.get(SalesTopicLibraryConstants.CONTENT, String.class, ""), (v1, v2) -> v2));
        questionDataList.forEach(questionData -> {
            String libraryId = questionData.get(ActivityQuestionConstants.LIBRARY_ID, String.class);
            String content = idToContent.get(libraryId);
            questionData.set(ActivityQuestionConstants.LIBRARY_CONTENT, content);
        });
    }

    private List<IObjectData> queryLibrary(User user, List<IObjectData> questionDataList) {
        List<String> libraryIdList = questionDataList.stream()
                .map(questionData -> questionData.get(ActivityQuestionConstants.LIBRARY_ID, String.class))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.empty(libraryIdList)) {
            log.warn("ActivitySuggestIssueService.queryLibrary libraryIdList is empty");
            return Collections.emptyList();
        }
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                .addFilter(DBRecord.ID, Operator.IN, libraryIdList)
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode());
        searchTemplateQueryPlus.setLimit(2000);
        searchTemplateQueryPlus.setOffset(0);
        searchTemplateQueryPlus.setSearchSource("db");
        return Optional.ofNullable(serviceFacade.findBySearchQueryIgnoreAll(user, SalesTopicLibraryConstants.API_NAME, searchTemplateQueryPlus))
                .map(QueryResult::getData)
                .orElse(new ArrayList<>());
    }

    private List<IObjectData> queryActivityQuestionData(User user, String accountId, String opportunityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<Wheres> wheres = new ArrayList<>();
        if (accountId != null) {
            List<IFilter> orFilters1 = new ArrayList<>();
            SearchUtil.fillFilterEq(orFilters1, ActivityQuestionConstants.ACCOUNT_ID, accountId);
            Wheres orWheres1 = new Wheres();
            orWheres1.setConnector(Where.CONN.AND.toString());
            orWheres1.setFilters(orFilters1);
            wheres.add(orWheres1);
        }
        if (opportunityId != null) {
            List<IFilter> orFilter2 = new ArrayList<>();
            SearchUtil.fillFilterEq(orFilter2, ActivityQuestionConstants.NEW_OPPORTUNITY_ID, opportunityId);
            Wheres orWheres2 = new Wheres();
            orWheres2.setConnector(Where.CONN.AND.toString());
            orWheres2.setFilters(orFilter2);
            wheres.add(orWheres2);
        }
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        SearchUtil.fillFilterEq(filters, ActivityQuestionConstants.QUESTION_TYPE, QuestionType.TWO.getQuestionType());
        SearchUtil.fillFilterEq(filters, ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
        SearchUtil.fillFilterIsNull(filters, "history_flag");
        query.setLimit(2000);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setOrders(Lists.newArrayList(new OrderBy(ActivityQuestionConstants.ORDER_FIELD, true)));
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setFilters(filters);
        query.setWheres(wheres);
        ActionContextExt contextExt = ActionContextExt.of(user).disableDeepQuote().skipRelevantTeam().setSearchRichTextExtra(true);
        IActionContext context = contextExt.getContext();
        context.setDoCalculate(false);
        List<IObjectData> questionList = Optional.ofNullable(serviceFacade.findBySearchQuery(context, CommonConstant.ACTIVITY_QUESTION_API_NAME, query))
                .map(QueryResult::getData)
                .orElse(new ArrayList<>());
        for (IObjectData question : questionList) {
            question.set(ADVICE_STATUS_PRE, question.get(ADVICE_STATUS));
        }
        return questionList;
    }

    private IObjectData queryActivityRecordData(User user, String objectId) {
        return serviceFacade.findObjectData(user, objectId, CommonConstant.ACTIVE_RECORD_API_NAME);
    }

    private static void cleanActiveRecord(List<IObjectData> questionList, String id) {
        for (IObjectData question : questionList) {
            @SuppressWarnings("unchecked")
            List<String> activeRecordIds = (List<String>) question.get(ACTIVE_RECORD_ID, List.class, new ArrayList<>());
            boolean contains = activeRecordIds.contains(id);
            boolean clear = CLEAR.equals(question.get(ADVICE_STATUS_CUR, String.class, ""));
            if (clear && !contains) {
                activeRecordIds.add(id);
            }
            if (!clear && contains) {
                activeRecordIds.remove(id);
            }
            question.set(ACTIVE_RECORD_ID, activeRecordIds);
        }
    }

    private boolean hasDocument(User user, String dataId) {
        List<InteractiveDocument> documents = activityMongoDao.queryListByActiveRecordId(user.getTenantId(), dataId, 0, 1);
        return documents != null && !documents.isEmpty();
    }

    private Pair<String, List<String>> queryFormatDocument(User user, String dataId) {
        List<InteractiveDocument> documents = activityMongoDao.queryListByActiveRecordId(user.getTenantId(), dataId, 0, 5000, true);
        Map<String, String> speakerMap = getSpeaker(user, dataId);
        StringBuilder builder = new StringBuilder();
        int index = 0;
        List<String> ids = new ArrayList<>(documents.size());
        for (InteractiveDocument document : documents) {
            String speaker = speakerMap.get(document.getActivityUserId());
            builder.append(index++).append('.'); // 序号
            if (speaker != null && !speaker.startsWith("user")) { // 发言人
                builder.append(speaker).append(':');
            }
            builder.append(document.getContent()).append('\n');
            ids.add(document.getId().toString());
        }
        return new ImmutablePair<>(builder.toString(), ids);
    }

    private Map<String, String> getSpeaker(User user, String dataId) {
        SearchTemplateQuery searchTemplateQuery = buildSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ACTIVE_RECORD_ID, dataId);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = this.serviceFacade.findBySearchQueryIgnoreAll(user, "ActivityUserObj", searchTemplateQuery);
        return queryResult.getData().stream().collect(Collectors.toMap(DBRecord::getId, IObjectData::getName));
    }

    private IObjectData newTopicAnswer(IObjectData question, String dataId, String answer, List<String> docIds) {
        IObjectData data = new ObjectData();
        data.setTenantId(question.getTenantId());
        data.setOwner(question.getOwner());
        data.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        data.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
        data.setDescribeApiName("ActivityTopicAnswerObj");
        data.set(ACTIVITY_QUESTION_ID, question.getId());
        data.set(ACTIVE_RECORD_ID, dataId);
        data.set(ANSWER_SUMMARY, answer);
        data.set("interactive_document_ids", docIds);
        return data;
    }

    private void upsertTopicAnswer(User user, List<IObjectData> list, String dataId) {
        try {
            serviceFacade.findObject(user.getTenantId(), "ActivityTopicAnswerObj");
        } catch (Exception e) {
            return;
        }
        List<String> questionIds = list.stream().map(d -> d.get(ACTIVITY_QUESTION_ID, String.class)).collect(Collectors.toList());
        SearchTemplateQuery searchTemplateQuery = buildSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ACTIVE_RECORD_ID, dataId);
        SearchUtil.fillFilterIn(filters, ACTIVITY_QUESTION_ID, questionIds);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = this.serviceFacade.findBySearchQueryIgnoreAll(user, "ActivityTopicAnswerObj", searchTemplateQuery);
        Map<String, IObjectData> existMap = queryResult.getData().stream().collect(Collectors.toMap(data -> data.get(ACTIVITY_QUESTION_ID, String.class), Function.identity(), (v1, v2) -> v1));
        List<IObjectData> insertList = new ArrayList<>();
        List<IObjectData> updateList = new ArrayList<>();
        for (IObjectData req : list) {
            IObjectData exist = existMap.get(req.get(ACTIVITY_QUESTION_ID, String.class));
            if (exist == null) {
                insertList.add(req);
            } else {
                exist.set(ANSWER_SUMMARY, req.get(ANSWER_SUMMARY, String.class));
                updateList.add(exist);
            }
        }
        if (!insertList.isEmpty()) {
            serviceFacade.bulkSaveObjectData(insertList, user);
        }
        if (!updateList.isEmpty()) {
            serviceFacade.batchUpdateByFields(user, updateList, Lists.newArrayList(ANSWER_SUMMARY, "interactive_document_ids"));
        }
    }

    private static SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        return searchTemplateQuery;
    }
}
