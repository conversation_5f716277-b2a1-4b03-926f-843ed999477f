package com.facishare.crm.task.sfa.activitysummary.service.paragraph;

import com.facishare.crm.task.sfa.activitysummary.model.ParagraphResultModel;
import com.facishare.crm.task.sfa.activitysummary.service.ParagraphAIService;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * ID处理服务类，负责处理与ID相关的操作
 */
@Service
@Slf4j
public class IDProcessingService {

    @Autowired
    private ParagraphAIService paragraphAiService;

    private static final int MAX_RETRY_COUNT = 3;

    /**
     * 查找缺失的ID
     * @param originalIds 原始ID列表
     * @param resultIds 结果ID列表
     * @return 缺失的ID列表
     */
    public List<String> findMissingIds(List<String> originalIds, List<String> resultIds) {
        if (CollectionUtils.isEmpty(originalIds)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(resultIds)) {
            return new ArrayList<>(originalIds);
        }

        // 将结果ID转换为Set以提高查找效率
        Set<String> resultIdSet = new HashSet<>(resultIds);
        
        // 用于存储缺失的ID
        List<String> missingIds = new ArrayList<>();
        
        // 遍历原始ID列表
        for (String originalId : originalIds) {
            if (!resultIdSet.contains(originalId)) {
                missingIds.add(originalId);
            }
        }

        return missingIds;
    }

    /**
     * 检查结果ID列表的顺序是否与原始ID列表一致
     * @param originalIds 原始ID列表（用作顺序参考）
     * @param resultIds 需要检查顺序的结果ID列表
     * @return 如果顺序完全一致返回true，否则返回false
     */
    public boolean checkIdsOrder(List<String> originalIds, List<String> resultIds) {
        if (originalIds == null || resultIds == null) {
            return false;
        }
        
        if (originalIds.size() != resultIds.size()) {
            return false;
        }
        
        for (int i = 0; i < originalIds.size(); i++) {
            if (!originalIds.get(i).equals(resultIds.get(i))) {
                log.warn("大模型返回结果顺序不一致，请求大模型id：{}，大模型返回结果：{}", originalIds, resultIds);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 检查并处理缺失的ID
     */
    public ParagraphResultModel checkMissingIds(User user, String currentContent, String previousSummary,
                                                List<String> currentBatchIds, ParagraphResultModel results) {
        List<String> allResultIds = collectAllResultIds(results);
        List<String> missingIds = findMissingIds(currentBatchIds, allResultIds);

        if (CollectionUtils.isNotEmpty(missingIds)) {
            log.warn("大模型返回结果不完整，请求大模型id：{}，大模型返回结果：{}，缺失id：{}", currentBatchIds, allResultIds, missingIds);
            results = retryWithMissingIds(user, currentContent, previousSummary, currentBatchIds, missingIds, results);
        } else if (!checkIdsOrder(currentBatchIds, allResultIds)) {
            log.warn("大模型返回结果顺序不一致，请求大模型id：{}，大模型返回结果：{}", currentBatchIds, allResultIds);
            results = retryWithMissingIds(user, currentContent, previousSummary, currentBatchIds, new ArrayList<>(), results);
        }

        return results;
    }

    /**
     * 针对缺失ID进行重试
     */
    private ParagraphResultModel retryWithMissingIds(User user, String currentContent, String previousSummary,
                                                     List<String> currentBatchIds, List<String> missingIds, ParagraphResultModel results) {
        ParagraphResultModel bestResults = results;
        int bestMissingCount = missingIds.size();

        for (int retry = 0; retry < MAX_RETRY_COUNT; retry++) {
            // 使用高阶模型处理缺失ID
            ParagraphResultModel retryResults = paragraphAiService.processContentSegmentationWithAdvancedModel(user, currentContent, previousSummary);

            if (retryResults == null || CollectionUtils.isEmpty(retryResults.getChunks())) {
                log.warn("第{}次重试请求失败，高阶大模型返回空结果", retry + 1);
                continue;
            }

            List<String> retryResultIds = collectAllResultIds(retryResults);
            List<String> currentMissingIds = findMissingIds(currentBatchIds, retryResultIds);

            if (currentMissingIds.isEmpty() && checkIdsOrder(currentBatchIds, retryResultIds)) {
                log.info("使用高阶模型重试成功，所有ID均已处理且顺序正确");
                return retryResults;
            }

            if (currentMissingIds.size() < bestMissingCount) {
                bestResults = retryResults;
                bestMissingCount = currentMissingIds.size();
            }
        }

        if (bestResults != null) {
            log.warn("经过多次高阶模型重试后仍有问题，使用最佳结果，缺失数量: {}", bestMissingCount);
            return bestResults;
        }

        log.warn("所有高阶模型重试均失败，返回null");
        return null;
    }

    /**
     * 收集所有结果中的内容ID
     */
    public List<String> collectAllResultIds(ParagraphResultModel results) {
        List<String> allResultIds = new ArrayList<>();
        if (results == null || CollectionUtils.isEmpty(results.getChunks())) {
            return allResultIds;
        }
        
        for (ParagraphResultModel.Chunk result : results.getChunks()) {
            if (result != null && result.getContent() != null) {
                allResultIds.addAll(result.getContent());
            }
        }
        return allResultIds;
    }
}