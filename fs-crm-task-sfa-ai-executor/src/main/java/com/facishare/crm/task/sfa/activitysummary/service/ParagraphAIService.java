package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.task.sfa.activitysummary.model.DirectTaggingResultModel;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphResultModel;
import com.facishare.crm.task.sfa.activitysummary.model.TopicMergeCheckResult;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI模型服务类，负责处理与AI模型相关的操作
 */
@Service
@Slf4j
public class ParagraphAIService {

    @Autowired
    private CompletionsService completionsService;

    // 常量定义
    private static final String API_NAME_ADVANCED_WITH_SUMMARY = "prompt_meeting_paragraph_summary_v3"; // 高阶模型-有摘要
    private static final String API_NAME_TOPIC_MERGE_CHECK = "prompt_topic_merge_check"; // 话题衔接检查API
    private static final String API_NAME_DIRECT_TAGGING = "prompt_direct_tagging"; // 直接打标签API
    private static final String MEET_CONTENT_KEY = "meetingContent";
    private static final String PREVIOUS_SUMMARY_KEY = "lastSummary";
    private static final String SEGMENT_END_CONTENT_KEY = "segmentEndContent";
    private static final String SEGMENT_START_CONTENT_KEY = "segmentStartContent";
    private static final String TAGS_KEY = "tags";
    private static final String CONTENT_KEY = "content";
    private static final String JSON_FORMAT = "{\"chunks\":[{\"content\":[\"id1\",\"id2\"],\"summary\":\"本段总结\"}]}";
    private static final String TOPIC_MERGE_JSON_FORMAT = "{\"shouldMerge\":true,\"reason\":\"合并原因\"}";
    private static final String DIRECT_TAGGING_JSON_FORMAT = "{\"results\":[{\"tagId\":\"4028828b6834325a01683432c6170000\",\"match\":true,\"source\":\"客户明确表示了预算\"}]}";
    private static final int MAX_RETRY_COUNT = 3;

    /**
     * 处理内容分段，带重试机制
     */
    public ParagraphResultModel requestWithRetry(User user, String meetContent, String previousSummary) {
        for (int retryCount = 0; retryCount < MAX_RETRY_COUNT; retryCount++) {
            ParagraphResultModel result = processContentSegmentation(user, meetContent, previousSummary);
            if (result != null && CollectionUtils.isNotEmpty(result.getChunks())) {
                return result;
            }
        }
        return null;
    }

    /**
     * 处理内容分段
     */
    public ParagraphResultModel processContentSegmentation(User user, String meetContent, String previousSummary) {
        try {
            AiRestProxyModel.Arg arg = prepareAiModelArgument(meetContent, previousSummary);
            log.debug("请求大模型进行分段，内容长度: {}, 有摘要: {}",
                    meetContent.length(), StringUtils.isNotEmpty(previousSummary));
            return completionsService.requestCompletionData(user, arg, JSON_FORMAT, ParagraphResultModel.class);
        } catch (Exception e) {
            log.error("请求大模型分段失败", e);
            return null;
        }
    }

    /**
     * 准备AI模型参数
     *
     * @param meetContent      会议内容
     * @param previousSummary  上一次摘要
     * @return AI模型参数
     */
    public AiRestProxyModel.Arg prepareAiModelArgument(String meetContent, String previousSummary) {
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();

        // 根据是否使用高级模型和是否有摘要选择API名称
        arg.setApiName(API_NAME_ADVANCED_WITH_SUMMARY);

        Map<String, Object> sceneVariables = new HashMap<>();
        sceneVariables.put(MEET_CONTENT_KEY, meetContent);

        if (StringUtils.isNotEmpty(previousSummary)) {
            String newPreviousSummary = "为了帮助你理解上下文，以下是上一段章节的总结\n" + previousSummary;
            sceneVariables.put(PREVIOUS_SUMMARY_KEY, newPreviousSummary);
        } else {
            sceneVariables.put(PREVIOUS_SUMMARY_KEY, "");
        }

        arg.setSceneVariables(sceneVariables);
        return arg;
    }

    /**
     * 使用高阶模型处理内容分段
     */
    public ParagraphResultModel processContentSegmentationWithAdvancedModel(User user, String meetContent, String previousSummary) {
        try {
            AiRestProxyModel.Arg arg = prepareAiModelArgument(meetContent, previousSummary);
            log.info("请求高阶大模型进行分段，内容长度: {}, 有摘要: {}",
                    meetContent.length(), StringUtils.isNotEmpty(previousSummary));
            return completionsService.requestCompletionData(user, arg, JSON_FORMAT, ParagraphResultModel.class);
        } catch (Exception e) {
            log.error("请求高阶大模型分段失败", e);
            return null;
        }
    }

    /**
     * 检查两个分段是否应该合并为同一话题
     *
     * @param user                用户信息
     * @param segmentEndContent   前一个分段的结尾内容
     * @param segmentStartContent 后一个分段的开始内容
     * @return 话题合并检查结果
     */
    public TopicMergeCheckResult checkTopicMerge(User user, String segmentEndContent, String segmentStartContent) {
        try {
            AiRestProxyModel.Arg arg = prepareTopicMergeArgument(segmentEndContent, segmentStartContent);
            log.debug("请求AI检查话题衔接，前段内容长度: {}, 后段内容长度: {}",
                    segmentEndContent.length(), segmentStartContent.length());
            return completionsService.requestCompletionData(user, arg, TOPIC_MERGE_JSON_FORMAT, TopicMergeCheckResult.class);
        } catch (Exception e) {
            log.error("请求AI检查话题衔接失败", e);
            return null;
        }
    }

    /**
     * 准备话题合并检查的AI模型参数
     *
     * @param segmentEndContent   前一个分段的结尾内容
     * @param segmentStartContent 后一个分段的开始内容
     * @return AI模型参数
     */
    private AiRestProxyModel.Arg prepareTopicMergeArgument(String segmentEndContent, String segmentStartContent) {
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName(API_NAME_TOPIC_MERGE_CHECK);

        Map<String, Object> sceneVariables = new HashMap<>();
        sceneVariables.put(SEGMENT_END_CONTENT_KEY, segmentEndContent);
        sceneVariables.put(SEGMENT_START_CONTENT_KEY, segmentStartContent);

        arg.setSceneVariables(sceneVariables);
        return arg;
    }

    /**
     * 直接对内容进行打标签
     *
     * @param user    用户
     * @param content 内容
     * @param tags    标签
     * @return 打标签结果
     */
    @Retryable(value = Exception.class,  backoff = @Backoff(delay = 500))
    public List<DirectTaggingResultModel> requestDirectTagging(User user, String content, String tags) {
        AiRestProxyModel.Arg arg = prepareDirectTaggingArgument(content, tags);
        log.debug("请求AI进行直接打标签，内容长度: {}", content.length());
        List<DirectTaggingResultModel> response = completionsService.requestCompletionList(user, arg, DIRECT_TAGGING_JSON_FORMAT,
                DirectTaggingResultModel.class);
        if (response == null || CollectionUtils.isEmpty(response)) {
            log.warn("请求AI进行直接打标签返回结果为空, response: {}", response);
            // 抛出异常以触发重试
            throw new RuntimeException("请求AI进行直接打标签返回结果为空");
        }
        return response;
    }

    @Recover
    public List<DirectTaggingResultModel> recoverRequestDirectTagging(Exception e, User user, String content, String tags) {
        log.error("请求AI进行直接打标签失败，已达最大重试次数. content length: {}, tags: {}", content.length(), tags, e);
        return null;
    }

    private AiRestProxyModel.Arg prepareDirectTaggingArgument(String content, String tags) {
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName(API_NAME_DIRECT_TAGGING);
        Map<String, Object> sceneVariables = new HashMap<>();
        sceneVariables.put(CONTENT_KEY, content);
        sceneVariables.put(TAGS_KEY, tags);
        arg.setSceneVariables(sceneVariables);
        return arg;
    }
}