package com.facishare.crm.task.sfa.activitysummary.service.tag;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTagMessage;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.UUID;

@Component
@Slf4j
public class MarkTagConsumer implements ApplicationListener<ContextRefreshedEvent> {
    private AutoConfMQPushConsumer consumer;
    @Autowired
    private ActivityTagService activityTagService;

    private static final String TAG_MARK = "mark";
    private static final String TAG_FEATURE = "feature";

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "sfa-activity-start-label-consumer", (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    consumeResponse(msg);
                } catch (Exception e) {
                    log.error("MarkTagConsumer :{}", msg, e);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    private void consumeResponse(MessageExt message) {
        String tag = message.getTags();
        byte[] body = message.getBody();

        switch (tag) {
            case TAG_MARK: {
                tagConsume(message, body);
                break;
            }
            case TAG_FEATURE: {
                log.info("MarkTagConsumer :{}", message);
                break;
            }
            default: {
                log.error("MarkTagConsumer :{}", message);
                break;
            }
        }
    }

    private void tagConsume(MessageExt message, byte[] body) {
        ActivityTagMessage activityTagMessage = JSON.parseObject(body, ActivityTagMessage.class);
        TraceContext.get().setTraceId(UUID.randomUUID() + "-" + activityTagMessage.getObjectId());
        log.info("ActivityTagMessage:{}, msgId: {}", activityTagMessage, message.getMsgId());
        activityTagService.consumer(activityTagMessage);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    @PreDestroy
    public void close() {
        consumer.close();
    }
}
