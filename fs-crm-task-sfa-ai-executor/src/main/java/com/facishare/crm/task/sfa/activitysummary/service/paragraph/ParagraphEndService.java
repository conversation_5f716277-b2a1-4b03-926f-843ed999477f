package com.facishare.crm.task.sfa.activitysummary.service.paragraph;

import com.facishare.crm.task.sfa.activitysummary.model.ParagraphContext;
import com.facishare.paas.appframework.core.model.User;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ParagraphEndService {
    @Autowired
    private JedisCmd SFAJedisCmd;

    private static final long expireTime = 60L * 60L;
    public static final int FINISH = 1;
    public static final int UNFINISH = 0;
    public static final int ERROR = -1;

    /**
     * 使用Lua脚本增加批次总数并设置过期时间
     */
    public void incrTotal(ParagraphContext context, User user) {
        String batchTotalKey = getBatchTotalKey(user.getTenantId(), context.getActiveRecordData().getId());
        log.info("incrTotal batchTotalKey: {}", batchTotalKey);
        // Lua脚本实现原子操作：增加计数并设置过期时间
        String script =
                "local key = KEYS[1] " +
                        "local expireTime = ARGV[1] " +
                        "local newValue = redis.call('incr', key) " +
                        "redis.call('expire', key, expireTime) " +
                        "return newValue";

        List<String> keys = Lists.newArrayList(batchTotalKey);
        List<String> args = Lists.newArrayList(String.valueOf(expireTime));
        SFAJedisCmd.eval(script, keys, args);
    }

    /**
     * 使用Lua脚本设置结束标记并设置过期时间
     */
    public void setEndFlag(ParagraphContext context, User user) {
        if (!context.isEnd()) {
            return;
        }
        String endKey = getEndKey(user.getTenantId(), context.getActiveRecordData().getId());
        log.info("setEndFlag endKey: {}", endKey);
        // Lua脚本设置结束标记并设置过期时间
        String script =
                "local key = KEYS[1] " +
                        "local value = ARGV[1] " +
                        "local expireTime = ARGV[2] " +
                        "redis.call('set', key, value) " +
                        "redis.call('expire', key, expireTime) " +
                        "return 1";

        List<String> keys = Lists.newArrayList(endKey);
        List<String> args = Lists.newArrayList(String.valueOf(System.currentTimeMillis()), String.valueOf(expireTime));
        SFAJedisCmd.eval(script, keys, args);
    }

    private String getBatchTotalKey(String tenantId, String objectId) {
        return String.format("%s-%s-batchTotal", tenantId, objectId);
    }

    private String getEndKey(String tenantId, String objectId) {
        return String.format("%s-%s-end", tenantId, objectId);
    }

    private String getProcessTotalKey(String tenantId, String objectId) {
        return String.format("%s-%s-processTotal", tenantId, objectId);
    }

    /**
     * 使用Lua脚本处理总数
     * 1. 根据key：tenantId-objectId-processTotal 进行自增
     * 2. 检查是否有end标记
     * 3. 如果有end标记，则比较处理总数是否大于等于batch总数
     * 4. 如果大于等于，则标记为完成状态并删除这些key
     * 5. 如果小于，则不处理
     *
     * @param tenantId 租户ID
     * @param objectId 对象ID
     * @return 处理结果：1-处理完成，0-处理中，-1-错误
     */
    public int processWithLuaScript(String tenantId, String objectId) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(objectId)) {
            log.warn("processWithLuaScript 参数不能为空: tenantId={}, objectId={}", tenantId, objectId);
            return ERROR;
        }

        String processTotalKey = getProcessTotalKey(tenantId, objectId);
        String batchTotalKey = getBatchTotalKey(tenantId, objectId);
        String endKey = getEndKey(tenantId, objectId);
        log.info("processWithLuaScript processTotalKey: {}", processTotalKey);
        log.info("processWithLuaScript batchTotalKey: {}", batchTotalKey);
        log.info("processWithLuaScript endKey: {}", endKey);
        // Lua脚本
        // 处理总数的key、批次总数的key、结束标记的key
        // 增加处理计数并设置过期时间
        // 检查是否有结束标记，如果有则比较处理总数和批次总数
        // 如果处理总数大于等于批次总数，则删除相关key并返回1（处理完成）
        // 否则返回0（处理中）
        String script = "local processTotalKey = KEYS[1] "
                + "local batchTotalKey = KEYS[2] "
                + "local endKey = KEYS[3] "
                + "local expireTime = ARGV[1]  "
                + "local processCount = redis.call('incr', processTotalKey) "
                + "redis.call('expire', processTotalKey, expireTime) "
                + "local endFlag = redis.call('exists', endKey) "
                + "if endFlag == 1 then "
                + "  local batchCount = redis.call('get', batchTotalKey) "
                + "  if batchCount then "
                + "    batchCount = tonumber(batchCount) "
                + "    if processCount >= batchCount then "
                + "      redis.call('del', processTotalKey) "
                + "      redis.call('del', batchTotalKey) "
                + "      redis.call('del', endKey) "
                + "      return 1 "
                + "    end "
                + "  end "
                + "end "
                + "return 0";

        List<String> keys = Lists.newArrayList(processTotalKey, batchTotalKey, endKey);
        List<String> args = Lists.newArrayList(String.valueOf(expireTime));
        Object result = SFAJedisCmd.eval(script, keys, args);
        log.info("success endKey: {}", endKey);
        if (result instanceof Long) {
            return ((Long) result).intValue();
        } else {
            log.warn("processWithLuaScript 返回结果类型异常: {}", result);
            return ERROR;
        }

    }
}
