package com.facishare.crm.task.sfa.activitysummary.service.tag;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTag;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphTagResult;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 基础标签处理服务
 * 提供标签处理的公共方法
 */
@Slf4j
public abstract class BaseTagProcessingService {

    // 公共常量
    protected static final String CONTENT_KEY = "content";
    protected static final String TAGS_KEY = "tags";
    protected static final String PARAGRAPH_ID_KEY = "paragraphId";

    /**
     * 构建内容列表
     *
     * @param paragraphContentsChunk 段落内容分片
     * @return 内容列表
     */
    protected List<Map<String, Object>> buildContentList(Map<String, String> paragraphContentsChunk) {
        List<Map<String, Object>> contentList = new ArrayList<>();
        for (Map.Entry<String, String> entry : paragraphContentsChunk.entrySet()) {
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put(PARAGRAPH_ID_KEY, entry.getKey());
            contentMap.put(CONTENT_KEY, entry.getValue());
            contentList.add(contentMap);
        }
        return contentList;
    }

    /**
     * 构建标签列表
     *
     * @param tags 标签列表
     * @return 标签列表Map
     */
    protected List<Map<String, Object>> buildTagsList(Collection<ActivityTag> tags) {
        List<Map<String, Object>> tagsList = new ArrayList<>();
        for (ActivityTag tag : tags) {
            Map<String, Object> tagMap = new HashMap<>();
            tagMap.put("id", tag.getId());
            tagMap.put("name", tag.getName());
            tagMap.put("description", tag.getDescription());
            tagsList.add(tagMap);
        }
        return tagsList;
    }

    /**
     * 处理标签结果模型
     *
     * @param resultModel 结果模型
     * @return 处理后的结果
     */
    protected Map<String, ParagraphTagResult.TagResult> processTagResultModel(ParagraphTagResult resultModel) {
        Map<String, ParagraphTagResult.TagResult> results = Maps.newHashMap();
        if (resultModel == null || resultModel.getTagResults() == null) {
            log.warn("No tag results returned from model");
            return results;
        }
        for (ParagraphTagResult.TagResult tagResult : resultModel.getTagResults()) {
            String paragraphId = tagResult.getParagraphId();
            List<String> tagIdList = tagResult.getTagId();
            if (StringUtils.isNotBlank(paragraphId) && CollectionUtils.isNotEmpty(tagIdList)) {
                results.put(paragraphId, tagResult);
            }
        }
        return results;
    }

    /**
     * 构建基础请求参数
     *
     * @param apiName     API名称
     * @param contentList 内容列表
     * @param tagsList    标签列表
     * @return 请求参数
     */
    protected AiRestProxyModel.Arg buildBaseTagArgument(String apiName, List<Map<String, Object>> contentList,
                                                        List<Map<String, Object>> tagsList) {
        return buildBaseTagArgument(apiName, contentList, tagsList, null);
    }

    /**
     * 构建基础请求参数（带建议）
     *
     * @param apiName     API名称
     * @param contentList 内容列表
     * @param tagsList    标签列表
     * @param suggestions 裁判模型的建议
     * @return 请求参数
     */
    protected AiRestProxyModel.Arg buildBaseTagArgument(String apiName, List<Map<String, Object>> contentList,
                                                        List<Map<String, Object>> tagsList, String suggestions) {
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName(apiName);

        Map<String, Object> sceneVariables = new HashMap<>();
        Map<String, Object> contentMap = Maps.newLinkedHashMap();
        contentMap.put(CONTENT_KEY, contentList);
        contentMap.put(TAGS_KEY, tagsList);

        // 如果有建议，添加到请求参数中
        if (StringUtils.isNotBlank(suggestions)) {
            contentMap.put("suggestions", "##选择合适标签的建议\n" + suggestions);
        } else {
            contentMap.put("suggestions", "");
        }

        sceneVariables.put(CONTENT_KEY, JSON.toJSONString(contentMap));
        arg.setSceneVariables(sceneVariables);

        return arg;
    }
} 