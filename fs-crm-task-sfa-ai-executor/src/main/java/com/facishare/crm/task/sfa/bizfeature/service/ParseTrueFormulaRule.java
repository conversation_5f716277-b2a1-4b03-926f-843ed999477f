package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.ParseRuleConstants;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ParseTrueFormulaRule extends ParseFormulaRule {

    @Override
    public String getRuleType() {
        return ParseRuleConstants.CalcMethodType.TRUE_FORMULA.getCalcMethodType();
    }

    @Override
    protected FeatureModel.ParseValueData getValue(User user, IObjectData feature, IObjectData rule, IObjectData data, IObjectDescribe dataDescribe) {

        FeatureModel.ParseValueData ret = super.getValue(user, feature, rule, data, dataDescribe);
        if (null != ret && Boolean.TRUE.equals(ret.getValue())) {
            return ret;
        }
        return null;
    }

}
