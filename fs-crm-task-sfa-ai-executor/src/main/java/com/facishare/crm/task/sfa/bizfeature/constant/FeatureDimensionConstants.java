package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 特征维度常量
 *
 * <AUTHOR>
 */
public interface FeatureDimensionConstants {
	/**
     * 父维度
     */
	String PARENT_ID = "parent_id";
	/**
     * 维度层级
     */
	String TREE_PATH = "tree_path";

	/**
	 * 系统数据类型
	 */
	String SYSTEM_TYPE = "system_type";

	enum SystemType {
		/**
		 * 系统
		 */
		SYSTEM("system"),
		/**
		 * 自定义
		 */
		UDEF("udef");

		private final String systemType;

		public String getSystemType() {
			return systemType;
		}

		SystemType(String systemType) {
			this.systemType = systemType;
		}
	}
}