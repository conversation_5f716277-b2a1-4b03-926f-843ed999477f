package com.facishare.crm.task.sfa.activitysummary.service.paragraph;

import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphDocument;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphMongoDao;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphContext;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphResultModel;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 文档处理服务类，负责处理与文档相关的操作
 */
@Service
@Slf4j
public class DocumentProcessService {

    @Resource
    private ActivityMongoDao activityMongoDao;
    @Resource
    private ParagraphMongoDao paragraphMongoDao;

    private static final int PAGE_SIZE = 50;

    /**
     * 查询一页文档
     */
    public List<InteractiveDocument> queryDocumentsPage(String tenantId, String objectId, int pageNo) {
        try {
            return activityMongoDao.queryListByActiveRecordId(tenantId, objectId, pageNo * PAGE_SIZE, PAGE_SIZE, true);
        } catch (Exception e) {
            log.error("查询文档失败: tenantId={}, objectId={}, pageNo={}", tenantId, objectId, pageNo, e);
            return Collections.emptyList();
        }
    }

    public boolean hasParagraphByObjectId(String tenantId, String objectId) {
        return CollectionUtils.isNotEmpty(paragraphMongoDao.queryListByActiveRecordId(tenantId, objectId, 0, 1, false));
    }

    /**
     * 保存段落结果
     *
     * @param user                  用户信息
     * @param paragraphResultModels 段落结果模型
     * @param paragraphContext             文本模型
     */
    public List<ParagraphDocument> saveSegmentationResult(User user, ParagraphResultModel paragraphResultModels, ParagraphContext paragraphContext) {
        if (paragraphResultModels == null || paragraphResultModels.getChunks() == null) {
            log.warn("段落结果为空，跳过保存");
            return Lists.newArrayList();
        }
        log.info("保存段落结果，数量={}", paragraphResultModels.getChunks().size());
        IObjectData activeRecordData = paragraphContext.getActiveRecordData();
        List<ParagraphDocument> paragraphDocumentList = Lists.newArrayList();
        try {
            for (ParagraphResultModel.Chunk chunk : paragraphResultModels.getChunks()) {
                if (chunk == null || CollectionUtils.isEmpty(chunk.getContent())) {
                    continue;
                }
                List<String> documentIds = chunk.getContent();
                Set<String> allActivityUserIds = collectActivityUserIds(paragraphContext, documentIds);
                TimeRange timeRange = calculateTimeRange(paragraphContext, documentIds);
                ParagraphDocument paragraphDocument = createParagraphDocument(
                        user,
                        chunk,
                        documentIds,
                        allActivityUserIds,
                        timeRange,
                        activeRecordData,
                        paragraphContext.getType(),
                        paragraphContext.getAddSeqNo()
                );
                paragraphDocumentList.add(paragraphDocument);
            }

            // 批量保存段落文档
            if (!paragraphDocumentList.isEmpty()) {
                saveParagraphDocuments(paragraphDocumentList, user);
                log.info("批量保存段落文档成功，数量={}", paragraphDocumentList.size());
                return paragraphDocumentList;
            }
        } catch (Exception e) {
            log.error("保存段落文档失败", e);
            return Lists.newArrayList();
        }
        return Lists.newArrayList();
    }

    /**
     * 收集活动用户ID
     */
    private Set<String> collectActivityUserIds(ParagraphContext paragraphContext, List<String> documentIds) {
        if (MapUtils.isEmpty(paragraphContext.getIdToDocumentMapping())) {
            return Sets.newHashSet();
        }
        Set<String> userIds = new HashSet<>();
        for (String docId : documentIds) {
            InteractiveDocument doc = paragraphContext.getIdToDocumentMapping().get(docId);
            if (doc != null && StringUtils.isNotBlank(doc.getUserId())) {
                userIds.add(doc.getUserId());
            }
        }
        return userIds;
    }

    /**
     * 计算时间范围
     */
    private TimeRange calculateTimeRange(ParagraphContext paragraphContext, List<String> documentIds) {
        if (documentIds.isEmpty() || MapUtils.isEmpty(paragraphContext.getIdToDocumentMapping())) {
            return new TimeRange(null, null);
        }
        // 获取第一个和最后一个文档的时间
        InteractiveDocument firstDoc = paragraphContext.getIdToDocumentMapping().get(documentIds.get(0));
        InteractiveDocument lastDoc = paragraphContext.getIdToDocumentMapping().get(documentIds.get(documentIds.size() - 1));

        String startTime = firstDoc != null ? firstDoc.getStartTime() : null;
        String endTime = lastDoc != null ? lastDoc.getEndTime() : null;

        return new TimeRange(startTime, endTime);
    }

    /**
     * 创建段落文档
     */
    private ParagraphDocument createParagraphDocument(
            User user,
            ParagraphResultModel.Chunk chunk,
            List<String> documentIds,
            Set<String> activityUserIds,
            TimeRange timeRange,
            IObjectData activeRecordData,
            String type,
            int seqNo) {

        ParagraphDocument paragraphDocument = new ParagraphDocument();
        // 手动生成并设置ID
        paragraphDocument.setId(new ObjectId());
        // 设置基本信息
        paragraphDocument.setTenantId(user.getTenantId());
        paragraphDocument.setSummary(chunk.getSummary());
        // 设置文档相关列表
        paragraphDocument.setDocumentIdList(documentIds);
        if (CollectionUtils.isNotEmpty(activityUserIds)) {
            paragraphDocument.setActivityUserIdList(new ArrayList<>(activityUserIds));
        }
        // 设置对象相关信息
        paragraphDocument.setObjectApiName(activeRecordData.getDescribeApiName());
        paragraphDocument.setObjectId(activeRecordData.getId());
        // 设置序号和时间信息
        paragraphDocument.setSeqNo(seqNo);
        if (StringUtils.isNotBlank(timeRange.getStartTime())) {
            paragraphDocument.setStartTime(timeRange.getStartTime());
        }
        if (StringUtils.isNotBlank(timeRange.getEndTime())) {
            paragraphDocument.setEndTime(timeRange.getEndTime());
        }
        // 设置其他标记
        paragraphDocument.setIsDeleted(false);
        // 设置关联ID
        paragraphDocument.setAccountId(activeRecordData.get(CommonConstant.ACCOUNT_ID, String.class));
        paragraphDocument.setLeadsId(activeRecordData.get(CommonConstant.LEADS_ID, String.class));
        paragraphDocument.setNewOpportunityId(activeRecordData.get(CommonConstant.NEW_OPPORTUNITY_ID, String.class));
        // 设置时间戳
        long currentTime = System.currentTimeMillis();
        paragraphDocument.setCreateTime(currentTime);
        paragraphDocument.setLastUpdateTime(currentTime);
        paragraphDocument.setType(type);
        return paragraphDocument;
    }

    /**
     * 批量保存段落文档
     */
    public void saveParagraphDocuments(List<ParagraphDocument> paragraphDocuments, User user) {
        try {
            paragraphMongoDao.batchInsert(user.getTenantId(), paragraphDocuments);
            log.info("批量保存段落文档成功，文档数量={}", paragraphDocuments.size());
        } catch (Exception e) {
            log.error("批量保存段落文档失败", e);
        }
    }

    /**
     * 时间范围内部类
     */
    private static class TimeRange {
        private final String startTime;
        private final String endTime;

        public TimeRange(String startTime, String endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        public String getStartTime() {
            return startTime;
        }

        public String getEndTime() {
            return endTime;
        }
    }
}