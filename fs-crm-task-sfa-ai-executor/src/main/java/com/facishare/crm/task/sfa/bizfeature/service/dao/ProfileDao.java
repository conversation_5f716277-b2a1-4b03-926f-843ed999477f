package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProfileDao {

    @Autowired
    private ServiceFacade serviceFacade;
    /**
     * 查询特征by dimension
     */
    public List<IObjectData> fetchLastProfilesByObjectId(User user, String objectId, String type) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileConstants.TYPE, type);
        if (type.equals(ProfileConstants.Type.ACCOUNT.getValue())) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileConstants.ACCOUNT_ID, objectId);
        }
        if (type.equals(ProfileConstants.Type.LEAD.getValue())) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileConstants.LEAD_ID, objectId);
        }
        if (type.equals(ProfileConstants.Type.OPPORTUNITY.getValue())) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileConstants.OPPORTUNITY_ID, objectId);
        }
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileConstants.IS_LATEST, "true");
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.PROFILE, query).getData();
    }
}
