package com.facishare.crm.task.sfa.util;


import com.facishare.crm.task.sfa.services.SFALicenseService;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 监听Liccense，初始化业务处理 class
 *
 * <AUTHOR>
 * @date 2021/1/16
 */
@Slf4j
public class LicenseBusinessProcessUtil {
    private static final SFALicenseService sfaLicenseService = SpringUtil.getContext().getBean(SFALicenseService.class);


    public static final String FXIAOKE_AI_PLATFORM_APP = "fxiaoke_ai_platform_app";


    public static boolean checkAILicense(String tenantId) {
       return sfaLicenseService.checkModuleLicenseExist(tenantId,FXIAOKE_AI_PLATFORM_APP);
    }
}
