package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivitySuggestIssueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ActivitySuggestIssueListener extends AbstractActivityCommonListener {
    @Autowired
    private ActivitySuggestIssueService activitySuggestIssueService;

    @Override
    String getSection() {
        return "sfa_activity_suggest_issues";
    }

    @Override
    void consume(ActivityMessage activityMessage) {
        String stage = activityMessage.getStage();
        if ("realtime2text".equals(stage)) {
            return;
        }
        activitySuggestIssueService.consumer(activityMessage);
    }
}
