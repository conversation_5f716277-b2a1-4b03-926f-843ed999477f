package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl.feature;

import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SkillScoreInsightHandler extends AbstractInsightHandler<AttendeesInsightModel.FeatureInsightResult> {
    private static final String PROMPT = "prompt_attendee_insight_skill_score";

    @Override
    public String getInsightType() {
        return "skill_score";
    }

    @Override
    public void insight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        doInsight(attendeesInsightMessage);
    }


    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        featureInsight(attendeesInsightMessage, PROMPT);
    }

    @Override
    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return super.getOurSideNames(insightMessage);
    }

    @Override
    protected void addSceneVariables(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, Map<String, Object> sceneVariables) {
        addMeetingObjectives(attendeesInsightMessage, sceneVariables);
    }
    protected String getFeatureFieldName() {
        return "skill_score";
    }

    @Override
    protected String getCorpus(AttendeesInsightModel.AttendeesInsightExtendData extendData) {
        return super.getQuestionCorpus(extendData);
    }

    @Override
    protected String getParticipantTypes() {
        return "our_side";
    }

}
