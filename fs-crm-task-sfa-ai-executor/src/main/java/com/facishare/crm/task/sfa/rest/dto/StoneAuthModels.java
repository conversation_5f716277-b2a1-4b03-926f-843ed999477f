package com.facishare.crm.task.sfa.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Stone Auth 相关的请求响应模型
 */
public interface StoneAuthModels {

    @Data
    class NoSignAcUrlRequest {
        private String path;
        private String fileTenantId;
        private String filename;
        private String extension;
        private Integer expireTime;
        private String business;
        private String tenantId;
        private String userId;
        private String outTenantId;
        private String outUserId;
        private String upstreamOwnerId;
    }

    @Data
    class NoSignAcUrlResponse {
        private Integer code;
        private String message;
        private String data;
    }

    @Data
    class GenerateDownloadUrlRequest {
        private String employeeAccount;
        private String enterpriseId;
        private String path;
        private Integer expireTime;
    }

    @Data
    class GenerateDownloadUrlResponse {
        private boolean success;
        private Integer code;
        private String message;
        private String data;
    }
    /**
     * {
     *  "success": true,
     *  "code": 200,
     *  "message": "请求成功",
     *  "data": "ALIOSS_c242994eda3c47e983ba78029d1d4b3e"
     * }
     */

    @Data
    class GenerationPathByObjectKeyResponse {
        private boolean success;
        private Integer code;
        private String message;
        private filePathData data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class filePathData{
        private String path;
        private String fileSize;
    }



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class GetTingWuCredentialResponse {
        private Boolean success;
        private Integer code;
        private String message;
        private TingWuCredentialData data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class TingWuCredentialData {
        private String appKey;
        private String accessKey;
        private String secretKey;
        private String stsToken;
        private String endPoint;
        private String transResultOssBucket;
    }

    @Data
    class AudioInfo {
        private boolean success;
        private int code;
        private String message;
        private AudioData data;
    }

    @Data
    class AudioData {
        private String filePath;
        private String fileName;
        private BigDecimal duration;
        private long fileSize;
        private String fileExt;
        private String objectKey;
    }
} 