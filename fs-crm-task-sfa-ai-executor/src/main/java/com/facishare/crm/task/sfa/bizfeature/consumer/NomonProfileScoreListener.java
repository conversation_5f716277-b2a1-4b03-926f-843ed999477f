package com.facishare.crm.task.sfa.bizfeature.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.crm.task.sfa.bizfeature.service.NomonProfileScoreService;
import com.facishare.crm.task.sfa.bizfeature.service.ProfileCommonService;
import com.facishare.crm.task.sfa.services.SFALicenseService;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 定时画像打分监听器
 */
@Slf4j
@Component
public class NomonProfileScoreListener implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private NomonProfileScoreService nomonProfileScoreService;

    @Autowired
    private ProfileCommonService profileCommonService;

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private SFALicenseService sfaLicenseService;

    private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");

    private static final String PROFILE_AGENT = "sales_portrait_insight_agent_app";

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "crm-profile-nomon-consumer", (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    ProfileScoreModel featureScoring = messageParse(msg);
                    consume(featureScoring);
                } catch (Exception e) {
                    log.error("NomonProfileScoreListener error :{}", msg, e);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    private void consume(ProfileScoreModel profileScoreModel) {
        try {
            if (profileScoreModel == null || StringUtils.isEmpty(profileScoreModel.getTenantId())) {
                log.error("nomon profile score error, profileScoreModel:{}", profileScoreModel);
                return;
            }
            if (gray.isAllow("profile-score-skip-tenant", profileScoreModel.getTenantId())) {
                return;
            }
            /*if (!sfaLicenseService.checkModuleLicenseExist(profileScoreModel.getTenantId(), PROFILE_AGENT)) {
                log.info("profile agent not exist, tenantId:{}", profileScoreModel.getTenantId());
                return;
            }*/
            nomonProfileScoreService.scoreCalc(profileScoreModel);
        } catch (Exception e) {
            log.error("nomon profile score error, profileScoreModel:{}", profileScoreModel, e);
        } finally {
            //创建下次任务
            if (profileScoreModel != null) {
                profileCommonService.createNextTask(profileScoreModel.getTenantId());
            }
        }
    }

    private ProfileScoreModel messageParse(MessageExt messageExt) {
        try {
            return JSON.parseObject(StringHelper.toString(messageExt.getBody()), ProfileScoreModel.class);
        } catch (Exception e) {
            log.error("nomon profile scoring message format failed. msgId:{}, body:{}", messageExt.getMsgId(), StringHelper.toString(messageExt.getBody()));
        }
        return null;
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}