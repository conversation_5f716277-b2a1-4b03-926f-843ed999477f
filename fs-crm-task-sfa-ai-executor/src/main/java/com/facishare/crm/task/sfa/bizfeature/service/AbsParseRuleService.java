package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.fastjson.util.TypeUtils;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureValueConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ParseRuleConstants;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.crm.task.sfa.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.functions.utils.Lists;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

@Slf4j
public abstract class AbsParseRuleService implements ParseRuleService {
    protected static final List<String> CONVERT_FIELD_TYPE = Lists.newArrayList(IFieldType.OBJECT_REFERENCE, IFieldType.SELECT_ONE);
    @Resource
    protected FieldDataConverterManager fieldDataConverterManager;
    @Resource
    protected ServiceFacade serviceFacade;

    @Override
    public FeatureModel.FeatureData parse(User user, IObjectData feature, IObjectData rule, IObjectData afterData, IObjectDescribe dataDescribe) {
        FeatureModel.FeatureData featureData = new FeatureModel.FeatureData();
        featureData.setReturnDataType(rule.get(ParseRuleConstants.RETURN_DATA_TYPE, String.class));

        try {
            FeatureModel.ParseValueData value = getValue(user, feature, rule, afterData, dataDescribe);
            if (value == null || value.getValue() == null) {
                return null;
            }

            boolean retFlag = makeFeatureData(user, feature, rule, afterData, featureData, value);
            if (!retFlag) {
                return null;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

        return featureData;
    }

    protected boolean makeFeatureData(User user, IObjectData feature, IObjectData rule, IObjectData afterData,
            FeatureModel.FeatureData featureData, FeatureModel.ParseValueData value) {

        boolean ret = setRealData(featureData, value);
        if (!ret) {
            return false;
        }

        setMasterInfo(user, feature, rule, afterData, featureData);
        return !StringUtils.isNotBlank(featureData.getMasterApiName()) || !StringUtils.isBlank(featureData.getMasterId());
    }

    protected void setMasterInfo(User user, IObjectData feature, IObjectData rule, IObjectData afterData, FeatureModel.FeatureData featureData) {
        String masterApiName = feature.get(FeatureConstants.MASTER_OBJECT_API_NAME, String.class);
        if (StringUtils.isNotBlank(masterApiName) && !masterApiName.equals(afterData.getDescribeApiName())) {
            featureData.setMasterApiName(masterApiName);
            featureData.setMasterId(findMasterId(user, feature, rule, afterData));
        }
    }

    private boolean setRealData(FeatureModel.FeatureData featureData, FeatureModel.ParseValueData value) {
        try {
            Object realValue = value.getValue();

            if (featureData.getReturnDataType().equals(ParseRuleConstants.ReturnDataType.TEXT.getReturnDataType())) {
                featureData.setValueText(realValue.toString());
            } else if (featureData.getReturnDataType()
                    .equals(ParseRuleConstants.ReturnDataType.BOOL.getReturnDataType())) {
                featureData.setValueBoolean(TypeUtils.castToBoolean(realValue));
            } else if (featureData.getReturnDataType()
                    .equals(ParseRuleConstants.ReturnDataType.NUMERIC.getReturnDataType())) {
                featureData.setValueNumber(TypeUtils.castToDouble(realValue));
            }
            Map<String, Object> triggerValue = new HashMap<>();
            if (!CollectionUtils.isEmpty(value.getTriggerValue())) {
                triggerValue.put("type", "field");
                triggerValue.put("trigger_value", value.getTriggerValue());
                triggerValue.put(FeatureValueConstants.TRIGGER_OBJECT_API_NAME, value.getObjectApiName());
                triggerValue.put(FeatureValueConstants.TRIGGER_OBJECT_ID, value.getObjectId());
            } else {
                triggerValue.put("type", "feature_value");
                triggerValue.put("data_type", featureData.getReturnDataType());
                triggerValue.put("feature_value", realValue);
            }
            featureData.setTriggerValue(triggerValue);

        } catch (Exception e) {
            log.error("parse rule error", e);
            return false;
        }
        return true;
    }

    protected abstract FeatureModel.ParseValueData getValue(User user, IObjectData feature, IObjectData rule,
            IObjectData afterData, IObjectDescribe dataDescribe);

    protected Object comparisonFieldValue(User user, IObjectData feature, IObjectData rule, IObjectData afterData) {
        String field = feature.get(FeatureConstants.COMPARISON_FIELD, String.class);
        if (StringUtils.isBlank(field)) {
            return null;
        }
        if (field.startsWith(FeatureConstants.CONFIG_KEY)) {
            String key = field.substring(FeatureConstants.CONFIG_KEY.length());
            return SFAConfigUtil.getConfigValue(user.getTenantId(), key, User.SUPPER_ADMIN_USER_ID);
        }

        Object value;
        String[] fields = field.split("\\.");
        if (fields.length == 1 || fields.length == 0) {
            value = afterData.get(field);
        } else {
            List<IObjectData> refData = getRefData(user, afterData, fields[0]);
            if (refData.isEmpty()) {
                return null;
            }
            value = refData.get(0).get(fields[1]);
        }

        return value;
    }

    private List<IObjectData> getRefData(User user, IObjectData afterData, String field) {
        String ref = afterData.get(field, String.class);
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(),
                afterData.getDescribeApiName());
        IFieldDescribe refField = objectDescribe.getFieldDescribe(field);
        String refFieldApiName = (String) refField.get("target_api_name");

        return serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(ref),
                refFieldApiName);
    }

    protected String findMasterId(User user, IObjectData feature, IObjectData rule, IObjectData afterData) {
        String masterApiName = feature.get(FeatureConstants.MASTER_OBJECT_API_NAME, String.class);
        String masterFieldApiName = feature.get(FeatureConstants.MASTER_FIELD_API_NAME, String.class);
        if (StringUtils.isBlank(masterApiName) || StringUtils.isBlank(masterFieldApiName)) {
            return null;
        }
        String[] fields = masterFieldApiName.split(".");
        String masterId;
        if (fields.length > 1) {
            List<IObjectData> refData = getRefData(user, afterData, fields[0]);
            if (refData.isEmpty()) {
                return null;
            }
            masterId = refData.get(0).get(fields[1], String.class);

        } else {
            masterId = afterData.get(masterFieldApiName, String.class);
        }

        return masterId;
    }

    protected FeatureModel.ParseExtData getExtData(IObjectData afterData) {
        Object value = afterData.get(FeatureConstants.PARSE_EXT_DATA);
        if (value == null) {
            return null;
        }

        return (FeatureModel.ParseExtData) value;
    }

    public String[] getFields(IObjectData feature) {
        String fieldInfo = feature.get(FeatureConstants.DATA_SOURCE_FIELD, String.class);
        return getFields(fieldInfo);
    }

    protected String[] getFields(String fieldInfo) {
        String[] fields = new String[] {fieldInfo};
        if (fieldInfo.startsWith(FeatureConstants.FIELDS)) {
            fieldInfo = fieldInfo.substring(FeatureConstants.FIELDS.length());
            fields = fieldInfo.split(",");
        }
        return fields;
    }

    @NotNull
    protected Map<String, Object> getSrcObjectMap(IObjectData data, String[] fields) {
        Map<String, Object> srcValue = new HashMap<>();
        for (String field : fields) {
            if(field.contains(".")){
                continue;
            }
            srcValue.put(field, data.get(field));
        }
        return srcValue;
    }
}
