package com.facishare.crm.task.sfa.bizfeature.service;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ParseServiceManager implements ApplicationContextAware {
    private final Map<String, ParseRuleService> actionServiceHashMap = Maps.newHashMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initPolicyRuleServiceMap(applicationContext);
    }

    private void initPolicyRuleServiceMap(ApplicationContext applicationContext) {
        Map<String, ParseRuleService> springBeanMap1 = applicationContext.getBeansOfType(ParseRuleService.class);
        springBeanMap1.values().forEach(provider -> {
            if (!Strings.isNullOrEmpty(provider.getRuleType())) {
                actionServiceHashMap.put(provider.getRuleType(), provider);
            }
        });

    }

    public ParseRuleService getActionService(String type) {
        return actionServiceHashMap.getOrDefault(type, null);
    }
}
