package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.facishare.crm.task.sfa.activitysummary.constants.AIAgentConstants;
import com.facishare.crm.task.sfa.activitysummary.model.*;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多轮对话段落分割服务类
 * 专门处理基于多轮对话的AI Agent段落分割逻辑
 *
 * 多轮对话流程:
 * 1. Planner: 分析文本并制定分段策略
 * 2. Executor: 根据策略执行分段
 * 3. Evaluator: 评估分段结果质量
 * 4. Adjuster: 根据评估结果调整策略
 * 5. 重复执行直到达到质量要求或达到最大调整轮数
 */
@Service
@Slf4j
public class MultiRoundChatParagraphService {

    @Autowired
    private ConverterService converterService;

    @Autowired
    private ParagraphChatManager chatManager;

    @Autowired
    private AIAgentExecutor agentExecutor;

    // 配置常量，从AIAgentConstants中获取
    private static final int MAX_ADJUSTMENT_ROUNDS = AIAgentConstants.MAX_ADJUSTMENT_ROUNDS; // 最大调整轮数
    private static final double QUALITY_THRESHOLD = AIAgentConstants.QUALITY_THRESHOLD; // 质量评分阈值

    /**
     * 使用多轮对话方式处理内容分段
     *
     * @param user            用户信息
     * @param meetContent     会议内容
     * @param previousSummary 上一次摘要
     * @param currentBatchIds 当前批次ID列表
     * @param contentList     内容列表
     * @return 段落结果模型
     */
    public ParagraphResultModel processContentSegmentationWithMultiRoundChat(User user, String meetContent, String previousSummary, List<String> currentBatchIds, List<String> contentList) {
        try {
            Map<String, String> idToContentMap = extractIdToContentMap(currentBatchIds, contentList);
            log.info("开始多轮对话AI Agent分段流程，内容长度: {}, 有摘要: {}",
                    meetContent.length(), StringUtils.isNotEmpty(previousSummary));

            // 初始化对话历史
            ParagraphChatManager.ChatHistories chatHistories = chatManager.createChatHistories();

            // 执行Planner阶段
            PlanModel plan = agentExecutor.executePlanner(user, meetContent, previousSummary, chatHistories.getPlannerHistory());
            if (plan == null) {
                log.error("多轮对话Planner执行失败，无法获取分段策略");
                return null;
            }

            // 执行Executor阶段
            List<SegmentModel> segments = agentExecutor.executeExecutor(user, meetContent, previousSummary, plan, 
                    chatHistories.getExecutorHistory(), chatHistories.getPlannerHistory().getLastAssistantMessage());
            if (CollectionUtils.isEmpty(segments)) {
                log.error("多轮对话Executor执行失败，无法获取分段结果");
                return null;
            }

            // 执行Evaluator阶段
            EvaluationModel evaluation = agentExecutor.executeEvaluator(user, meetContent, previousSummary, segments, 
                    idToContentMap, chatHistories.getEvaluatorHistory(), chatHistories.getExecutorHistory().getLastAssistantMessage());
            if (evaluation == null) {
                log.error("多轮对话Evaluator执行失败，无法评估分段质量");
                return null;
            }

            log.info("多轮对话Evaluator执行成功，总体评分: {}, 是否需要修正: {}",
                    evaluation.getOverallScore(), evaluation.isNeedsRevision());

            // 执行多轮调整
            ChatResultTracker resultTracker = new ChatResultTracker(evaluation.getOverallScore(), segments, evaluation);
            EvaluationModel finalEvaluation = performAdjustmentRounds(user, meetContent, previousSummary, plan, 
                    evaluation, segments, idToContentMap, chatHistories, resultTracker);

            // 获取最佳结果
            EvaluationModel bestEvaluation = getBestResult(finalEvaluation, resultTracker);
            List<SegmentModel> bestSegments = (bestEvaluation == resultTracker.getBestEvaluation()) ? 
                    resultTracker.getBestSegments() : segments;

            // 转换为ParagraphResultModel
            return converterService.convertToParagraphResultModel(bestSegments, bestEvaluation);

        } catch (Exception e) {
            log.error("多轮对话AI Agent分段流程执行失败", e);
            return null;
        }
    }

    /**
     * 执行多轮调整
     */
    private EvaluationModel performAdjustmentRounds(User user, String meetContent, String previousSummary, 
            PlanModel plan, EvaluationModel evaluation, List<SegmentModel> segments, 
            Map<String, String> idToContentMap, ParagraphChatManager.ChatHistories chatHistories, ChatResultTracker resultTracker) {
        
        int adjustmentRound = 0;
        EvaluationModel currentEvaluation = evaluation;
        List<SegmentModel> currentSegments = segments;

        while (currentEvaluation.isNeedsRevision() &&
                currentEvaluation.getOverallScore() < QUALITY_THRESHOLD &&
                adjustmentRound < MAX_ADJUSTMENT_ROUNDS) {

            adjustmentRound++;
            log.info("分段质量不满足要求，开始第{}轮多轮对话调整", adjustmentRound);

            // 重新执行Executor
            List<SegmentModel> newSegments = agentExecutor.reExecuteExecutor(user, meetContent, previousSummary, 
                    chatHistories.getExecutorHistory(), chatHistories.getPlannerHistory().getLastAssistantMessage());
            
            if (CollectionUtils.isEmpty(newSegments)) {
                log.error("第{}轮多轮对话调整后执行分段失败，使用上一轮结果", adjustmentRound);
                continue;
            }

            // 重新评估
            EvaluationModel newEvaluation = agentExecutor.reExecuteEvaluator(user, meetContent, previousSummary, 
                    newSegments, idToContentMap, chatHistories.getEvaluatorHistory(), 
                    chatHistories.getExecutorHistory().getLastAssistantMessage());
            
            if (newEvaluation == null) {
                log.error("第{}轮多轮对话调整后评估失败，继续下一轮", adjustmentRound);
                continue;
            }

            log.info("第{}轮多轮对话调整后评分: {} -> {}",
                    adjustmentRound, currentEvaluation.getOverallScore(), newEvaluation.getOverallScore());

            // 更新结果跟踪器
            resultTracker.updateIfBetter(newEvaluation.getOverallScore(), newSegments, newEvaluation, adjustmentRound);

            // 更新当前状态
            currentEvaluation = newEvaluation;
            currentSegments = newSegments;

            // 如果已经达到质量阈值，停止调整
            if (newEvaluation.getOverallScore() >= QUALITY_THRESHOLD) {
                log.info("已达到质量阈值，停止调整");
                break;
            }
        }

        if (adjustmentRound > 0) {
            log.info("共进行了{}轮多轮对话调整，最终评分: {}, 段落数: {}",
                    adjustmentRound, currentEvaluation.getOverallScore(), currentSegments.size());
        }

        return currentEvaluation;
    }

    /**
     * 获取最佳结果
     */
    private EvaluationModel getBestResult(EvaluationModel currentEvaluation, ChatResultTracker resultTracker) {
        if (currentEvaluation.getOverallScore() < QUALITY_THRESHOLD && 
                resultTracker.getBestEvaluation().getOverallScore() > currentEvaluation.getOverallScore()) {
            log.info("未达到质量阈值，使用最高得分的结果，得分: {}", resultTracker.getBestEvaluation().getOverallScore());
            return resultTracker.getBestEvaluation();
        }
        return currentEvaluation;
    }

    /**
     * 从会议内容中提取ID到原文的映射
     * 会议内容格式假设为: 【ID】内容\n【ID】内容\n...
     *
     * @param currentBatchIds 会议内容id
     * @param contentList     会议内容
     * @return ID到原文的映射
     */
    private Map<String, String> extractIdToContentMap(List<String> currentBatchIds, List<String> contentList) {
        Map<String, String> idToContentMap = new HashMap<>();
        for (int i = 0; i < currentBatchIds.size(); i++) {
            idToContentMap.put(currentBatchIds.get(i), contentList.get(i));
        }
        return idToContentMap;
    }
}