package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.crm.task.sfa.rest.FsBigFileManagerProxy;
import com.facishare.crm.task.sfa.rest.dto.StoneAuthModels;
import com.facishare.crm.task.sfa.util.HttpHeaderUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.task.sfa.common.constants.CommonConstant.ACTIVE_RECORD_API_NAME;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/17 10:52
 * @description:
 */
@Slf4j
public abstract class AbstractFile2Text implements File2Text{

    @Resource
    protected GDSHandler gdsHandler;

    @Resource
    protected Rec2TextService rec2TextService;

    @Resource
    private ActivityRocketProducer activityRocketProducer;

    @Resource
    private InteractiveScenarioService interactiveScenarioService;

    @Resource
    private FsBigFileManagerProxy fsBigFileManagerProxy;


    /**
     * 获取 path 加上后缀
     * @param message
     * @return
     */
    public List<String> getPathWithExt(ActivityMessage message){
            IObjectData activityData = rec2TextService.findById(message);
            if (activityData == null || activityData.get("interaction_records") == null) {
                log.error("not found objectdata, message: {}", message);
            }
            List<String> pathWithExt = Lists.newArrayList();
            List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) activityData.get("interaction_records");
//            List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) activityData.get("field_tdYhe__c");
            for (Map<String, Object> interactionRecord : interactionRecords) {
                String path = (String) interactionRecord.get("path");
                String extension = (String) interactionRecord.get("ext");
                if (!path.endsWith(extension)) {
                    path = path + "." + extension;
                }
                pathWithExt.add(path);
            }
            return pathWithExt;
    }

    /**
     * 获取完整的HTTPS URL路径（用于图片处理）
     * @param message
     * @return 完整的HTTPS URL列表
     */
    public List<String> getFullHttpsUrls(ActivityMessage message){
        IObjectData activityData = rec2TextService.findById(message);
        if (activityData == null || activityData.get("interaction_records") == null) {
            log.warn("not found objectdata, message: {}", message);
            return Lists.newArrayList();
        }

        List<String> fullUrls = Lists.newArrayList();
        List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) activityData.get("interaction_records");
        String ea = gdsHandler.getEAByEI(message.getTenantId());
        String userId = message.getOpId();

        for (Map<String, Object> interactionRecord : interactionRecords) {
            String path = (String) interactionRecord.get("path");
            //String extension = (String) interactionRecord.get("ext");
            //if (!path.endsWith(extension)) {
            //    path = path + "." + extension;
            //}
            // 使用getNoSignAcUrl获取完整的HTTPS URL
            String fullUrl = getNoSignAcUrl(message.getTenantId(), ea, userId, path);
            if (fullUrl != null) {
                fullUrls.add(fullUrl);
                log.info("Generated full HTTPS URL for path {}: {}", path, fullUrl);
            } else {
                log.error("Failed to generate HTTPS URL for path: {}", path);
                // 如果获取URL失败，仍然添加原始路径作为备选
                fullUrls.add(path);
            }
        }
        return fullUrls;
    }



    /**
     * 获取无签名访问URL
     *
     * @param path              文件路径
     * @param enterpriseAccount 当前租户ea
     * @param userId            当前用户ID
     * @return 无签名访问URL
     */
    public String getNoSignAcUrl(String ei,String enterpriseAccount, String userId, String path) {
        Map<String, String> pathParams = Maps.newHashMap();
        pathParams.put("employeeAccount", enterpriseAccount);
        pathParams.put("employeeId", userId);
        pathParams.put("path", path);
        pathParams.put("expireTime", "360000");

        StoneAuthModels.GenerateDownloadUrlResponse response = fsBigFileManagerProxy.generateDownloadUrl(HttpHeaderUtil.getHeaders(new User(ei,userId)),pathParams);

        if (response != null && response.isSuccess() && response.getCode() == 200) {
            return response.getData();
        }
        log.error("获取文件下载签名URL失败, path={}, response={}", path, response);
        return null;
    }

    public IObjectData findById(ActivityMessage message) {
        return rec2TextService.findById(message);
    }

    /**
     * 更新活动文本
     *
     * @param tenantId 租户ID
     * @param objectId 对象ID
     * @param objectApiName 对象API名称
     * @param fullText 语音识别结果
     */
    protected  void updateActivityText(String tenantId, String objectId, String objectApiName, String fullText) {
        if (ACTIVE_RECORD_API_NAME.equals(objectApiName)) {
            interactiveScenarioService.checkInteractiveScenario(tenantId, objectId);
        }
        rec2TextService.updateActivityText(tenantId, objectId, objectApiName, fullText);
    }

    protected void sendActivityMessage(ActivityMessage message) {
        message.setStage("file2text");
        message.setActionCode("file2text");
        activityRocketProducer.sendActivityToTextMessage(message);
    }
}
