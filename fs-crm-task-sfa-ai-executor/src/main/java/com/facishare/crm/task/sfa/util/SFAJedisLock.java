package com.facishare.crm.task.sfa.util;

import com.github.jedis.support.JedisCmd;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023-03-14 08:22:57
 */
@Slf4j
public class SFAJedisLock implements AutoCloseable {

    private final JedisCmd jedis;
    private final String lockKey;
    /**
     * 锁超时时间，防止线程在入锁以后，无限的执行等待，毫秒 ，2小时
     */
    private int expireMS = 60 * 60 * 2000;

    /**
     * 秒
     */
    private final int expireS = 60 * 60;

    private volatile boolean locked = false;

    public SFAJedisLock(JedisCmd jedis, String lockKey) {
        this.jedis = jedis;
        this.lockKey = lockKey;
    }

    public SFAJedisLock(JedisCmd jedis, String lockKey, int expireMS) {
        this(jedis, lockKey);
        this.expireMS = expireMS;
    }

    public synchronized boolean tryLock() {
        String lockValue = String.valueOf(System.currentTimeMillis() + expireMS);
        if (jedis.setnx(lockKey, lockValue) == 1) {
            return expireLockKey();
        }
        // 判断锁是否超时，如果超时则重新设置，锁超时，获取到锁。
        String currentValue = jedis.get(lockKey);
        if (currentValue != null && Long.parseLong(currentValue) < System.currentTimeMillis()) {
            lockValue = String.valueOf(System.currentTimeMillis() + expireMS);
            String oldValue = jedis.getSet(lockKey, lockValue);
            if (oldValue != null && oldValue.equals(currentValue)) {
                return expireLockKey();
            }
        }
        return false;
    }

    /**
     * 谨慎使用,tryLock获取锁之后，对持有的锁进行续期。
     * @param lockKey
     * @return
     */
    public boolean resetExpireLock(String lockKey){
        String lockValue = String.valueOf(System.currentTimeMillis() + expireMS);
        jedis.set(lockKey,lockValue);
        expireLockKey();
        return true;
    }

    private boolean expireLockKey() {
        // 设置一个较长时间的有效期，避免获取锁之后，执行动作时间过长，由于过期而失去锁
        jedis.expire(lockKey, expireS);
        locked = true;
        return true;
    }

    public synchronized void unlock() {
        if (locked) {
			log.info("del lockKey:{}", lockKey);
			jedis.del(lockKey);
			locked = false;
        }
    }


    public synchronized boolean waitTryLock(long waitTime){
		long spinTime = System.currentTimeMillis() + waitTime;
		while (!tryLock() && System.currentTimeMillis() < spinTime ){
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
		log.info("tryLock success! key:{}, locked:{}", lockKey, locked);
        return locked;
    }


    @Override
    public void close() throws Exception {
        unlock();
    }
}
