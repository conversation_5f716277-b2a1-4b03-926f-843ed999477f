package com.facishare.crm.task.sfa.bizfeature.consumer;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.model.RequirementModel;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileConstants;
import com.facishare.crm.task.sfa.bizfeature.enums.OpenStatusEnum;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureCrmNoteContext;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureInitModel;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileAdviceModel;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.crm.task.sfa.bizfeature.service.*;
import com.facishare.crm.task.sfa.common.enums.ConfigType;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * 实时画像打分监听器
 */
@Slf4j
@Component
public class ProfileAdviceListener implements ApplicationListener<ContextRefreshedEvent> {

    @Resource
    private ProfileAdviceService profileAdviceService;
    @Resource
    private ProfileProsConsService profileProsConsService;
    @Resource
    private FeatureInitService featureInitService;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private CrmNoteManager crmNoteManager;
    @Resource
    protected ConfigService configService;

    private AutoConfMQPushConsumer consumer;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "crm-feature-advice-consumer", (MessageListenerConcurrently) (msgs, context) -> {
             for (MessageExt msg : msgs) {
                 if (Objects.isNull(msg.getTags())) {
                     try {
                         ProfileAdviceModel featureAdvice = messageProfileAdviceModel(msg);
                         log.info("ProfileAdviceListener_start_{}_{}", featureAdvice.getProfileId(), featureAdvice);
                         consume(featureAdvice);
                         log.info("ProfileAdviceListener_end");
                     } catch (Exception e) {
                         log.error("ProfileAdviceService error :{}", msg, e);
                         //throw new RuntimeException(e);
                     }
                 } else {
                     if ("feature-init-data".equals(msg.getTags())) {
                         try {
                             FeatureInitModel featureInitModel = messageParseFeatureInitModel(msg);
                             log.info("FeatureInitModelListener_start_{}", featureInitModel.getTenantId());
                             consumeFeatureInit(featureInitModel);
                             log.info("FeatureInitModelListener_start_");
                         } catch (Exception e) {
                             log.error("FeatureInitModelListener_start_ error :{}", msg, e);
                             //throw new RuntimeException(e);
                         }
                     }
                 }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    private void consume(ProfileAdviceModel profileAdviceModel) {
        FeatureCrmNoteContext featureCrmNoteContext = FeatureCrmNoteContext.builder()
                .tenantId(profileAdviceModel.getTenantId()).receiverIds(Lists.newArrayList(Integer.valueOf(profileAdviceModel.getReceiverId())))
                .refreshType(profileAdviceModel.getRefreshType()).build();
        try {
            if (StringUtils.isAnyEmpty(profileAdviceModel.getTenantId(), profileAdviceModel.getProfileId())) {
                featureCrmNoteContext.setSucess(false);
                log.error("generateProfileProsCons param error ");
            }
            fillProfileAdviceModel(profileAdviceModel, featureCrmNoteContext);
            //生成优劣势
            profileProsConsService.generateProfileProsCons(profileAdviceModel, featureCrmNoteContext);
            //生成意见建议
            profileAdviceService.generateAdvice(profileAdviceModel, featureCrmNoteContext);
            featureCrmNoteContext.setSucess(true);
        } catch (Exception ex) {
            log.error("generateProfileProsCons unknown exception : ", ex);
            featureCrmNoteContext.setSucess(false);
            throw ex;
        } finally {
            log.info("ProfileAdviceListener_sendCrmNote_{}", featureCrmNoteContext);
            crmNoteManager.sendCrmNote(featureCrmNoteContext);
        }
    }

    private void consumeFeatureInit(FeatureInitModel param) {
        try {
            featureInitService.initModule(param);
            setConfigValue(User.systemUser(param.getTenantId()), OpenStatusEnum.OPENED.getStatus());
            crmNoteManager.sendFeatureInitNote(param.getTenantId(), param.getReceiverId(), true);
        } catch (Exception ex) {
            log.error("generateProfileProsCons unknown exception : ", ex);
            setConfigValue(User.systemUser(param.getTenantId()), OpenStatusEnum.FAILED.getStatus());
            crmNoteManager.sendFeatureInitNote(param.getTenantId(), param.getReceiverId(), false);
        }
    }

    private ProfileAdviceModel messageProfileAdviceModel(MessageExt messageExt) {
        try {
            return JSON.parseObject(StringHelper.toString(messageExt.getBody()), ProfileAdviceModel.class);
        } catch (Exception e) {
            log.error("profile advice message format failed. msgId:{}, body:{}", messageExt.getMsgId(), StringHelper.toString(messageExt.getBody()));
        }
        return null;
    }
    private FeatureInitModel messageParseFeatureInitModel(MessageExt messageExt) {
        try {
            return JSON.parseObject(StringHelper.toString(messageExt.getBody()), FeatureInitModel.class);
        } catch (Exception e) {
            log.error("feature init message format failed. msgId:{}, body:{}", messageExt.getMsgId(), StringHelper.toString(messageExt.getBody()));
        }
        return null;
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    private void fillProfileAdviceModel(ProfileAdviceModel param, FeatureCrmNoteContext featureCrmNoteContext) {
        String profileId = param.getProfileId();
        if (com.google.common.base.Strings.isNullOrEmpty(profileId)) {
            return;
        }

        // 1. 查询画像及所有分项得分
        User user = User.systemUser(param.getTenantId());
        IObjectData profile = serviceFacade.findObjectData(user, profileId, FeatureConstants.PROFILE);
        if (profile == null) {
            return;
        }
        String type = profile.get(ProfileConstants.TYPE, String.class);
        String objectId = StringUtil.EMPTY;
        String objectApiName = StringUtil.EMPTY;
        if (type.equals(ProfileConstants.Type.ACCOUNT.getValue())) {
            objectId = profile.get(ProfileConstants.ACCOUNT_ID, String.class);
            objectApiName = Utils.ACCOUNT_API_NAME;
        }
        if (type.equals(ProfileConstants.Type.LEAD.getValue())) {
            objectId = profile.get(ProfileConstants.LEAD_ID, String.class);
            objectApiName = Utils.LEADS_API_NAME;
        }
        if (type.equals(ProfileConstants.Type.OPPORTUNITY.getValue())) {
            objectId = profile.get(ProfileConstants.OPPORTUNITY_ID, String.class);
            objectApiName = Utils.NEW_OPPORTUNITY_API_NAME;
        }
        param.setProfile(profile);
        param.setObjectId(objectId);
        param.setObjectDescribeApiName(objectApiName);
        featureCrmNoteContext.setObjectId(objectId);
        featureCrmNoteContext.setApiName(objectApiName);
    }
    private void setConfigValue(User user, String value) {
        String queryRst = configService.findTenantConfig(user, ConfigType.CUSTOMER_PROFILE_AGENT.getKey());
        if (StringUtils.isBlank(queryRst)) {
            configService.createTenantConfig(user, ConfigType.CUSTOMER_PROFILE_AGENT.getKey(), value, ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(user, ConfigType.CUSTOMER_PROFILE_AGENT.getKey(), value, ConfigValueType.STRING);
        }
    }
}