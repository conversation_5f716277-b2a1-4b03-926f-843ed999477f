package com.facishare.crm.task.sfa.activitysummary.service.strategy;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.constant.ActivityTagMap;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTag;
import com.facishare.crm.task.sfa.activitysummary.model.DirectTaggingResultModel;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphContext;
import com.facishare.crm.task.sfa.activitysummary.service.ContentSplitter;
import com.facishare.crm.task.sfa.activitysummary.service.ParagraphAIService;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.ActivityParagraphService;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.DocumentProcessService;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureValueProducer;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.impl.search.Operator;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 直接打标签服务
 * on 2024/6/1
 */
@Service
@Slf4j
public class DirectTaggingService {

    @Autowired
    private ParagraphAIService paragraphAIService;

    @Autowired
    private ActivityParagraphService activityParagraphService;

    @Autowired
    private DocumentProcessService documentProcessService;

    @Autowired
    private ContentSplitter contentSplitter;

    @Autowired
    private ServiceFacade serviceFacade;

    private static boolean multipleMatchEnabled;

    private static int concurrentCount;

    @Resource(name = "activityTagExecutor")
    private ExecutorService executorService;

    @Autowired
    private FeatureValueProducer featureValueProducer;

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            multipleMatchEnabled = config.getBool("multipleMatchEnabled", false);
            concurrentCount = config.getInt("tagConcurrentCount", 3);
        });
    }

    public void process(User user, ActivityMessage activityMessage, String type) {
        String objectId = activityMessage.getObjectId();
        // 获取活动记录数据
        IObjectData activeRecordData = getActiveRecordData(user, objectId);
        if (activeRecordData == null) {
            return;
        }

        // 获取交互内容
        String interactiveContent = getInteractiveContent(user, activeRecordData, objectId, type);
        if (interactiveContent == null) {
            return;
        }

        // 对内容进行分割处理
        List<String> contentSegments = contentSplitter.splitContent(interactiveContent);
        if (CollectionUtils.isEmpty(contentSegments)) {
            return;
        }

        // 获取所有子标签
        List<ActivityTag> allChildTags = getAllChildTags();
        String tagsString = buildTagsString(allChildTags);
        Map<String, String> nameToId = buildNameToIdMap(allChildTags);

        // 处理每个内容段落
        List<DirectTaggingResultModel> allFinalResults = Lists.newArrayList();
        for (String contentSegment : contentSegments) {
            // 根据开关决定使用单次还是多次匹配
            List<DirectTaggingResultModel> segmentResults = multipleMatchEnabled
                    ? processWithMultipleMatch(user, contentSegment, tagsString, allChildTags, nameToId, objectId)
                    : processWithSingleMatch(user, contentSegment, tagsString, allChildTags, nameToId, objectId);

            if (CollectionUtils.isNotEmpty(segmentResults)) {
                allFinalResults.addAll(segmentResults);
            }
        }

        // 去重处理结果
        List<DirectTaggingResultModel> finalResults = deduplicateResults(allFinalResults);

        if (CollectionUtils.isEmpty(finalResults)) {
            log.info("[DirectTaggingService] No results found for objectId: {}", objectId);
            return;
        }

        // 处理结果并保存
        processAndSaveResults(user, activeRecordData, type, objectId, finalResults, nameToId);

        // 将ActivityTagMessage转换为FeatureMqModel.Message
        FeatureMqModel.Message featureMessage = FeatureMqModel.Message.builder()
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .objectId(objectId)
                .objectApiName(activityMessage.getObjectApiName())
                .build();
        // 发送特征计算消息
        featureValueProducer.sendTagMessage(featureMessage);
    }

    /**
     * 获取活动记录数据
     */
    private IObjectData getActiveRecordData(User user, String objectId) {
        return activityParagraphService.queryActiveRecord(user, objectId);
    }

    /**
     * 获取交互内容
     */
    private String getInteractiveContent(User user, IObjectData activeRecordData, String objectId, String type) {
        if (type.equals(ParagraphContext.MONGO_KEY)) {
            List<InteractiveDocument> interactiveDocuments = queryAllDocuments(user, objectId);
            if (CollectionUtils.isNotEmpty(interactiveDocuments)) {
                return formatMongoContent(user, interactiveDocuments, objectId);
            }
            return null;
        }
        return activityParagraphService.getInteractiveContent(activeRecordData, objectId);
    }

    /**
     * 查询所有交互文档
     * 循环查询所有页面的数据，直到获取完所有文档
     *
     * @param user     用户信息
     * @param objectId 对象ID
     * @return 所有交互文档列表
     */
    private List<InteractiveDocument> queryAllDocuments(User user, String objectId) {
        List<InteractiveDocument> allDocuments = Lists.newArrayList();

        try {
            // 设置最大页数限制（假设每页50条，最多查询1000页，即50000条记录）
            for (int pageNo = 0; pageNo <= 1000; pageNo++) {
                List<InteractiveDocument> pageDocuments = documentProcessService.queryDocumentsPage(
                        user.getTenantId(), objectId, pageNo);

                if (CollectionUtils.isEmpty(pageDocuments)) {
                    // 没有更多数据，退出循环
                    log.info("queryAllDocuments 完成, objectId: {}, 总页数: {}, 总文档数: {}",
                            objectId, pageNo, allDocuments.size());
                    break;
                }

                allDocuments.addAll(pageDocuments);

                // 检查是否达到最大页数限制
                if (pageNo == 1000) {
                    log.warn("queryAllDocuments 达到最大页数限制, objectId: {}, pageNo: {}", objectId, pageNo);
                }
            }

        } catch (Exception e) {
            log.error("queryAllDocuments error, objectId: {}", objectId, e);
        }

        return allDocuments;
    }

    /**
     * 格式化Mongo内容
     * 输出格式：姓名（时间）：具体的对话
     *
     * @param user            用户信息
     * @param interactiveList 交互文档列表
     * @param objectId        对象ID
     * @return 格式化后的内容
     */
    private String formatMongoContent(User user, List<InteractiveDocument> interactiveList, String objectId) {
        if (CollectionUtils.isEmpty(interactiveList)) {
            return "";
        }
        String speakLabel = I18N.text("sfa.activity.corpus.list_item_user_label");
        StringBuilder sb = new StringBuilder();

        // 查询活动用户信息并构建用户映射
        Map<String, String> userMap = queryActivityUserMap(user, objectId);

        for (InteractiveDocument document : interactiveList) {
            try {
                // 获取用户名
                String userName = getUserName(document, userMap, speakLabel);

                // 获取时间信息
                String timeInfo = document.getStartTime();

                // 构建格式：姓名（时间）：具体的对话
                sb.append(userName);
                if (StringUtils.isNotBlank(timeInfo)) {
                    sb.append("（").append(timeInfo).append("）");
                }
                sb.append("：").append(document.getContent()).append("\n");
            } catch (Exception e) {
                log.error("formatMongoContent error, objectId: {}", objectId, e);
            }
        }
        String resultStr = sb.toString();

        if (StringUtils.isBlank(resultStr) || resultStr.length() < 150) {
            log.warn("活动记录数据中没有交互内容，或少于150个字，objectId: {}", objectId);
            return null;
        }
        return resultStr;
    }

    /**
     * 查询活动用户映射
     *
     * @param user     用户信息
     * @param objectId 活动记录ID
     * @return 用户ID到用户名的映射
     */
    private Map<String, String> queryActivityUserMap(User user, String objectId) {
        SearchTemplateQueryPlus searchQuery = new SearchTemplateQueryPlus();
        searchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        searchQuery.addFilter(Tenantable.TENANT_ID, Operator.EQ, user.getTenantId());
        searchQuery.addFilter("active_record_id", Operator.EQ, objectId);
        searchQuery.setLimit(1000);
        searchQuery.setFindExplicitTotalNum(false);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);

        try {
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(
                    user, "ActivityUserObj", searchQuery);

            if (ObjectUtils.isNotEmpty(queryResult) && CollectionUtils.isNotEmpty(queryResult.getData())) {
                return queryResult.getData().stream()
                        .collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (v1, v2) -> v1));
            }
        } catch (Exception e) {
            log.error("queryActivityUserMap error, objectId: {}", objectId, e);
        }

        return new HashMap<>();
    }

    /**
     * 获取用户名
     *
     * @param document   交互文档
     * @param userMap    用户映射
     * @param speakLabel 发言人标签
     * @return 用户名
     */
    private String getUserName(InteractiveDocument document, Map<String, String> userMap, String speakLabel) {
        String userName = document.getUserName();
        if (userMap.containsKey(document.getActivityUserId())) {
            userName = userMap.get(document.getActivityUserId());
        }
        if (userName.contains("user_")) {
            String[] userNameArr = userName.split("_");
            if (userNameArr.length > 1) {
                userName = speakLabel + userNameArr[1];
            }
        }

        return userName;
    }

    /**
     * 格式化时间信息
     *
     * @param document 交互文档
     * @return 格式化后的时间信息
     */
    private String formatTimeInfo(InteractiveDocument document) {
        String startTime = document.getStartTime();
        String endTime = document.getEndTime();

        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            return startTime + "-" + endTime;
        } else if (StringUtils.isNotBlank(startTime)) {
            return startTime;
        } else if (StringUtils.isNotBlank(endTime)) {
            return endTime;
        }

        return "";
    }

    /**
     * 获取所有子标签
     */
    private List<ActivityTag> getAllChildTags() {
        return ActivityTagMap.TAG_MAP.values()
                .stream()
                .map(ActivityTag::getActivityTags)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    /**
     * 构建标签字符串
     */
    private String buildTagsString(List<ActivityTag> allChildTags) {
        StringBuilder tags = new StringBuilder();
        for (int i = 0; i < allChildTags.size(); i++) {
            tags.append(i + 1).append(". **").append(allChildTags.get(i).getName()).append("**\n");
        }
        return tags.toString();
    }

    /**
     * 构建标签名称到ID的映射
     */
    private Map<String, String> buildNameToIdMap(List<ActivityTag> allChildTags) {
        return allChildTags.stream().collect(Collectors.toMap(ActivityTag::getName, ActivityTag::getId));
    }

    /**
     * 单次匹配处理
     */
    private List<DirectTaggingResultModel> processWithSingleMatch(User user, String interactiveContent,
                                                                  String tagsString, List<ActivityTag> allChildTags,
                                                                  Map<String, String> nameToId, String objectId) {
        List<DirectTaggingResultModel> results = paragraphAIService.requestDirectTagging(user, interactiveContent,
                tagsString);
        log.info("大模型返回结果, objectId: {}, results: {}", objectId, JSON.toJSONString(results));
        return validateAndRetryTagging(user, interactiveContent, results, allChildTags, nameToId);
    }

    /**
     * 多次匹配处理（多个并发请求）
     */
    private List<DirectTaggingResultModel> processWithMultipleMatch(User user, String interactiveContent,
                                                                    String tagsString, List<ActivityTag> allChildTags,
                                                                    Map<String, String> nameToId, String objectId) {
        // 创建多个并发请求，使用processWithSingleMatch进行单次调用
        List<CompletableFuture<List<DirectTaggingResultModel>>> futures = Lists.newArrayList();

        for (int i = 0; i < concurrentCount; i++) {
            CompletableFuture<List<DirectTaggingResultModel>> future = CompletableFuture
                    .supplyAsync(() -> processWithSingleMatch(user, interactiveContent, tagsString, allChildTags,
                            nameToId, objectId), executorService);
            futures.add(future);
        }

        try {
            // 统一等待所有请求完成，设置60秒超时
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.get(120, TimeUnit.SECONDS);

            // 统一获取所有结果
            List<List<DirectTaggingResultModel>> allResults = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

            // 合并结果，如果有大部分结果认为某个标签匹配，则视为匹配
            return mergeMultipleResults(allResults, nameToId);
        } catch (Exception e) {
            log.error("多次匹配处理异常,转成同步处理", e);
            // 降级到单次匹配
            return processWithSingleMatch(user, interactiveContent, tagsString, allChildTags, nameToId, objectId);
        }
    }

    /**
     * 合并多次匹配的结果
     */
    private List<DirectTaggingResultModel> mergeMultipleResults(List<List<DirectTaggingResultModel>> allResults,
                                                                Map<String, String> nameToId) {

        Map<String, Integer> tagMatchCount = Maps.newHashMap();
        Map<String, String> tagSourceMap = Maps.newHashMap();

        // 统计每个标签在所有结果中的匹配次数
        for (List<DirectTaggingResultModel> results : allResults) {
            countTagMatches(results, tagMatchCount, tagSourceMap, nameToId);
        }

        // 构建最终结果，匹配次数>=大部分(超过一半)的标签视为匹配
        int majorityThreshold = Math.max(1, (allResults.size() + 1) / 2);
        List<DirectTaggingResultModel> finalResults = Lists.newArrayList();
        tagMatchCount.entrySet().stream()
                .filter(entry -> entry.getValue() >= majorityThreshold)
                .forEach(entry -> {
                    DirectTaggingResultModel result = new DirectTaggingResultModel();
                    result.setTagName(entry.getKey());
                    result.setMatch(true);
                    result.setSource(tagSourceMap.get(entry.getKey()));
                    finalResults.add(result);
                });

        log.info("多次匹配合并结果, concurrentCount: {}, majorityThreshold: {}, tagMatchCount: {}, finalResults: {}",
                concurrentCount, majorityThreshold, JSON.toJSONString(tagMatchCount), JSON.toJSONString(finalResults));

        return finalResults;
    }

    /**
     * 统计标签匹配次数
     */
    private void countTagMatches(List<DirectTaggingResultModel> results,
                                 Map<String, Integer> tagMatchCount,
                                 Map<String, String> tagSourceMap,
                                 Map<String, String> nameToId) {
        if (CollectionUtils.isEmpty(results)) {
            return;
        }

        results.stream()
                .filter(DirectTaggingResultModel::isMatch)
                .filter(result -> nameToId.containsKey(result.getTagName())) // 只统计有效的标签
                .forEach(result -> {
                    String tagName = result.getTagName();
                    tagMatchCount.put(tagName, tagMatchCount.getOrDefault(tagName, 0) + 1);
                    if (!tagSourceMap.containsKey(tagName)) {
                        tagSourceMap.put(tagName, result.getSource());
                    }
                });
    }

    /**
     * 验证标签名称并重试
     */
    private List<DirectTaggingResultModel> validateAndRetryTagging(User user, String interactiveContent,
                                                                   List<DirectTaggingResultModel> results,
                                                                   List<ActivityTag> allChildTags,
                                                                   Map<String, String> nameToId) {
        if (CollectionUtils.isEmpty(results)) {
            return results;
        }

        // 检查是否有不在入参里的tagName
        List<String> invalidTagNames = results.stream()
                .filter(DirectTaggingResultModel::isMatch)
                .map(DirectTaggingResultModel::getTagName)
                .filter(tagName -> !nameToId.containsKey(tagName))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(invalidTagNames)) {
            log.warn("发现无效的标签名称，重新调用大模型, invalidTagNames: {}", JSON.toJSONString(invalidTagNames));

            // 重新调用大模型
            String tagsString = buildTagsString(allChildTags);
            List<DirectTaggingResultModel> retryResults = paragraphAIService.requestDirectTagging(user,
                    interactiveContent, tagsString);

            // 再次验证，只保留match为true且tagName有效的结果
            if (CollectionUtils.isNotEmpty(retryResults)) {
                return retryResults.stream()
                        .filter(result -> result.isMatch() && nameToId.containsKey(result.getTagName()))
                        .collect(Collectors.toList());
            }
        }

        // 只保留match为true且tagName有效的结果
        return results.stream()
                .filter(result -> result.isMatch() && nameToId.containsKey(result.getTagName()))
                .collect(Collectors.toList());
    }

    /**
     * 处理结果并保存
     */
    private void processAndSaveResults(User user, IObjectData activeRecordData, String type, String objectId,
                                       List<DirectTaggingResultModel> results, Map<String, String> nameToId) {
        log.info("最终打标签结果, objectId: {}, results: {}", objectId, JSON.toJSONString(results));

        List<String> tagId = Lists.newArrayList();
        List<String> reason = Lists.newArrayList();

        results.stream()
                .filter(DirectTaggingResultModel::isMatch)
                .forEach(result -> {
                    tagId.add(nameToId.get(result.getTagName()));
                    reason.add(result.getSource());
                });

        if (CollectionUtils.isNotEmpty(tagId)) {
            ParagraphDocument paragraphDocument = createParagraphDocument(user, activeRecordData, type, tagId, reason);
            documentProcessService.saveParagraphDocuments(Lists.newArrayList(paragraphDocument), user);
        }
    }

    private ParagraphDocument createParagraphDocument(
            User user,
            IObjectData activeRecordData,
            String type,
            List<String> tags,
            List<String> reason) {

        ParagraphDocument paragraphDocument = new ParagraphDocument();
        // 手动生成并设置ID
        paragraphDocument.setId(new ObjectId());
        // 设置基本信息
        paragraphDocument.setTenantId(user.getTenantId());

        // 设置对象相关信息
        paragraphDocument.setObjectApiName(activeRecordData.getDescribeApiName());
        paragraphDocument.setObjectId(activeRecordData.getId());
        // 设置序号和时间信息
        paragraphDocument.setSeqNo(0);
        // 设置其他标记
        paragraphDocument.setIsDeleted(false);
        // 设置关联ID
        paragraphDocument.setAccountId(activeRecordData.get(CommonConstant.ACCOUNT_ID, String.class));
        paragraphDocument.setLeadsId(activeRecordData.get(CommonConstant.LEADS_ID, String.class));
        paragraphDocument.setNewOpportunityId(activeRecordData.get(CommonConstant.NEW_OPPORTUNITY_ID, String.class));
        paragraphDocument.setTags(tags);
        paragraphDocument.setReason(JSON.toJSONString(reason));
        // 设置时间戳
        long currentTime = System.currentTimeMillis();
        paragraphDocument.setCreateTime(currentTime);
        paragraphDocument.setLastUpdateTime(currentTime);
        paragraphDocument.setType(type);
        return paragraphDocument;
    }

    /**
     * 去重处理结果
     */
    private List<DirectTaggingResultModel> deduplicateResults(List<DirectTaggingResultModel> allResults) {
        if (CollectionUtils.isEmpty(allResults)) {
            return Lists.newArrayList();
        }

        Map<String, DirectTaggingResultModel> uniqueResults = Maps.newLinkedHashMap();
        for (DirectTaggingResultModel result : allResults) {
            if (result.isMatch()) {
                // 如果已存在相同标签名的结果，保留第一个
                uniqueResults.putIfAbsent(result.getTagName(), result);
            }
        }

        return Lists.newArrayList(uniqueResults.values());
    }
}