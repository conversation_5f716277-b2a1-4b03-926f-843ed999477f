package com.facishare.crm.task.sfa.bizfeature.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.bizfeature.service.MethodologyServiceImpl;
import com.facishare.crm.task.sfa.bizfeature.service.MethodologyNodeService;
import com.facishare.crm.task.sfa.common.constants.SystemConstants;
import com.facishare.crm.task.sfa.model.ObjectData;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MethodologyEngineListener implements ApplicationListener<ContextRefreshedEvent> {
    private AutoConfMQPushConsumer consumer;
    private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private static final String FEATURE_ENGINE_SKIP_TENANT = "feature-engine-skip-tenant";
    private static final Set<String> METHODOLOGY_API_NAMES = Sets.newHashSet("AccountObj", "LeadsObj",
            "NewOpportunityObj");
    @Autowired
    private MethodologyServiceImpl methodologyService;
    @Autowired
    private MethodologyNodeService methodologyNodeService;

    @PostConstruct
    public void init() {

        MessageListenerConcurrently messageListenerConcurrently = (messageExts, context) -> {
            List<ObjectData.ObjectChange> objectChangeList = messageExts.stream()
                    .map(this::messageParse)
                    .filter(it -> null != it
                            && "object_data".equals(it.getName())
                            && !gray.isAllow(FEATURE_ENGINE_SKIP_TENANT, it.getTenantId()))
                    .map(ObjectData::getBody)
                    .flatMap(Collection::stream)
                    .filter(it -> METHODOLOGY_API_NAMES.contains(it.getEntityId()))
                    // todo 需要根据license 过滤
                    .collect(Collectors.toList());

            for (ObjectData.ObjectChange msg : objectChangeList) {
                try {
                    if (ObjectData.ObjectChange.TRIGGER_TYPE_UPDATE.equals(msg.getTriggerType())) {
                        JSONObject jsonAfter = msg.getAfterTriggerData();
                        String lifeStatus = jsonAfter.getString(SystemConstants.Field.LifeStatus.apiName);

                        JSONObject jsonBefore = msg.getBeforeTriggerData();
                        String lifeStatusBefore = jsonBefore.getString(SystemConstants.Field.LifeStatus.apiName);
                        // 新建有审批情况
                        if (SystemConstants.LifeStatus.Normal.value.equals(lifeStatus) &&
                                (SystemConstants.LifeStatus.Ineffective.value.equals(lifeStatusBefore)
                                        || SystemConstants.LifeStatus.UnderReview.value.equals(lifeStatusBefore))) {
                            consumeResponse(msg);
                        }
                        // 编辑状态处理
                        if ((SystemConstants.LifeStatus.Normal.value.equals(lifeStatus) &&
                                SystemConstants.LifeStatus.InChange.value.equals(lifeStatusBefore)) || (StringUtils.isEmpty(lifeStatusBefore))) {
                            startNode(msg);
                        }
                    }
                } catch (Exception e) {
                    log.error("MethodologyEngineListener has error :{}", msg, e);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "crm-methodology-engine-consumer",
                messageListenerConcurrently);
    }

    private void startNode(ObjectData.ObjectChange message) {
        methodologyNodeService.startMethodologyNode(message);
    }

    private ObjectData messageParse(MessageExt messageExt) {
        try {
            return JSON.parseObject(StringHelper.toString(messageExt.getBody()), ObjectData.class);
        } catch (Exception e) {
            log.error("Methodology format failed. msgId:{}, body:{}",
                    messageExt.getMsgId(), StringHelper.toString(messageExt.getBody()));
        }
        return null;
    }

    private void consumeResponse(ObjectData.ObjectChange message) {
        methodologyService.consumer(message);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}