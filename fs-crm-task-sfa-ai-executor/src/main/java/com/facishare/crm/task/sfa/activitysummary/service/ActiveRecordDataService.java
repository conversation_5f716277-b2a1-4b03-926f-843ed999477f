package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class ActiveRecordDataService {
    @Autowired
    private ServiceFacade serviceFacade;

    /**
     * 查询活动记录数据
     *
     * @param user           用户
     * @param objectId       对象ID
     * @param queryFieldList 查询字段列表
     * @return 活动记录数据
     */
    public IObjectData queryActiveRecordDataWithFields(User user, String objectId, List<String> queryFieldList) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.ID, Operator.EQ, objectId)
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        searchTemplateQueryPlus.setLimit(1);
        searchTemplateQueryPlus.setOffset(0);
        searchTemplateQueryPlus.setSearchSource("db");

        IActionContext actionContext = ActionContextExt.of(user).getContext();
        actionContext.put(ActionContextKey.SEARCH_RICH_TEXT_EXTRA, true);
        return Optional.ofNullable(serviceFacade.findBySearchTemplateQueryWithFields(
                actionContext,
                CommonConstant.ACTIVE_RECORD_API_NAME,
                searchTemplateQueryPlus,
                queryFieldList
        ))
                .map(QueryResult::getData)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .orElse(null);
    }
} 