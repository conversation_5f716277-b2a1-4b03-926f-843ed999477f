package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.utils.LicenseCheckUtil;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.UUID;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/9 17:02
 * @description:
 */
@Slf4j
@Component
public abstract class AbstractActivityCommonListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    /**
     * MQ 配置的 consumer section
     * @return
     */
    abstract String getSection();

    abstract void consume(ActivityMessage activityMessage);

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", getSection(), (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    consumeResponse(msg);
                } catch (Exception e) {
                    log.error("AbstractActivityCommonListener :{}", msg, e);
                    throw new RuntimeException(e);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    private void consumeResponse(MessageExt message) {
        byte[] body = message.getBody();
        ActivityMessage activityMessage = JSON.parseObject(body, ActivityMessage.class);
        TraceContext.get().setTraceId(UUID.randomUUID()+"-"+activityMessage.getObjectId());
        log.info("AbstractActivityCommonListener receive activityMessage msgId:{},:{}", message.getMsgId(), activityMessage);
        if (!LicenseCheckUtil.checkAIExist(activityMessage.getTenantId())) {
            log.info("no ai license, tenantId:{}", activityMessage.getTenantId());
            return;
        }
        consume(activityMessage);
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}
