package com.facishare.crm.task.sfa.model.proxy.bi;

import lombok.Data;

import java.util.List;

public interface FilterQuery {
    @Data
    class Arg {
        private String viewId;
    }

    @Data
    class Result {
        private Integer code;
        private String message;
        private ResultData data;
    }

    @Data
    class ResultData {
        private List<FilterGroup> filterGroups;
    }

    @Data
    class FilterGroup {
        private List<Filter> filters;
    }

    @Data
    class Filter {
        private String fieldID;
        private String filterId;
    }
}