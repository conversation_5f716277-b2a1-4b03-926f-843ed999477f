package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyNodeConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyTaskConstants;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 方法论Markdown服务
 * 用于根据方法论ID生成Markdown格式的方法论、流程节点和任务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MethodologyMarkdownService {

    @Autowired
    private ServiceFacade serviceFacade;

    private static final int MAX_QUERY_LIMIT = 1000;

    /**
     * 根据方法论ID生成Markdown格式的方法论、流程节点和任务
     *
     * @param methodologyId 方法论ID
     * @param user 用户信息
     * @return Markdown格式的方法论、流程节点和任务
     */
    public String generateMethodologyMarkdown(String methodologyId, User user) {
        // 1. 查询流程类型的方法论信息
        IObjectData methodology = queryMethodologyById(methodologyId, user);
        if (methodology == null) {
            log.error("方法论不存在或不是流程类型, methodologyId: {}", methodologyId);
            return "";
        }

        // 2. 查询流程节点
        List<IObjectData> nodeList = queryWorkflowNodesByMethodologyId(methodologyId, user);
        if (CollectionUtils.isEmpty(nodeList)) {
            log.error("方法论没有流程节点, methodologyId: {}", methodologyId);
            return "";
        }

        // 3. 批量查询所有节点的任务
        Map<String, List<IObjectData>> nodeTasks = queryAllTasksByNodeIds(nodeList, user);

        // 4. 生成Markdown
        return generateMarkdownContent(methodology, nodeList, nodeTasks);
    }

    /**
     * 生成Markdown内容
     *
     * @param methodology 方法论对象
     * @param nodeList 节点列表
     * @param nodeTasks 节点任务映射
     * @return Markdown内容
     */
    private String generateMarkdownContent(IObjectData methodology, List<IObjectData> nodeList, Map<String, List<IObjectData>> nodeTasks) {
        StringBuilder markdown = new StringBuilder();

        // 方法论名称作为一级标题，带前缀序号 "1."
        String methodologyName = StringUtils.isNotEmpty(methodology.getName()) ? methodology.getName() : "未命名方法论";
        markdown.append("# 1. ").append(methodologyName).append("\n\n");

        // 直接按照nodeList的顺序生成二级标题和任务
        for (int i = 0; i < nodeList.size(); i++) {
            // 二级标题序号，如 "1.1", "1.2" 等
            int nodeIndex = i + 1;
            String nodePrefix = "1." + nodeIndex;

            generateNodeContent(nodeList.get(i), nodeTasks, markdown, nodePrefix);
        }

        return markdown.toString();
    }

    /**
     * 生成节点内容
     *
     * @param node 节点
     * @param nodeTasks 节点任务映射
     * @param markdown Markdown构建器
     * @param nodePrefix 节点标题前缀序号
     */
    private void generateNodeContent(IObjectData node, Map<String, List<IObjectData>> nodeTasks, StringBuilder markdown, String nodePrefix) {
        String nodeId = node.getId();

        // 生成节点标题
        generateNodeTitle(node, markdown, nodePrefix);

        // 生成节点的任务
        generateNodeTasks(nodeId, nodeTasks, markdown, nodePrefix);
    }

    /**
     * 生成节点标题
     *
     * @param node 节点
     * @param markdown Markdown构建器
     * @param nodePrefix 节点标题前缀序号
     */
    private void generateNodeTitle(IObjectData node, StringBuilder markdown, String nodePrefix) {
        // 获取节点显示名称
        String displayName = getNodeDisplayName(node);

        // 添加二级标题，带前缀序号
        markdown.append("## ").append(nodePrefix).append(" ").append(displayName).append("\n\n");
    }

    /**
     * 获取节点显示名称
     *
     * @param node 节点
     * @return 节点显示名称
     */
    private String getNodeDisplayName(IObjectData node) {
        // 获取节点名称
        String nodeName = node.getName();

        // 如果名称为空，则使用简称
        if (StringUtils.isEmpty(nodeName)) {
            String shortName = node.get(MethodologyNodeConstants.SHORT_NAME, String.class);
            return StringUtils.isNotEmpty(shortName) ? shortName : "";
        }

        return nodeName;
    }

    /**
     * 生成节点的任务
     *
     * @param nodeId 节点ID
     * @param nodeTasks 节点任务映射
     * @param markdown Markdown构建器
     * @param nodePrefix 节点标题前缀序号
     */
    private void generateNodeTasks(String nodeId, Map<String, List<IObjectData>> nodeTasks, StringBuilder markdown, String nodePrefix) {
        List<IObjectData> tasks = nodeTasks.get(nodeId);
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }

        for (int i = 0; i < tasks.size(); i++) {
            // 三级标题序号，如 "1.1.1", "1.1.2" 等
            int taskIndex = i + 1;
            String taskPrefix = nodePrefix + "." + taskIndex;

            generateTaskTitle(tasks.get(i), markdown, taskPrefix);
        }
    }

    /**
     * 生成任务标题
     *
     * @param task 任务
     * @param markdown Markdown构建器
     * @param taskPrefix 任务标题前缀序号
     */
    private void generateTaskTitle(IObjectData task, StringBuilder markdown, String taskPrefix) {
        // 获取任务显示名称
        String taskDisplayName = getTaskDisplayName(task);

        // 添加三级标题，带前缀序号
        markdown.append("### ").append(taskPrefix).append(" ").append(taskDisplayName).append("\n\n");
    }

    /**
     * 获取任务显示名称
     *
     * @param task 任务
     * @return 任务显示名称
     */
    private String getTaskDisplayName(IObjectData task) {
        // 获取任务名称
        String taskName = task.getName();

        // 如果名称为空，则使用简称
        if (StringUtils.isEmpty(taskName)) {
            String taskShortName = task.get(MethodologyTaskConstants.SHORT_NAME, String.class);
            return StringUtils.isNotEmpty(taskShortName) ? taskShortName : "";
        }

        return taskName;
    }

    /**
     * 获取节点任务映射
     *
     * @param nodeList 节点列表
     * @param user 用户信息
     * @return 节点任务映射
     */
    private Map<String, List<IObjectData>> queryAllTasksByNodeIds(List<IObjectData> nodeList, User user) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return Collections.emptyMap();
        }

        // 提取所有节点ID
        List<String> nodeIds = extractNodeIds(nodeList);

        // 批量查询所有任务（已在查询时按节点ID和任务顺序排序）
        List<IObjectData> allTasks = queryWorkflowTasksByNodeIds(nodeIds, user);

        // 按节点ID分组
        return groupTasksByNodeId(allTasks);
    }

    /**
     * 提取节点ID列表
     *
     * @param nodeList 节点列表
     * @return 节点ID列表
     */
    private List<String> extractNodeIds(List<IObjectData> nodeList) {
        return nodeList.stream()
                .map(IObjectData::getId)
                .collect(Collectors.toList());
    }

    /**
     * 按节点ID分组任务
     *
     * @param tasks 任务列表
     * @return 节点任务映射
     */
    private Map<String, List<IObjectData>> groupTasksByNodeId(List<IObjectData> tasks) {
        Map<String, List<IObjectData>> nodeTasks = new HashMap<>();

        for (IObjectData task : tasks) {
            String nodeId = task.get(MethodologyTaskConstants.NODE_ID, String.class);
            if (StringUtils.isNotEmpty(nodeId)) {
                List<IObjectData> nodeTaskList = nodeTasks.computeIfAbsent(nodeId, k -> new ArrayList<>());
                nodeTaskList.add(task);
            }
        }

        return nodeTasks;
    }

    /**
     * 批量查询节点对应的任务
     *
     * @param nodeIds 节点ID列表
     * @param user 用户信息
     * @return 任务列表
     */
    private List<IObjectData> queryWorkflowTasksByNodeIds(List<String> nodeIds, User user) {
        if (CollectionUtils.isEmpty(nodeIds)) {
            return Collections.emptyList();
        }

        // 使用SearchTemplateQueryPlus来添加过滤条件
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(MethodologyTaskConstants.NODE_ID, Operator.IN, nodeIds);

        // 只查询必要的字段
        List<String> queryFields = Lists.newArrayList(
                DBRecord.ID,
                IObjectData.NAME,
                MethodologyTaskConstants.NODE_ID,
                MethodologyTaskConstants.TASK_ORDER,
                MethodologyTaskConstants.SHORT_NAME
        );

        // 添加排序条件，按节点ID和任务顺序排序
        searchTemplateQueryPlus.setOrders(Lists.newArrayList(
                new OrderBy(MethodologyTaskConstants.TASK_ORDER, true)
        ));

        searchTemplateQueryPlus.setLimit(MAX_QUERY_LIMIT);
        searchTemplateQueryPlus.setOffset(0);

        return Optional.ofNullable(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                user,
                MethodologyTaskConstants.API_NAME,
                searchTemplateQueryPlus,
                queryFields))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    /**
     * 根据方法论ID查询流程类型的方法论
     *
     * @param methodologyId 方法论ID
     * @param user 用户信息
     * @return 流程类型的方法论对象，如果不存在或不是流程类型则返回null
     */
    private IObjectData queryMethodologyById(String methodologyId, User user) {
        // 使用SearchTemplateQueryPlus来添加过滤条件
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(DBRecord.ID, Operator.EQ, methodologyId)
                .addFilter(MethodologyConstants.TYPE, Operator.EQ, MethodologyConstants.Type.FLOW.getType());

        // 只查询必要的字段
        List<String> queryFields = Lists.newArrayList(
                DBRecord.ID,
                IObjectData.NAME
        );

        List<IObjectData> methodologyList = Optional.ofNullable(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                user,
                MethodologyConstants.API_NAME,
                searchTemplateQueryPlus,
                queryFields))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());

        if (CollectionUtils.isEmpty(methodologyList)) {
            return null;
        }

        return methodologyList.get(0);
    }

    /**
     * 查询方法论的流程节点
     *
     * @param methodologyId 方法论ID
     * @param user 用户信息
     * @return 流程节点列表
     */
    private List<IObjectData> queryWorkflowNodesByMethodologyId(String methodologyId, User user) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(MethodologyNodeConstants.METHODOLOGY_ID, Operator.EQ, methodologyId)
                .addFilter(MethodologyNodeConstants.STATUS, Operator.EQ, MethodologyNodeConstants.StatusType.ENABLE.getStatusType());

        // 只查询必要的字段
        List<String> queryFields = Lists.newArrayList(
                DBRecord.ID,
                IObjectData.NAME,
                MethodologyNodeConstants.PARENT_ID,
                MethodologyNodeConstants.NODE_ORDER,
                MethodologyNodeConstants.LEVEL,
                MethodologyNodeConstants.SHORT_NAME
        );

        searchTemplateQueryPlus.setLimit(MAX_QUERY_LIMIT);
        searchTemplateQueryPlus.setOffset(0);
        searchTemplateQueryPlus.setOrders(Lists.newArrayList(
                new OrderBy(MethodologyNodeConstants.LEVEL, true),
                new OrderBy(MethodologyNodeConstants.NODE_ORDER, true)
        ));

        return Optional.ofNullable(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                user,
                MethodologyNodeConstants.API_NAME,
                searchTemplateQueryPlus,
                queryFields))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    /**
     * 更新方法论的process_overview字段
     *
     * @param methodologyId 方法论ID
     * @param markdown Markdown内容
     * @param user 用户信息
     * @return 是否更新成功
     */
    public boolean updateMethodologyProcessOverview(String methodologyId, String markdown, User user) {
        try {
            // 查询方法论，不限制类型
            SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                    .addFilter(DBRecord.IS_DELETED, Operator.EQ, "0")
                    .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                    .addFilter(DBRecord.ID, Operator.EQ, methodologyId);

            // 只查询必要的字段
            List<String> queryFields = Lists.newArrayList(
                    DBRecord.ID,
                    IObjectData.NAME
            );

            List<IObjectData> methodologyList = Optional.ofNullable(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                    user,
                    MethodologyConstants.API_NAME,
                    searchTemplateQueryPlus,
                    queryFields))
                    .map(QueryResult::getData)
                    .orElse(Lists.newArrayList());

            if (CollectionUtils.isEmpty(methodologyList)) {
                log.error("方法论不存在, methodologyId: {}", methodologyId);
                return false;
            }

            IObjectData methodology = methodologyList.get(0);
            methodology.set(MethodologyConstants.PROCESS_OVERVIEW, markdown);
            serviceFacade.updateObjectData(user, methodology);
            return true;
        } catch (Exception e) {
            log.error("更新方法论process_overview失败, methodologyId: {}", methodologyId, e);
            return false;
        }
    }
}
