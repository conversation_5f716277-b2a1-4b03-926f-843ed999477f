package com.facishare.crm.task.sfa.rest.dto;

import com.facishare.crm.task.sfa.model.PAASContext;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface KnowledgeModel {
    @Data
    @Builder
    class AddKnowledgeCategoryArg {
        private String name;
        private String record_type;
        private int code;
        private String pid;
    }

    @Data
    class AddKnowledgeCategoryResult {
        int errCode;
        String errMessage;
        Map<String, Map<String, Object>> result;
    }

    @Data
    @Builder
    class CreateKnowledgeSpaceArg {
        private CreateKnowledgeSpaceArgModel arg1;
    }

    @Data
    class CreateKnowledgeSpaceResult {
        String id;
        String name;
        String recordType;
    }
    @Data
    @Builder
    class CreateKnowledgeSpaceArgModel {
        String fsEa;
        long fsUserId;
        String id;
        String name;
        String description;
        String recordType;
        List<String> categoryCodeList;
    }
    @Data
    @Builder
    class ListKnowledgeCategoryArg {
        private String record_type;
        private String sourceApiName;
        private List<String> scenaryList;
    }

    @Data
    class ListKnowledgeCategoryResult {
        List<Map<String, Object>> result;
    }
}
