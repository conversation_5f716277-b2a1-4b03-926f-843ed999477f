package com.facishare.crm.task.sfa.model.proxy.bi;

import lombok.Data;

import java.time.format.DateTimeFormatter;
import java.util.List;

public interface BiCrmRestQuery {
    @Data
    class Page {
        /**
         * 当前页，默认1， 统计图不支持
         */
        private Integer pageNumber;
        /**
         * 条数，默认20，统计图不支持
         */
        private Integer pageSize;
        /**
         * 覆盖已存在的条件
         */
        private List<Filter> filterList;
    }

    @Data
    class Filter {
        public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        private String filterId;
        private String value1;
        private String value2;
        private Integer dateRangeId;
        private Integer operator;
    }

    @Data
    class DetailResultData {
        private List<String> ids;
    }
}
