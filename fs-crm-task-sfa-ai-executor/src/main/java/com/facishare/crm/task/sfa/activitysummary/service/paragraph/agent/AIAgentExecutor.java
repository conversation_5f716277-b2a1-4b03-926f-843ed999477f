package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.facishare.crm.task.sfa.activitysummary.model.*;
import com.facishare.crm.task.sfa.activitysummary.service.ChatBot;
import com.facishare.crm.task.sfa.activitysummary.service.FixJSONFormatService;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * AI Agent执行器
 * 负责执行各个阶段的AI任务（Planner、Executor、Evaluator）
 */
@Component
@Slf4j
public class AIAgentExecutor {

    @Autowired
    private ChatBot chatBot;

    @Autowired
    private FixJSONFormatService fixJSONFormatService;

    /**
     * 执行Planner阶段
     */
    public PlanModel executePlanner(User user, String meetContent, String previousSummary, 
                                   ChatHistory plannerHistory) {
        try {
            String plannerPrompt = getPlannerPrompt();
            String plannerUserInput = buildPlannerUserInput(meetContent, previousSummary);

            String plannerResponse = chatBot.executeRound(user.getTenantId(), plannerHistory, plannerPrompt, plannerUserInput);

            PlanResponse planResponse = parsePlannerResponse(user, plannerResponse);
            if (planResponse == null || planResponse.getPlan() == null) {
                return null;
            }

            PlanModel plan = planResponse.getPlan();
            log.info("多轮对话Planner执行成功，分段策略: {}, 预期段落数: {}", plan.getStrategy(), plan.getExpectedSegments());
            return plan;
        } catch (Exception e) {
            log.error("执行Planner阶段失败", e);
            return null;
        }
    }

    /**
     * 执行Executor阶段
     */
    public List<SegmentModel> executeExecutor(User user, String meetContent, String previousSummary, 
                                             PlanModel plan, ChatHistory executorHistory, 
                                             String plannerResponse) {
        try {
            String executorPrompt = getExecutorPrompt();
            String executorUserInput = buildExecutorUserInput(meetContent, previousSummary, plannerResponse);

            String executorResponse = chatBot.executeRound(user.getTenantId(), executorHistory, executorPrompt, executorUserInput);

            List<SegmentModel> segments = parseExecutorResponse(user, executorResponse);
            if (!CollectionUtils.isEmpty(segments)) {
                log.info("多轮对话Executor执行成功，生成段落数: {}", segments.size());
            }
            return segments;
        } catch (Exception e) {
            log.error("执行Executor阶段失败", e);
            return null;
        }
    }

    /**
     * 执行Evaluator阶段
     */
    public EvaluationModel executeEvaluator(User user, String meetContent, String previousSummary, 
                                           List<SegmentModel> segments, Map<String, String> idToContentMap,
                                           ChatHistory evaluatorHistory, String executorResponse) {
        try {
            List<SegmentModel> segmentsWithContent = replaceIdsWithOriginalContent(segments, idToContentMap);
            log.info("已将分段ID替换为原文，用于评估");

            String evaluatorPrompt = getEvaluatorPrompt(segmentsWithContent);
            String evaluatorUserInput = buildEvaluatorUserInput(meetContent, previousSummary, executorResponse);

            String evaluatorResponseStr = chatBot.executeRound(user.getTenantId(), evaluatorHistory, evaluatorPrompt, evaluatorUserInput);

            return parseEvaluatorResponse(user, evaluatorResponseStr);
        } catch (Exception e) {
            log.error("执行Evaluator阶段失败", e);
            return null;
        }
    }

    /**
     * 重新执行Executor阶段
     */
    public List<SegmentModel> reExecuteExecutor(User user, String meetContent, String previousSummary, 
                                               ChatHistory executorHistory, String adjusterResponse) {
        try {
            String newExecutorUserInput = buildReExecutorUserInput(meetContent, previousSummary, adjusterResponse);
            String newExecutorResponse = chatBot.continueChat(user.getTenantId(), executorHistory, newExecutorUserInput);

            List<SegmentModel> newSegments = parseExecutorResponse(user, newExecutorResponse);
            if (!CollectionUtils.isEmpty(newSegments)) {
                log.info("重新执行Executor成功，生成新的分段结果");
            }
            return newSegments;
        } catch (Exception e) {
            log.error("重新执行Executor阶段失败", e);
            return null;
        }
    }

    /**
     * 重新执行Evaluator阶段
     */
    public EvaluationModel reExecuteEvaluator(User user, String meetContent, String previousSummary, 
                                             List<SegmentModel> segments, Map<String, String> idToContentMap,
                                             ChatHistory evaluatorHistory, String executorResponse) {
        try {
            String newEvaluatorUserInput = buildReEvaluatorUserInput(meetContent, previousSummary, executorResponse);
            String newEvaluatorResponse = chatBot.continueChat(user.getTenantId(), evaluatorHistory, newEvaluatorUserInput);

            return parseEvaluatorResponse(user, newEvaluatorResponse);
        } catch (Exception e) {
            log.error("重新执行Evaluator阶段失败", e);
            return null;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 构建Planner用户输入
     */
    private String buildPlannerUserInput(String meetContent, String previousSummary) {
        return "会议内容:\n" + meetContent + "\n\n" +
                "上次会议摘要:\n" + (StringUtils.isBlank(previousSummary) ? "无" : previousSummary);
    }

    /**
     * 构建Executor用户输入
     */
    private String buildExecutorUserInput(String meetContent, String previousSummary, String plannerResponse) {
        StringBuilder input = new StringBuilder();
        input.append("会议内容:\n").append(meetContent).append("\n\n");
        if (StringUtils.isNotBlank(previousSummary)) {
            input.append("上次会议摘要:\n").append(previousSummary).append("\n\n");
        }
        input.append("分段策略:\n").append(plannerResponse);
        return input.toString();
    }

    /**
     * 构建Evaluator用户输入
     */
    private String buildEvaluatorUserInput(String meetContent, String previousSummary, String executorResponse) {
        StringBuilder input = new StringBuilder();
        input.append("会议内容:\n").append(meetContent).append("\n\n");
        if (StringUtils.isNotBlank(previousSummary)) {
            input.append("上次会议摘要:\n").append(previousSummary).append("\n\n");
        }
        input.append("分段结果:\n").append(executorResponse);
        return input.toString();
    }

    /**
     * 构建重新执行Executor的用户输入
     */
    private String buildReExecutorUserInput(String meetContent, String previousSummary, String adjusterResponse) {
        StringBuilder input = new StringBuilder();
        input.append("会议内容:\n").append(meetContent).append("\n\n");
        if (StringUtils.isNotBlank(previousSummary)) {
            input.append("上次会议摘要:\n").append(previousSummary).append("\n\n");
        }
        input.append("调整后的策略:\n").append(adjusterResponse).append("\n\n请根据新策略重新分段。");
        return input.toString();
    }

    /**
     * 构建重新执行Evaluator的用户输入
     */
    private String buildReEvaluatorUserInput(String meetContent, String previousSummary, String executorResponse) {
        StringBuilder input = new StringBuilder();
        input.append("会议内容:\n").append(meetContent).append("\n\n");
        if (StringUtils.isNotBlank(previousSummary)) {
            input.append("上次会议摘要:\n").append(previousSummary).append("\n\n");
        }
        input.append("新的分段结果:\n").append(executorResponse).append("\n\n请评估新的分段质量。");
        return input.toString();
    }

    /**
     * 将分段中的ID替换为原始文本内容
     */
    private List<SegmentModel> replaceIdsWithOriginalContent(List<SegmentModel> segments, Map<String, String> idToContentMap) {
        if (CollectionUtils.isEmpty(segments)) {
            return segments;
        }

        try {
            if (idToContentMap.isEmpty()) {
                log.warn("无法从会议内容中提取ID到原文的映射");
                return segments;
            }

            // 创建新的分段列表，替换ID为原文
            List<SegmentModel> newSegments = new ArrayList<>();
            for (SegmentModel segment : segments) {
                SegmentModel newSegment = new SegmentModel();
                newSegment.setSegmentId(segment.getSegmentId());
                newSegment.setSummary(segment.getSummary());
                newSegment.setConfidence(segment.getConfidence());

                // 替换内容ID为原文
                List<String> originalContents = new ArrayList<>();
                for (String contentId : segment.getContentIds()) {
                    String originalContent = idToContentMap.getOrDefault(contentId, contentId);
                    originalContents.add(originalContent);
                }
                newSegment.setContentIds(originalContents);

                newSegments.add(newSegment);
            }

            return newSegments;
        } catch (Exception e) {
            log.error("替换ID为原文时发生错误", e);
            return segments; // 出错时返回原始分段
        }
    }

    // ==================== 提示词方法 ====================

    private String getPlannerPrompt() {
        return "你是一个专业的文本分析和分段策略专家，负责分析会议内容并制定分段策略。\n\n" +
                "你的主要职责:\n" +
                "1. 分析会议内容，制定初始分段策略\n" +
                "2. 根据评估结果，调整和优化分段策略\n\n" +
                "请分析用户提供的会议内容，并制定一个合理的分段策略。\n\n" +
                "请考虑以下因素:\n" +
                "1. 主题变化和连贯性\n" +
                "2. 关键词密度\n" +
                "3. 语义相关性\n" +
                "4. 段落长度平衡\n\n" +
                "输出格式要求:\n" +
                "{\n" +
                "\"plan\": {\n" +
                "\"strategy\": \"分段策略名称\",\n" +
                "\"expected_segments\": 预期段落数量,\n" +
                "\"focus_points\": [\"关注点1\", \"关注点2\", ...],\n" +
                "\"special_handling\": [\"特殊处理1\", \"特殊处理2\", ...]\n" +
                "}\n" +
                "}";
    }

    private String getExecutorPrompt() {
        return "你是一个专业的文本分段专家，负责根据策略将会议内容分成多个有意义的段落。\n\n" +
                "用户将提供分段计划，请根据该计划将会议内容分成多个有意义的段落，每个段落包含相关的内容ID和摘要。\n\n" +
                "输出格式要求:\n" +
                "{\n" +
                "\"segments\": [\n" +
                "{\n" +
                "\"segment_id\": \"1\",\n" +
                "\"content_ids\": [\"id1\", \"id2\", ...],\n" +
                "\"summary\": \"段落摘要\",\n" +
                "\"confidence\": 置信度(0-1)\n" +
                "},\n" +
                "...\n" +
                "]\n" +
                "}";
    }

    private String getEvaluatorPrompt(List<SegmentModel> segments) {
        return "你是一个专业的文本分段评估专家，负责评估分段结果的质量并提供详细的改进建议。你的评估必须保持高度一致性和客观性。\n\n" +
                "# 任务背景\n" +
                "你正在评估一个会议内容的分段结果。这些分段将用于生成会议摘要，因此分段质量直接影响最终摘要的质量。\n\n" +
                "# 评估流程\n" +
                "请严格按照以下步骤进行评估:\n" +
                "1. 仔细阅读用户提供的分段结果\n" +
                "2. 分别评估四个维度的得分\n" +
                "3. 计算总体评分\n" +
                "4. 确定是否需要修正\n" +
                "5. 提供具体的调整建议\n\n" +
                "# 输出格式要求\n" +
                "你必须严格按照以下JSON格式输出评估结果:\n" +
                "{\n" +
                "\"evaluation\": {\n" +
                "  \"overall_score\": 总体评分(0-1，由四个维度平均得出),\n" +
                "  \"dimension_scores\": {\n" +
                "    \"coherence\": 连贯性得分(0-1),\n" +
                "    \"summary_quality\": 摘要质量得分(0-1),\n" +
                "    \"segmentation_count\": 分段数量合理性得分(0-1),\n" +
                "    \"distinction\": 段落区分度得分(0-1)\n" +
                "  },\n" +
                "  \"issues\": [\"问题1\", \"问题2\", ...],\n" +
                "  \"suggestions\": [\"建议1\", \"建议2\", ...],\n" +
                "  \"needs_revision\": 是否需要修正(true/false),\n" +
                "  \"adjustment_plan\": {\n" +
                "    \"merge_segments\": [需要合并的段落ID数组],\n" +
                "    \"split_segments\": [需要拆分的段落ID数组],\n" +
                "    \"ideal_segment_count\": 理想段落数量,\n" +
                "    \"focus_areas\": [\"需要重点关注的区域1\", \"需要重点关注的区域2\", ...]\n" +
                "  }\n" +
                "}\n" +
                "}";
    }

    // ==================== 解析方法 ====================

    private PlanResponse parsePlannerResponse(User user, String response) {
        String jsonFormat = "{\n" +
                "\"plan\": {\n" +
                "\"strategy\": \"分段策略名称\",\n" +
                "\"expected_segments\": 预期段落数量,\n" +
                "\"focus_points\": [\"关注点1\", \"关注点2\", ...],\n" +
                "\"special_handling\": [\"特殊处理1\", \"特殊处理2\", ...]\n" +
                "}\n" +
                "}";
        try {
            return fixJSONFormatService.getDataFixedInvalidJSON(user, jsonFormat, response, PlanResponse.class);
        } catch (Exception e) {
            log.error("解析Planner响应失败", e);
            return null;
        }
    }

    private List<SegmentModel> parseExecutorResponse(User user, String response) {
        String jsonFormat = "{\n" +
                "\"segments\": [\n" +
                "{\n" +
                "\"segment_id\": \"1\",\n" +
                "\"content_ids\": [\"id1\", \"id2\", ...],\n" +
                "\"summary\": \"段落摘要\",\n" +
                "\"confidence\": 置信度(0-1)\n" +
                "},\n" +
                "...\n" +
                "]\n" +
                "}";
        try {
            SegmentsResponse executorResponse = fixJSONFormatService.getDataFixedInvalidJSON(user, jsonFormat, response, SegmentsResponse.class);
            return executorResponse != null ? executorResponse.getSegments() : null;
        } catch (Exception e) {
            log.error("解析Executor响应失败", e);
            return null;
        }
    }

    private EvaluationModel parseEvaluatorResponse(User user, String response) {
        String jsonFormat = "{\n" +
                "\"evaluation\": {\n" +
                "  \"overall_score\": 总体评分(0-1，由四个维度平均得出),\n" +
                "  \"dimension_scores\": {\n" +
                "    \"coherence\": 连贯性得分(0-1),\n" +
                "    \"summary_quality\": 摘要质量得分(0-1),\n" +
                "    \"segmentation_count\": 分段数量合理性得分(0-1),\n" +
                "    \"distinction\": 段落区分度得分(0-1)\n" +
                "  },\n" +
                "  \"issues\": [\"问题1\", \"问题2\", ...],\n" +
                "  \"suggestions\": [\"建议1\", \"建议2\", ...],\n" +
                "  \"needs_revision\": 是否需要修正(true/false),\n" +
                "  \"adjustment_plan\": {\n" +
                "    \"merge_segments\": [需要合并的段落ID数组],\n" +
                "    \"split_segments\": [需要拆分的段落ID数组],\n" +
                "    \"ideal_segment_count\": 理想段落数量,\n" +
                "    \"focus_areas\": [\"需要重点关注的区域1\", \"需要重点关注的区域2\", ...]\n" +
                "  }\n" +
                "}\n" +
                "}";
        try {
            EvaluationResponse evaluationResponse = fixJSONFormatService.getDataFixedInvalidJSON(user, jsonFormat, response, EvaluationResponse.class);
            return evaluationResponse != null ? evaluationResponse.getEvaluation() : null;
        } catch (Exception e) {
            log.error("解析Evaluator响应失败", e);
            return null;
        }
    }
} 