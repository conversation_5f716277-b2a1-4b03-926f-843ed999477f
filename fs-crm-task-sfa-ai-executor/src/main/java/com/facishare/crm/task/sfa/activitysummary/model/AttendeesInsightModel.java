package com.facishare.crm.task.sfa.activitysummary.model;

import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.*;

import java.util.List;
import java.util.Map;

public interface AttendeesInsightModel {


    List<String> FEATURE_INSIGHT_TYPE_LIST = Lists.newArrayList("trust_level","skill_score","question_score","answer_score");


    @Data
    class AttendeesInsightMessage {
        private String tenantId;
        private String activeRecordId;
        private String activityUserId;

        private List<String> insightTypeList;

        private AttendeesInsightExtendData extendData;

    }

    @Data
    class AttendeesInsightExtendData {
        private IObjectData activeRecord;
        private List<InteractiveDocument> documents;
        private String corpusOriginText;
        private List<IObjectData> activityUserList;
        private List<IObjectData> questionList;
        private Map<String, String> userIdNameMap;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class AttitudeInsightResult extends InsightResult {
        private String attitude;
    }

    @Data
    class InsightResult {
        private String userName;
        private List<Insight> insightList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Insight {
        private String insightText;
        private List<String> quotaSeqList;
        private Integer questionSeq;
        private String insightType;
    }

    @Data
    class InsightSOPCoverageResult  {
        private String userName;
        private String confirmedQuestions;
        private String unconfirmedQuestions;
        private String confirmedCount;
    }

    @Data
    class FeatureInsightResult extends InsightResult  {
        private String userName;
        private String insightText;
    }


}
