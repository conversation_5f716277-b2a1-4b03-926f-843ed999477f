package com.facishare.crm.task.sfa.activitysummary.service.tag;

import com.facishare.crm.sfa.lto.activity.mongo.ParagraphDocument;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTagMessage;
import com.facishare.crm.task.sfa.activitysummary.model.TagProcessingResult;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.ParagraphEndService;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureValueProducer;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 活动标签服务入口类
 * 负责接收消息并分发处理
 */
@Service
@Slf4j
public class ActivityTagService {


    @Autowired
    private ParagraphContentService paragraphContentService;

    @Autowired
    private TagProcessingService tagProcessingService;

    @Autowired
    private ParagraphTagDataService paragraphTagDataService;

    @Autowired
    private ParagraphEndService paragraphEndService;

    @Autowired
    private FeatureValueProducer featureValueProducer;


    /**
     * 处理活动标签消息
     *
     * @param activityTagMessage 活动标签消息
     */
    public void consumer(ActivityTagMessage activityTagMessage) {
        if (activityTagMessage == null || CollectionUtils.isEmpty(activityTagMessage.getParagraphIds())
                || StringUtils.isBlank(activityTagMessage.getTenantId())) {
            log.warn("ActivityTagMessage is null or paragraphIds is empty");
            return;
        }

        try {
            doConsumer(activityTagMessage);
        } catch (Exception e) {
            log.error("Error processing activity tag message", e);
        } finally {
            log.info("processWithLuaScript tenantId: {}, objectId: {}", activityTagMessage.getTenantId(),
                    activityTagMessage.getObjectId());
            if (ParagraphEndService.FINISH == (paragraphEndService.processWithLuaScript(activityTagMessage.getTenantId(), activityTagMessage.getObjectId()))) {
                // 将ActivityTagMessage转换为FeatureMqModel.Message
                FeatureMqModel.Message featureMessage = FeatureMqModel.Message.builder()
                        .paragraphIds(activityTagMessage.getParagraphIds())
                        .tenantId(activityTagMessage.getTenantId())
                        .userId(activityTagMessage.getUserId())
                        .objectId(activityTagMessage.getObjectId())
                        .objectApiName(activityTagMessage.getObjectApiName())
                        .build();
                // 发送特征计算消息
                featureValueProducer.sendTagMessage(featureMessage);
            }
            log.info("processWithLuaScript end tenantId: {}, objectId: {}", activityTagMessage.getTenantId(),
                    activityTagMessage.getObjectId());
        }
    }

    private void doConsumer(ActivityTagMessage activityTagMessage) {
         String tenantId = activityTagMessage.getTenantId();
        String userId = activityTagMessage.getUserId();
        User user = StringUtils.isBlank(userId) ? User.systemUser(tenantId) : new User(tenantId, userId);
        // 1. 根据段落ID查询段落文档
        List<ParagraphDocument> paragraphDocuments = paragraphContentService.queryParagraphDocuments(
                tenantId, activityTagMessage.getParagraphIds());
        if (CollectionUtils.isEmpty(paragraphDocuments)) {
            log.warn("No paragraph documents found for paragraphIds: {}", activityTagMessage.getParagraphIds());
        }

        // 2. 处理每个段落文档的内容
        Map<String, String> paragraphContentsMap = paragraphContentService.getParagraphContents(
                user, paragraphDocuments, activityTagMessage.getType());
        TagProcessingResult processingResult = TagProcessingResult.builder()
                .contentChunks(paragraphContentsMap)
                .tagResults(Maps.newHashMap())
                .build();

        //处理打标签逻辑
        tagProcessingService.processTags(user, processingResult);

        // 6. 更新段落标签
        paragraphTagDataService.updateParagraphTags(tenantId, paragraphDocuments, processingResult);

        log.info("Successfully processed activity tag message for tenant: {}, paragraphs count: {}",
                tenantId, activityTagMessage.getParagraphIds().size());
    }
}
