package com.facishare.crm.task.sfa.util;

import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;

public class ActionContextUtil {

    /**
     * 获取跳过检查的ActionContext
     * @param user
     * @return
     */
    public static IActionContext getSkipActionContext(User user) {
            return  ActionContextExt.of(user, RequestContextManager.getContext())
                    .setNotValidate(true).set(ActionContextKey.SKIP_OBJECT_REFERENCE_EXIST_VALID, true).setSkipRequiredValidate(true)
                    .getContext();
    }
}
