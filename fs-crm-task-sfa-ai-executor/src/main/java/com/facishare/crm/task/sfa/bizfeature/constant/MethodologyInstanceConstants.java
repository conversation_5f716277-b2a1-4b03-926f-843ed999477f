package com.facishare.crm.task.sfa.bizfeature.constant;

import com.google.common.collect.ImmutableMap;

import java.util.Map;

/**
 * 方法论实例常量
 *
 * <AUTHOR>
 */
public interface MethodologyInstanceConstants {
    String API_NAME = "MethodologyInstanceObj";
    /**
     * 方法论
     */
    String METHODOLOGY_ID = "methodology_id";
    /**
     * 线索
     */
    String LEAD_ID = "lead_id";
    /**
     * 客户
     */
    String ACCOUNT_ID = "account_id";
    /**
     * 商机
     */
    String OPPORTUNITY_ID = "opportunity_id";
    /**
     * 联系人
     */
    String CONTACT_ID = "contact_id";
    /**
     * 创建时间
     */
    String CREATE_TIME = "create_time";
    /**
     * 状态
     */
    String STATUS = "status";
    /**
     * 流程阶段层级
     */
    String STAGE_LEVEL = "stage_level";
    /**
     * 类型
     */
    String TYPE = "type";
    /**
     * 关联方法论
     */
    String METHODOLOGY_IDS = "methodology_ids";

    /**
     * 状态类型
     */
    enum StatusType {
        ENABLE("1"),
        DISABLE("0");

        private final String statusType;

        StatusType(String statusType) {
            this.statusType = statusType;
        }

        public String getStatusType() {
            return statusType;
        }
    }

    /**
     * 状态类型
     */
    enum Type {
        FLOW("flow"),
        PROFILE("profile");

        private final String type;

        Type(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }

    Map<String, String> OBJECT_FIELD_MAP = ImmutableMap.<String, String>builder()
            .put("LeadsObj", "lead_id")
            .put("AccountObj", "account_id")
            .put("NewOpportunityObj", "opportunity_id")
            .build();

    Map<String, String> OBJECT_FIELD_MAP_LIST = ImmutableMap.<String, String>builder()
            .build();
}