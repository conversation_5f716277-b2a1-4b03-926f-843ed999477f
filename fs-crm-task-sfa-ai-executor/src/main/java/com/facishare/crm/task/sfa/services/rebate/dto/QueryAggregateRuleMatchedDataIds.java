package com.facishare.crm.task.sfa.services.rebate.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface QueryAggregateRuleMatchedDataIds {

    @Data
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends BaseEngine.Arg {
        private Set<String> aggregateRuleIds;
        private List<FindRuleCodesMatchedDataIds.DataDetail> dataList;
    }

    @Data
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Result extends BaseEngine.Result<Map<String, Set<String>>> {
    }
}
