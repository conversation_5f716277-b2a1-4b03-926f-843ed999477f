package com.facishare.crm.task.sfa.activitysummary.constant;

/**
 * 问题常量
 *
 * <AUTHOR>
 */
public interface ActivityQuestionConstants {
    String ACTIVITY_QUESTION_ID = "activity_question_id";
	String CORPORA_FROM_MONGGO = "corporaFromMonggo";
	String API_NAME = "ActivityQuestionObj";
	/**
     * 销售记录
     */
	String ACTIVE_RECORD_ID = "active_record_id";
	/**
     * 客户名称
     */
	String ACCOUNT_ID = "account_id";
	/**
     * 商机名称
     */
	String NEW_OPPORTUNITY_ID = "new_opportunity_id";
	/**
     * 销售话题
     */
	String LIBRARY_ID = "library_id";
	/**
     * 互动策略
     */
	String STRATEGY_ID = "strategy_id";
	/**
     * 排序
     */
	String ORDER_FIELD = "order_field";
	/**
     * 问题内容
     */
	String QUESTION_CONTENT = "question_content";
	/**
	 * 临时的问题内容
	 */
	String LIBRARY_CONTENT = "library_content";
	/**
     * 问题提出人
     */
	String QUESTION_PROPOSER = "question_proposer";

	String HISTORY_FLAG = "history_flag";

	String HISTORY_FLAG_VALUE = "history";
	/**
     * 话题状态
     */
	String ADVICE_STATUS = "advice_status";
	enum AdviceStatusType {
		/**
         * 已明确
         */
		CLEARLY_DEFINED("clearly_defined") ,
		/**
         * 不明确
         */
		UNCLEARLY_DEFINED("unclearly_defined") ,
		/**
         * 未提问
         */
		NOT_ASKED("not_asked") ,
		/**
         * 已获取但不明确
         */
		OBTAINED_BUT_UNCLEARLY_DEFINED("obtained_but_unclearly_defined") ,
		/**
         * 未应答
         */
		NOT_RESPONDED("not_responded") ,
		/**
         * 已应答
         */
		ANSWERED_ALREADY("answered_already") ;
		private final String adviceStatus;

		public String getAdviceStatusType() {
            return adviceStatus;
        }


		AdviceStatusType(String adviceStatus) {
            this.adviceStatus = adviceStatus;
        }
	}
	/**
     * 互动话题对应建议话题
     */
	String MATCH_SUGGEST_TOPIC = "match_suggest_topic";
	/**
     * 问题类型
     */
	String QUESTION_TYPE = "question_type";
	enum QuestionType {
		/**
         * 建议问题
         */
		TWO("2") ,
		/**
         * 互动问题
         */
		ONE("1") ;
		private final String questionType;

		public String getQuestionType() {
            return questionType;
        }


		QuestionType(String questionType) {
            this.questionType = questionType;
        }
	}
	/**
     * 回答总结
     */
	String ANSWER_SUMMARY = "answer_summary";
    String ANSWER_SUMMARY_O = "answer_summary__o";
	/**
     * 回答人
     */
	String ANSWER_PERSON = "answer_person";
	/**
     * 回答时间
     */
	String ANSWER_TIME = "answer_time";
	/**
     * 回答总结版本
     */
	String ANSWER_VERSION = "answer_version";
	/**
     * 问题提出人态度
     */
	String PROPOSER_ATTITUDE = "proposer_attitude";
	enum ProposerAttitudeType {
		/**
         * 不满意
         */
		DISSATISFIED("dissatisfied") ,
		/**
         * 一般
         */
		GENERAL("general") ,
		/**
         * 满意
         */
		SATISFIED("satisfied") ;
		private final String proposerAttitude;

		public String getProposerAttitudeType() {
            return proposerAttitude;
        }


		ProposerAttitudeType(String proposerAttitude) {
            this.proposerAttitude = proposerAttitude;
        }
	}
	/**
     * 问题提出人态度分析
     */
	String PROPOSER_ATTITUDE_ANALYSIS = "proposer_attitude_analysis";
	/**
     * AI建议回答内容
     */
	String AI_ANSWER_CONTENT = "ai_answer_content";
	/**
     * AI建议回答时间
     */
	String AI_ANSWER_TIME = "ai_answer_time";
}