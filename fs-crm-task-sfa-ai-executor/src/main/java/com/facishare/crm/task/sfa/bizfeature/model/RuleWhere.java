package com.facishare.crm.task.sfa.bizfeature.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class RuleWhere implements Serializable {
    @JSONField(name = "filters")
    @JsonProperty("filters")
    private List<FiltersBean> filters;
    @JSONField(name = "connector")
    @JsonProperty("connector")
    private String connector;

    public RuleWhere(List<FiltersBean> filters) {
        this.connector = Where.CONN.OR.toString();
        this.filters = filters;
    }

    public RuleWhere() {
        this.connector = Where.CONN.OR.toString();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FiltersBean implements Serializable {
        @JSONField(name = "type")
        @JsonProperty("type")
        private String type;
        @JSONField(name = "operator")
        @JsonProperty("operator")
        private String operator;
        @JSONField(name = "field_name")
        @JsonProperty("field_name")
        private String fieldName;
        @JSONField(name = "operator__s")
        @JsonProperty("operator__s")
        private String operatorS;
        @JSONField(name = "field_name__s")
        @JsonProperty("field_name__s")
        private String fieldNameS;
        @JSONField(name = "operator_name")
        @JsonProperty("operator_name")
        private String operatorName;
        @JSONField(name = "field_values__s")
        @JsonProperty("field_values__s")
        private String fieldValuesS;
        @JSONField(name = "is_master_field")
        @JsonProperty("is_master_field")
        private Boolean isMasterField;
        @JSONField(name = "field_values")
        @JsonProperty("field_values")
        private List<String> fieldValues;
        @JSONField(name = "field_name_type")
        @JsonProperty("field_name_type")
        private String fieldNameType;
        @JSONField(name = "object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;
        @JSONField(name = "object_api_name__s")
        @JsonProperty("object_api_name__s")
        private String objectApiNameS;
        /**
         * 特殊的字段类型
         * 1. "EXT#APL" --apl函数
         * 2. ""EXT#ATRR" --扩展属性
         */
        @JSONField(name = "field_value_type")
        @JsonProperty("field_value_type")
        private String fieldValueType;
        @JSONField(name = "agg_value_type")
        @JsonProperty("agg_value_type")
        private String aggValueType;
        @JSONField(name = "connector")
        @JsonProperty("connector")
        private String connector;
        @JSONField(name = "filter_type")
        @JsonProperty("filter_type")
        private String filterType;
        @JSONField(name = "return_type")
        @JsonProperty("return_type")
        private String returnType;
        @JSONField(name = "decimal_places")
        @JsonProperty("decimal_places")
        private Integer decimalPlaces;
        @JSONField(name = "default_to_zero")
        @JsonProperty("default_to_zero")
        private Boolean defaultToZero;
        @JSONField(name = "progressive_flag")
        @JsonProperty("progressive_flag")
        private Boolean progressiveFlag;
        @JSONField(name = "bind_object_api_name")
        @JsonProperty("bind_object_api_name")
        private String bindObjectApiName;
        @JSONField(name = "name_space")
        @JsonProperty("name_space")
        private String nameSpace;
        @JSONField(name = "ext_api_name")
        @JsonProperty("ext_api_name")
        private String extApiName;
        @JSONField(name = "ext_default_value")
        @JsonProperty("ext_default_value")
        private String extDefaultValue;
        private boolean fromMetric;
        /**
         * 激励规则，field_name_type == metric时。记录其id
         */
        private String metricId;
        /**
         * 前端使用，记录其名称，在后端没有具体业务逻辑
         */
        @JSONField(name = "agg_data_type")
        @JsonProperty("agg_data_type")
        private String aggDataType;
    }

    /**
     * 转换规则查询条件
     */
    public static List<Wheres> transformRuleWheres(List<RuleWhere> ruleWhereList) {
        List<Wheres> wheresList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(ruleWhereList)) {
            return wheresList;
        }
        for (RuleWhere ruleWhere : ruleWhereList) {
            List<IFilter> filters = Lists.newArrayList();
            List<RuleWhere.FiltersBean> innerFilter = ruleWhere.getFilters();
            if (CollectionUtils.isEmpty(innerFilter)) {
                continue;
            }
            innerFilter.forEach(filter -> filters.add(SearchTemplateQueryPlus.getFilter(filter.getFieldName(),
                    Operator.valueOf(filter.getOperator()), filter.getFieldValues())));
            Wheres wheres = new Wheres();
            wheres.setFilters(filters);
            wheresList.add(wheres);
        }
        return wheresList;
    }
}
