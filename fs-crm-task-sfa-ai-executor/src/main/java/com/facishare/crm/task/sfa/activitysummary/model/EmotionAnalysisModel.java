package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.Data;

import java.util.List;

public interface EmotionAnalysisModel {

    @Data
    class EmotionAnalysisSummary {
        private String emotionalTendency;
        private String summary;
        private List<UserAnalysis> userAnalysisList;
    }

    @Data
    class UserAnalysis {
        private String userName;
        private String emotionalTendency;
        private String emotionalTendencyValue;
        private String analysis;
    }


    @Data
    class EmotionAnalysisDetail {
        private String quoteText;
        private String quoteContext;
        private String emotionType;
        private String emotionalTendency;
        private String emotionPoint;
    }
}
