package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 特征分数常量
 *
 * <AUTHOR>
 */
public interface FeatureScoreConstants {
	/**
     * 特征
     */
	String FEATURE_ID = "feature_id";
	/**
     * 特征值
     */
	String FEATURE_VALUE_ID = "feature_value_id";
	/**
     * 评分规则
     */
	String SCORING_RULE_ID = "scoring_rule_id";
	/**
     * 匹配对象
     */
	String OBJECT_API_NAME = "object_api_name";
	/**
     * 对象id
     */
	String OBJECT_ID = "object_id";
	/**
     * 分数
     */
	String SCORE = "score";
	/**
     * 计算时间
     */
	String CALC_TIME = "calc_time";
     /**
      * 方法论
      */
	String METHODOLOGY_ID = "methodology_id";
     /**
      * 方法论实例
      */
	String METHODOLOGY_INSTANCE_ID = "methodology_instance_id";
     /**
      * 任务id
      */
	String TASK_ID = "task_id";
}