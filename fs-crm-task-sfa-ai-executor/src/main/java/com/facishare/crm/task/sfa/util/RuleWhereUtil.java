package com.facishare.crm.task.sfa.util;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.bizfeature.model.RuleWhere;
import com.facishare.crm.task.sfa.bizfeature.model.UseRangeInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class RuleWhereUtil {
    public static List<RuleWhere> getRuleWhereListByUseRange(IObjectData objectData, String objectApiName) {
        String conditionStr = objectData.get(objectApiName, String.class);
        if (StringUtils.isBlank(conditionStr)) {
            return Lists.newArrayList();
        }
        UseRangeInfo useRangeInfo = JSON.parseObject(conditionStr, UseRangeInfo.class);
        if (!"CONDITION".equals(useRangeInfo.getType())) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(useRangeInfo.getValue(), RuleWhere.class);
    }

    public static String getRuleType(IObjectData objectData, String objectApiName) {
        String conditionStr = objectData.get(objectApiName, String.class);
        if (StringUtils.isBlank(conditionStr)) {
            return "";
        }
        UseRangeInfo useRangeInfo = JSON.parseObject(conditionStr, UseRangeInfo.class);
        return useRangeInfo.getType();
    }
}
