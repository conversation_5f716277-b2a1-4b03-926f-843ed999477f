package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyInstanceConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.TaskInstanceConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.NodeInstanceConstants;
import com.facishare.crm.task.sfa.bizfeature.model.MethodologyModel;
import com.facishare.crm.task.sfa.bizfeature.service.dao.TaskFeatureDao;
import com.facishare.crm.task.sfa.bizfeature.service.dao.NodeFeatureDao;
import com.facishare.crm.task.sfa.model.ObjectData;
import com.facishare.crm.task.sfa.services.SFALicenseService;
import com.facishare.crm.task.sfa.util.BuildDataUtil;
import com.facishare.crm.task.sfa.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MethodologyServiceImpl implements MethodologyService {

    @Resource
    private SFALicenseService sfaLicenseService;

    @Resource
    private MethodologyDataService methodologyDataService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private TaskFeatureDao taskFeatureDao;

    @Resource
    private NodeFeatureDao nodeFeatureDao;

    @Override
    public void consumer(ObjectData.ObjectChange message) {
        if (message == null) {
            return;
        }
        // 只记录关键信息，避免输出过长
        String objectApiName = message.getEntityId();
        String objectId = message.getObjectId();

        // 检查license
        if (message.getContext() == null || message.getContext().getTenantId() == null) {
            log.info("No tenantId found");
            return;
        }

        User user = User.systemUser(message.getContext().getTenantId());
        String aiLicense = "ai_interactive_assistant_app";
        if (!sfaLicenseService.checkModuleLicenseExist(user.getTenantId(), aiLicense)) {
            log.info("No aiLicense found");
            return;
        }

        if (!SFAConfigUtil.isOpenCustomerProfileAgent(user.getTenantId())) {
            log.info("no customer profile agent, tenantId:{}", user.getTenantId());
            return;
        }
        log.info("Processing: objectApiName={}, objectId={}", objectApiName, objectId);

        // 查询方法论数据
        List<IObjectData> methodologyList = methodologyDataService.queryAllMethodology(user);
        if (CollectionUtils.isEmpty(methodologyList)) {
            log.info("No methodology data found");
            return;
        }

        // 根据id查询明细数据
        List<IObjectData> detailRuleList = methodologyDataService.queryRuleList(user, objectApiName);
        if (CollectionUtils.isEmpty(detailRuleList)) {
            log.info("No detail data found");
            return;
        }

        // 使用统一方法论处理逻辑，根据不同对象类型选择合适的方法论
        MethodologyModel.MatchMethodology matchMethodologys = methodologyDataService.unifyMethodology(objectApiName,
                objectId, user,
                methodologyList, detailRuleList);
        IObjectData matchMethodology = matchMethodologys.getMatchFlow();
        if (matchMethodology == null && CollectionUtils.isEmpty(matchMethodologys.getMatchProfileList())) {
            log.info("No matching methodology found for object: {},objectApiName: {}", objectId, objectApiName);
            return;
        }
        MethodologyModel.AllInstanceData allInstanceData = MethodologyModel.AllInstanceData.builder().build();
        if (matchMethodology != null) {
            MethodologyModel.AllInstanceData flowInstanceData = makeMethodologyInstance(matchMethodology, user,
                    objectApiName,
                    objectId);
            if (flowInstanceData != null) {
                allInstanceData = flowInstanceData;
            }
        }

        if (!CollectionUtils.isEmpty(matchMethodologys.getMatchProfileList())) {
            for (IObjectData matchMethodology1 : matchMethodologys.getMatchProfileList()) {
                MethodologyModel.AllInstanceData matchInstanceData = makeMethodologyInstance(matchMethodology1, user,
                        objectApiName,
                        objectId);
                if (matchInstanceData != null) {
                    if (!CollectionUtils.isEmpty(matchInstanceData.getUpMethodologyInstance())) {
                        allInstanceData.getUpMethodologyInstance().addAll(matchInstanceData.getUpMethodologyInstance());
                    }
                    if (!CollectionUtils.isEmpty(matchInstanceData.getMethodologyInstance())) {
                        allInstanceData.getMethodologyInstance().addAll(matchInstanceData.getMethodologyInstance());
                    }
                    if (!CollectionUtils.isEmpty(matchInstanceData.getNodeInstanceList())) {
                        allInstanceData.getNodeInstanceList().addAll(matchInstanceData.getNodeInstanceList());
                    }
                    if (!CollectionUtils.isEmpty(matchInstanceData.getTaskInstanceList())) {
                        allInstanceData.getTaskInstanceList().addAll(matchInstanceData.getTaskInstanceList());
                    }
                    if (!CollectionUtils.isEmpty(matchInstanceData.getInstanceFeatureList())) {
                        allInstanceData.getInstanceFeatureList().addAll(matchInstanceData.getInstanceFeatureList());
                    }
                }
            }
        }

        // 保存所有实例数据（事务控制）
        methodologyDataService.saveAllInstanceData(allInstanceData, user);
        // log.info(
        // "saveAllInstanceData success methodologyInstance: {}, nodeInstanceList: {},
        // taskInstanceList: {} ,objectApiName: {}, objectId: {}",
        // methodologyInstance.getId(), nodeInstanceList.size(),
        // taskInstanceList.size(), objectApiName, objectId);
    }

    public MethodologyModel.AllInstanceData makeMethodologyInstance(IObjectData matchMethodology, User user,
                                                                    String objectApiName,
                                                                    String objectId) {
        log.info("Found matching methodology: {} for object: {},objectApiName: {}",
                matchMethodology.getId(), objectId, objectApiName);

        // 获取工作流节点
        List<IObjectData> workFlowNodeList = getWorkFlowNodeList(matchMethodology, user, objectApiName, objectId);
        if (workFlowNodeList == null) {
            return null;
        }

        // 构建方法论实例
        MethodologyInstanceData methodologyInstanceData = buildMethodologyInstanceData(matchMethodology, objectApiName, objectId, user);
        
        // 构建节点实例
        List<IObjectData> nodeInstanceList = buildNodeInstanceList(workFlowNodeList, matchMethodology, objectApiName, objectId, methodologyInstanceData.getMethodologyInstance(), user);

        // 根据方法论类型构建特征实例
        InstanceFeatureData instanceFeatureData = buildInstanceFeatureData(matchMethodology, nodeInstanceList, methodologyInstanceData.getMethodologyInstance(), user, objectApiName, objectId);
        if (instanceFeatureData == null) {
            return null;
        }

        return MethodologyModel.AllInstanceData.builder()
                .methodologyInstance(methodologyInstanceData.getMethodologyInstanceList())
                .upMethodologyInstance(methodologyInstanceData.getUpMethodologyInstanceList())
                .nodeInstanceList(nodeInstanceList)
                .taskInstanceList(instanceFeatureData.getTaskInstanceList())
                .instanceFeatureList(instanceFeatureData.getInstanceFeatureList())
                .build();
    }

    private List<IObjectData> getWorkFlowNodeList(IObjectData matchMethodology, User user, String objectApiName, String objectId) {
        List<IObjectData> workFlowNodeList = methodologyDataService.getWorkFlowNode(matchMethodology, user, objectApiName, objectId);
        if (CollectionUtils.isEmpty(workFlowNodeList)) {
            log.info("No matching workflow node found for object: {},objectApiName: {}", objectId, objectApiName);
            return null;
        }
        log.info("Found matching workflow node for object: {},objectApiName: {}", objectId, objectApiName);
        return workFlowNodeList;
    }

    private MethodologyInstanceData buildMethodologyInstanceData(IObjectData matchMethodology, String objectApiName, String objectId, User user) {
        List<IObjectData> methodologyInstanceList = Lists.newArrayList();
        List<IObjectData> upMethodologyInstanceList = Lists.newArrayList();
        
        IObjectData methodologyInstance = (IObjectData) matchMethodology.get(MethodologyConstants.TEMP_INSTANCE);
        if (methodologyInstance != null) {
            upMethodologyInstanceList.add(methodologyInstance);
            setObjectIdToMethodologyInstance(methodologyInstance, objectApiName, objectId);
        } else {
            methodologyInstance = BuildDataUtil.buildMethodologyInstance(matchMethodology, objectApiName, objectId, user);
            methodologyInstanceList.add(methodologyInstance);
        }
        
        return new MethodologyInstanceData(methodologyInstanceList, upMethodologyInstanceList, methodologyInstance);
    }

    private void setObjectIdToMethodologyInstance(IObjectData methodologyInstance, String objectApiName, String objectId) {
        switch (objectApiName) {
            case FeatureConstants.LEADS_OBJ:
                methodologyInstance.set(MethodologyInstanceConstants.LEAD_ID, objectId);
                break;
            case FeatureConstants.ACCOUNT_OBJ:
                methodologyInstance.set(MethodologyInstanceConstants.ACCOUNT_ID, objectId);
                break;
            case FeatureConstants.NEW_OPPORTUNITY_OBJ:
                methodologyInstance.set(MethodologyInstanceConstants.OPPORTUNITY_ID, objectId);
                break;
        }
    }

    private List<IObjectData> buildNodeInstanceList(List<IObjectData> workFlowNodeList, IObjectData matchMethodology, 
                                                   String objectApiName, String objectId, IObjectData methodologyInstance, User user) {
        IObjectData objectData = serviceFacade.findObjectData(user, objectId, objectApiName);
        
        BuildDataUtil.NodeInstanceBuildParam nodeInstanceParam = BuildDataUtil.NodeInstanceBuildParam.builder()
                .workFlowNodeList(workFlowNodeList)
                .matchMethodology(matchMethodology)
                .objectData(objectData)
                .objectApiName(objectApiName)
                .objectId(objectId)
                .methodologyInstance(methodologyInstance)
                .user(user)
                .build();

        return BuildDataUtil.buildNodeInstance(nodeInstanceParam);
    }

    private InstanceFeatureData buildInstanceFeatureData(IObjectData matchMethodology, List<IObjectData> nodeInstanceList, 
                                                        IObjectData methodologyInstance, User user, String objectApiName, String objectId) {
        String methodologyType = matchMethodology.get(MethodologyConstants.TYPE, String.class);
        
        if (MethodologyConstants.Type.FLOW.getType().equals(methodologyType)) {
            return buildFlowInstanceFeatureData(matchMethodology, nodeInstanceList, methodologyInstance, user, objectApiName, objectId);
        } else if (MethodologyConstants.Type.PROFILE.getType().equals(methodologyType)) {
            return buildProfileInstanceFeatureData(matchMethodology, nodeInstanceList, methodologyInstance, user, objectApiName, objectId);
        }
        log.info("Unknown methodology type: {}, object: {},objectApiName: {}", methodologyType, objectId, objectApiName);
        return null;
    }

    private InstanceFeatureData buildFlowInstanceFeatureData(IObjectData matchMethodology, List<IObjectData> nodeInstanceList, 
                                                           IObjectData methodologyInstance, User user, String objectApiName, String objectId) {
        List<IObjectData> nodeTaskList = methodologyDataService.queryNodeTask(nodeInstanceList, matchMethodology, user);
        List<IObjectData> taskInstanceList = BuildDataUtil.buildTaskInstance(matchMethodology, methodologyInstance,
                nodeInstanceList, nodeTaskList, user, objectApiName, objectId);

        if (CollectionUtils.isEmpty(taskInstanceList)) {
            log.info("No matching task instance found for flow methodology: {},objectApiName: {}", objectId, objectApiName);
            return null;
        }

        List<String> taskIds = extractTaskIds(taskInstanceList);
        List<IObjectData> taskFeatureList = taskFeatureDao.fetchTaskFeaturesByTaskIds(user, taskIds);
        List<IObjectData> instanceFeatureList = BuildDataUtil.buildInstanceFeature(
                taskFeatureList, taskInstanceList, methodologyInstance, user);

        if (CollectionUtils.isEmpty(instanceFeatureList)) {
            log.info("No matching feature found for flow methodology: {}, object: {},objectApiName: {}",
                    matchMethodology.getId(), objectId, objectApiName);
            return null;
        }

        log.info("Found matching task instance for object: {},objectApiName: {}", objectId, objectApiName);
        return new InstanceFeatureData(taskInstanceList, instanceFeatureList);
    }

    private InstanceFeatureData buildProfileInstanceFeatureData(IObjectData matchMethodology, List<IObjectData> nodeInstanceList, 
                                                              IObjectData methodologyInstance, User user, String objectApiName, String objectId) {
        List<String> nodeIds = extractNodeIds(nodeInstanceList);
        List<IObjectData> nodeFeatureList = nodeFeatureDao.fetchNodeFeaturesByNodeIdsAndMethodologyId(
                user, nodeIds, matchMethodology.getId());

        List<IObjectData> instanceFeatureList = BuildDataUtil.buildInstanceFeatureFromNodeFeature(
                nodeFeatureList, nodeInstanceList, methodologyInstance, user);

        if (CollectionUtils.isEmpty(instanceFeatureList)) {
            log.info("No matching feature found for profile methodology: {}, object: {},objectApiName: {}",
                    matchMethodology.getId(), objectId, objectApiName);
            return null;
        }

        log.info("Found matching profile feature for object: {},objectApiName: {}", objectId, objectApiName);
        return new InstanceFeatureData(Lists.newArrayList(), instanceFeatureList);
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    private static class MethodologyInstanceData {
        private List<IObjectData> methodologyInstanceList;
        private List<IObjectData> upMethodologyInstanceList;
        private IObjectData methodologyInstance;
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    private static class InstanceFeatureData {
        private List<IObjectData> taskInstanceList;
        private List<IObjectData> instanceFeatureList;
    }

    /**
     * 从任务实例列表中提取任务ID列表
     */
    private List<String> extractTaskIds(List<IObjectData> taskInstanceList) {
        return taskInstanceList.stream()
                .map(taskInstance -> taskInstance.get(TaskInstanceConstants.TASK_ID, String.class))
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 从节点实例列表中提取节点ID列表
     */
    private List<String> extractNodeIds(List<IObjectData> nodeInstanceList) {
        return nodeInstanceList.stream()
                .map(nodeInstance -> nodeInstance.get(NodeInstanceConstants.NODE_ID, String.class))
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
    }

}
