package com.facishare.crm.task.sfa.rest.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/17 10:30
 * @description:
 */
public interface FileParseText {
    @Data
    class Result {
        Boolean success;
        Integer code;
        String message;
        ResultData data;
    }

    @Data
    class ResultData {
        String txtPath;
        Boolean standard;
        List<ShardContents> shardContents;
    }

    @Data
    class ShardContents {
        Integer sequence;
        Boolean titleOrNot;
        Integer level;
        Integer pageIndex;
        String content;
        String title;
    }
}
