package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 活动标签模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityTagMessage {
    /**
     * 段落id
     */
    private List<String> paragraphIds;
    /**
     * 企业id
     */
    private String tenantId;
    /**
     * userId
     */
    private String userId;
    /**
     * 类型 mongo,text
     */
    private String type;

    private String objectId;

    private String objectApiName;
} 