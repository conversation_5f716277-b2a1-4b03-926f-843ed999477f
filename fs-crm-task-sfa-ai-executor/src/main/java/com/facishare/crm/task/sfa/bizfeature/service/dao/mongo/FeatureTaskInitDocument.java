package com.facishare.crm.task.sfa.bizfeature.service.dao.mongo;


import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

@Data
@Entity(value = "feature_scheduled_task_init",noClassnameStored = true)
public class FeatureTaskInitDocument implements Serializable {
    @Id
    private ObjectId id; // MongoDB主键

    @Property("tenantId")
    private String tenantId; // 企业id

    @Property("createTime")
    private Long createTime; // 创建时间

}
