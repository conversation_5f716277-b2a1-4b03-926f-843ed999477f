package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 *
 * @IgnoreI18nFile
 */
public interface FeatureTaskConstants {
    class TaskFields {
        public static final String ID = "id";
        public static final String TENANT_ID = "tenantId";
        public static final String STATUS = "status";
        public static final String FAIL_REASON = "failReason";
        public static final String CREATE_BY = "createBy";
        public static final String CREATE_TIME = "createTime";
        public static final String LAST_MODIFY_TIME = "lastModifyTime";
    }

    class TaskStatus {
        public static final String INIT = "init";
        public static final String RUNNING = "running";
        public static final String FINISHED = "finished";
        public static final String FAIL = "fail";
    }

    enum ScheduledType {
        DAY("DAY", "每天"),
        WEEK("WEEK", "每周"),
        MONTH("MONTH", "每月"),
        QUARTER("QUARTER", "每季度"),
        YEAR("YEAR", "每年");

        private final String value;

        ScheduledType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }
}
