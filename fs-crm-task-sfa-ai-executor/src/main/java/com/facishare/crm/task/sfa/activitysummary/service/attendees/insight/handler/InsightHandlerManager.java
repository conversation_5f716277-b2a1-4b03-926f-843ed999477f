package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler;


import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.crm.task.sfa.util.SFAConcurrentLock;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Slf4j
@Component
public class InsightHandlerManager {


    @Autowired
    private List<InsightHandler> insightHandlers;
    @Autowired
    private SFAConcurrentLock sfaConcurrentLock;


    public void executeInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {

        List<String> insightTypeList = attendeesInsightMessage.getInsightTypeList();
        if (Safes.isEmpty(insightTypeList)) {
            parallelInsight(attendeesInsightMessage, insightHandlers);
            return;
        }

        List<InsightHandler> matchedHandlers = insightHandlers.stream().filter(d -> insightTypeList.contains(d.getInsightType())).collect(Collectors.toList());
        if (Safes.isNotEmpty(matchedHandlers)) {
            parallelInsight(attendeesInsightMessage, matchedHandlers);
        } else {
            log.warn("No insight handler found for insight types: {}", insightTypeList);
        }
    }

    private void parallelInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, List<InsightHandler> insightHandlers) {
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        for (InsightHandler insightHandler : insightHandlers) {
            parallelTask.submit(() -> insightInTrace(attendeesInsightMessage, insightHandler));
        }
        try {
            parallelTask.await(5 , TimeUnit.MINUTES);
        } catch (TimeoutException e) {
            log.error("Timeout executing insight handlers", e);
        }
    }

    private void insightInTrace(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, InsightHandler insightHandler) {
        String activeRecordId = attendeesInsightMessage.getActiveRecordId();
        String insightType = insightHandler.getInsightType();
        try {
            log.info("Executing insight handler: {}, activeRecordId:{}", insightHandler.getClass().getSimpleName(), activeRecordId);
            sfaConcurrentLock.runIfGetLock(getLockKey(insightType, activeRecordId), 5 * 6000, () ->
                    insightHandler.insight(attendeesInsightMessage)
            );
            log.info("Finished executing insight handler: {}, activeRecordId:{}", insightHandler.getClass().getSimpleName(), activeRecordId);
        } catch (Exception e) {
            log.error("Error executing insight handler: {}, activeRecordId:{}", insightHandler.getClass().getSimpleName(), activeRecordId, e);
        }
    }

    private String getLockKey(String insightType, String activeRecordId) {
        return insightType + ":" + activeRecordId;
    }

}
