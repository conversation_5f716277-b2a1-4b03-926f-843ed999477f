package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.rest.dto.RoleModel;
import com.facishare.paas.appframework.privilege.dto.AddRoleModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 *PAAS角色
 */
@RestResource(value = "PAAS-PRIVILEGE", desc = "pass user role proxy ", contentType = "application/json")
public interface PaasUserRoleProxy {
    @POST(value = "/addRole", desc = "增加角色")
    RoleModel.AddRoleResult addRole(@Body RoleModel.AddRoleArg arg, @HeaderMap Map<String, String> header);
}
