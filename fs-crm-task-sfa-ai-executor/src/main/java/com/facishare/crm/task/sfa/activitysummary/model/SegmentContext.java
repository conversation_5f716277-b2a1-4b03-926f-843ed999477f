package com.facishare.crm.task.sfa.activitysummary.model;

import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.Map;

/**
 * 分段上下文信息
 * 用于在批次处理之间传递分段信息，以便进行话题衔接检查
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SegmentContext {

    /**
     * 上次最后一次的 ParagraphResultModel.Chunk，不保存
     * 如果检查通过了，需要合并，保存到下一次
     * 如果不通过检查则单独保存
     */
    private ParagraphResultModel.Chunk lastChunk;

    /**
     * 是否是第一个批次
     */
    private boolean isFirstBatch;

    /**
     * id和文档的映射，存储一批的文档，保存段落的时候补充信息
     */
    private Map<String, InteractiveDocument> lastChunkIdToDocumentMapping;

    /**
     * 具体的内容
     */
    private List<String> lastChunkContentList;

    /**
     * id集合，和内容集合一一对应
     */
    private List<String> lastChunkIdList;
    
    /**
     * 创建第一个批次的上下文
     */
    public static SegmentContext createFirstBatch() {
        SegmentContext context = new SegmentContext();
        context.setFirstBatch(true);
        return context;
    }

    /**
     * 更新上下文信息
     */
    public void updateContext(Map<String, InteractiveDocument> idToDocumentMapping,
                             List<String> contentList,
                             List<String> idList) {
        this.lastChunkIdToDocumentMapping = idToDocumentMapping;
        this.lastChunkContentList = contentList;
        this.lastChunkIdList = idList;
        this.isFirstBatch = false;
    }

    /**
     * 设置上次的分段块（不保存）
     * 如果检查通过了，需要合并，保存到下一次
     * 如果不通过检查则单独保存
     */
    public void setLastChunkForCheck(ParagraphResultModel.Chunk chunk) {
        this.lastChunk = chunk;
    }

}
