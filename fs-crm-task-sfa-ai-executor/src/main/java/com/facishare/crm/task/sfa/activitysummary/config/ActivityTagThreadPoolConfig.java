package com.facishare.crm.task.sfa.activitysummary.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 线程池配置类
 */
@Configuration
public class ActivityTagThreadPoolConfig {

    /**
     * 活动标签处理线程池
     */
    @Bean("activityTagExecutor")
    public ExecutorService activityTagExecutor() {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("ActivityTag-%d")
                .setDaemon(true)
                .build();
                
        return new ThreadPoolExecutor(
                50,                     // 核心线程数
                200,                    // 最大线程数
                0,                      // 空闲线程存活时间
                TimeUnit.MILLISECONDS,  // 时间单位
                new ArrayBlockingQueue<>(50), // 工作队列
                workerFactory,          // 线程工厂
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：由调用线程处理
        );
    }
} 