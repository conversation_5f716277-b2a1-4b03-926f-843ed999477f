package com.facishare.crm.task.sfa.activitysummary.model;

import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.*;

import java.util.Map;

public interface InteractiveScenarioModel {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ScenarioContext {
        /** 配置文件中快速区分各个业务组件 */
        private String moduleApiName;
        /** 当前的业务使用的提示词模板 */
        private String templateApiName;
        /** 提示词片段代入提示词时的变量名，默认为：content */
        private String useVariableName;
        /** 不同场景的提示词模板配置 */
        private Map<String, TemplateConfig> template;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class TemplateConfig {
        /** 提示词片段 */
        private String content;
        /** 最终要使用的提示词模板ApiName */
        private String templateApiName;
        /** 原始配置键值对 */
        private Map<String, String> sourceConfig;
    }


    @Getter
    enum ModelApiName{
        /** 全文摘要 */
        FULL_TEXT_SUMMARY("full_text_summary"),
        /** 章节概览 */
        CHAPTER_PREVIEW("chapter_preview"),
        /** 互动话题 */
        ACTIVITY_QUESTION("activity_question");

        private final String apiName;

        ModelApiName(String apiName){
            this.apiName = apiName;
        }

        public static ModelApiName transition(String apiName) {
            for (ModelApiName model : values()) {
                if (model.apiName.equals(apiName)) {
                    return model;
                }
            }
            throw new ValidateException("ModelApiName error:" + apiName);
        }

    }

}
