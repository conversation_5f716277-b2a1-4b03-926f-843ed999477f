package com.facishare.crm.task.sfa.activitysummary.constant;

import com.alibaba.fastjson2.JSON;
import com.esotericsoftware.minlog.Log;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTag;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.ImmutableMap;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 活动标签映射关系
 */
public class ActivityTagMap {
    private ActivityTagMap() {
    }

    /**
     * 标签映射关系
     */
    public static Map<String, ActivityTag> TAG_MAP;

    @Data
    private static class TagConfig {
        private List<ActivityTag> tags;
    }

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            ImmutableMap.Builder<String, ActivityTag> builder = ImmutableMap.builder();
            try {
                // 从resources目录下读取JSON配置文件
                String jsonContent = config.get("paragraph_tags","");

                // 解析JSON配置
                TagConfig tagConfig = JSON.parseObject(jsonContent, TagConfig.class);

                // 构建TAG_MAP
                for (ActivityTag parentTag : tagConfig.getTags()) {
                    builder.put(parentTag.getId(), parentTag);
                }

            } catch (Exception e) {
                Log.error("Failed to load activity tags configuration", e);
            } finally {
                TAG_MAP = builder.build();
            }
        });

    }

    /**
     * 根据标签ID获取标签
     */
    public static ActivityTag getTagById(String tagId) {
        return TAG_MAP.get(tagId);
    }
} 