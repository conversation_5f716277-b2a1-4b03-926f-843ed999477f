package com.facishare.crm.task.sfa.activitysummary.model;

import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

@Data
public class ParagraphContext {
    public static final int MIN_CONTENT_LENGTH = 3000;
    public static final String MONGO_KEY = "mongo";
    public static final String TEXT_KEY = "text";
    //具体的内容
    private List<String> contentList;
    //id集合，和内容集合一一对应
    private List<String> idList;
    // id和索引的映射，用作mongo数据，请求大模型之前替换id改为index。可以减少token输出
    private BiMap<String, String> idToIndexMapping;
    // id和文档的映射，存储一批的文档，保存段落的时候补充信息
    private Map<String, InteractiveDocument> idToDocumentMapping;
    private int contentSumLength;
    // 销售记录数据，保存段落的时候补充信息
    private IObjectData activeRecordData;
    // 段落序号
    private int seqNo;
    //用于最后区分 mongo,text
    private String type;
    // 是否是最后的批次
    private boolean isEnd;
    // 分段上下文信息，用于话题衔接检查
    private SegmentContext segmentContext;

    public ParagraphContext() {
        this.contentList = Lists.newArrayList();
        this.idList = Lists.newArrayList();
        this.idToIndexMapping = HashBiMap.create();
        this.idToDocumentMapping = Maps.newLinkedHashMap();
        this.contentSumLength = 0;
        this.seqNo = 1;
    }

    public int getAddSeqNo() {
        return this.seqNo++;
    }

    public void addContent(String content, String id) {
        this.contentList.add(content);
        this.idList.add(id);
        this.contentSumLength += content.length();
    }

    
    public void addContent(InteractiveDocument document) {
        this.contentList.add(document.getContent());
        this.idList.add(document.getId().toString());
        this.contentSumLength += document.getContent().length();
        this.idToDocumentMapping.put(document.getId().toString(), document);
    }

    public void clearAll() {
        this.contentList.clear();
        this.idList.clear();
        this.idToIndexMapping.clear();
        this.idToDocumentMapping.clear();
        this.contentSumLength = 0;
    }

    public boolean isContentEmpty() {
        return this.contentList.isEmpty();
    }

    public int size() {
        return this.contentSumLength;
    }

    public boolean isContentLengthSufficient() {
        return this.size() > MIN_CONTENT_LENGTH;
    }

    public String getContentByIndex(int index) {
        StringBuilder contentBuilder = new StringBuilder();
        //增加数组下标越界校验
        if (index < 0 || index >= this.idList.size() || index >= this.contentList.size()) {
            return null;
        }
        contentBuilder.append("【").append(this.idList.get(index)).append("】").append(this.contentList.get(index)).append("\n");
        return contentBuilder.toString();
    }

    public String contentBuilder() {
        StringBuilder contentBuilder = new StringBuilder();
        for (int i = 0; i < idList.size(); i++) {
            contentBuilder.append(getContentByIndex(i));
        }
        return contentBuilder.toString();
    }

    public void buildMapping() {
        if(CollectionUtils.isEmpty(this.idList)){
            return;
        }
        List<String> newIndexList = Lists.newArrayList();
        this.idToIndexMapping.clear();
        for (int i = 0; i < idList.size(); i++) {
            String id = idList.get(i);
            String index = String.valueOf(i);
            idToIndexMapping.put(id, index);
            newIndexList.add(index);
        }
        this.idList = newIndexList;
    }

    public List<String> restoreIdsFromIndexes(ParagraphResultModel.Chunk chunk) {
        if (CollectionUtils.isEmpty(chunk.getContent())) {
            return Lists.newArrayList();
        }
        List<String> restoredIds = Lists.newArrayList();
        for (String content : chunk.getContent()) {
            String id = idToIndexMapping.inverse().get(content);
            restoredIds.add(id);
        }
        return restoredIds;
    }

    /**
     * 初始化分段上下文
     */
    public void initSegmentContext() {
        if (this.segmentContext == null) {
            this.segmentContext = SegmentContext.createFirstBatch();
        }
    }
}
