package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileItemScoreConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileProsConsConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProfileProsConsDao {

    @Autowired
    private ServiceFacade serviceFacade;
    public List<IObjectData> fetchProfileProsConsByProfileIdAndDimensionId(User user, String profileId,String featureDimensionId) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileProsConsConstants.PROFILE_ID, profileId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileProsConsConstants.RANGE_TYPE, ProfileProsConsConstants.RangeType.DIMENSION.getValue());
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileProsConsConstants.FEATURE_DIMENSION_ID, featureDimensionId);
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.PROFILE_PROS_CONS, query).getData();
    }
    public List<IObjectData> fetchDatasByProfileIdWithProfileType(User user, String profileId) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileProsConsConstants.PROFILE_ID, profileId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileProsConsConstants.RANGE_TYPE, ProfileProsConsConstants.RangeType.PROFILE.getValue());
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.PROFILE_PROS_CONS, query).getData();
    }
}
