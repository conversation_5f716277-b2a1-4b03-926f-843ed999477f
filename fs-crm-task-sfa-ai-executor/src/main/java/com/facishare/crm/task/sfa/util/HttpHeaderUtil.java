package com.facishare.crm.task.sfa.util;

import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;

import java.util.Map;

import static com.facishare.crm.task.sfa.common.constants.CommonConstant.CRM_APP_ID;
import static com.facishare.crm.task.sfa.common.constants.CommonConstant.SUPER_USER;

public class HttpHeaderUtil {

    public static Map<String, String> getHeaders(User user) {
        Map<String, String> header = Maps.newHashMap();
        header.put("x-fs-Employee-Id", user.getUserId());
        header.put("x-fs-Enterprise-Id", user.getTenantId());
        header.put("x-fs-ei", user.getTenantId());
        header.put("x-fs-userInfo", user.getUserId());
        return header;
    }

    public static Map<String, String> getHeaders(String userId, String tenantId) {
        Map<String, String> header = Maps.newHashMap();
        header.put("x-fs-Employee-Id", userId);
        header.put("x-fs-Enterprise-Id", tenantId);
        header.put("x-fs-ei", tenantId);
        header.put("x-fs-userInfo", userId);
        return header;
    }

    public static Map<String, String> getHeaders(User user, String appID) {
        Map<String, String> header = Maps.newHashMap();
        header.put("x-fs-Employee-Id", user.getUserId());
        header.put("x-fs-Enterprise-Id", user.getTenantId());
        header.put("x-fs-ei", user.getTenantId());
        header.put("x-fs-userInfo", user.getUserId());
        if (user.isOutUser()) {
            header.put("x-out-user-id", user.getOutUserId());
            header.put("x-out-tenant-id", user.getOutTenantId());
            header.put("x-app-id", appID);
        }
        return header;
    }

    public static Map<String, String> getHeaders(String tenantId) {
        return getHeaders(new User(tenantId,SUPER_USER),CRM_APP_ID);
    }
}
