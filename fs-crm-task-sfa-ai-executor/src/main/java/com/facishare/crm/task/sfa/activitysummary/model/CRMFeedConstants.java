package com.facishare.crm.task.sfa.activitysummary.model;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/1/4 17:11
 * @description:
 */
public interface CRMFeedConstants {
    class Field {
        public static final String NAME = "related_name";//销售记录关联对象主属性
        public static final String FEED_TYPE = "feed_type";//销售记录信息类型
        public static final String SEND_TIME = "send_time";//销售记录发送时间
        public static final String SEND_BY = "send_by";//销售记录发送人ID
        public static final String RELATED_TYPE = "related_type";//销售记录关联对象类型
        public static final String OWNER = "owner";
        public static final String RELATED_OBJECT = "related_object";
        public static final String ACTIVE_RECORD_TYPE = "active_record_type";
        public static final String ACTIVE_RECORD_CONTENT = "active_record_content";
        public static final String TEXT = "text";
        public static final String __xt = "__xt";
        public static final String __json = "__json";
        public static final String TYPE = "type";
        public static final String CONTENT = "content";


        public static final String ATTACHMENTS = "attachments";

    }
}
