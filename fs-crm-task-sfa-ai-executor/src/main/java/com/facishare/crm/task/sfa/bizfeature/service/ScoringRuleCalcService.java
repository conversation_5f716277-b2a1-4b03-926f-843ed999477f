package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureNode;
import org.apache.rocketmq.common.message.Message;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureScoreModel;
import com.facishare.crm.task.sfa.bizfeature.model.RuleContentModel;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.util.FeatureBaseDataUtils;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.functions.utils.Maps;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ScoringRuleCalcService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private BaseDataMergeManager baseDataMergeManager;
    @Autowired
    private CompletionsService completionsService;
    @Resource(name = "featureNodeProducer")
    private AutoConfMQProducer producer;

    public void featureValueScoreCalc(String tenantId, List<IObjectData> featureValueList) {
        if (StringUtils.isEmpty(tenantId) || CollectionUtils.isEmpty(featureValueList)) {
            log.error("featureValueScoreCalc error, tenantId or featureValueList is null, tenantId:{}", tenantId);
            return;
        }
        StopWatch stopWatch = StopWatch.create("featureValueScoreCalc");
        User user = User.systemUser(tenantId);
        List<String> featureIds = featureValueList.stream().map(featureValue ->
                featureValue.get(FeatureValueConstants.FEATURE_ID, String.class)).collect(Collectors.toList());
        SearchTemplateQueryPlus featureQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(featureQuery).addFilter(Operator.IN, DBRecord.ID, featureIds);
        SearchTemplateQueryExt.of(featureQuery).addFilter(Operator.EQ, FeatureConstants.STATUS,
                FeatureConstants.StatusType.ENABLED.getStatusType());
        List<IObjectData> featureList = baseDataMergeManager.findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE, featureQuery);
        stopWatch.lap("findFeature");
        if (CollectionUtils.isEmpty(featureList)) {
            log.info("featureList is empty, tenantId:{}", tenantId);
            return;
        }
        SearchTemplateQueryPlus featureScoreRuleQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(featureScoreRuleQuery).addFilter(Operator.IN, FeatureScoreRuleConstants.FEATURE_ID, featureIds);
        List<IObjectData> featureScoreRuleList = baseDataMergeManager.findBySearchQueryIgnoreAll(user,
                FeatureConstants.FEATURE_SCORE_RULE, featureScoreRuleQuery);
        stopWatch.lap("findFeatureScoreRule");
        if (CollectionUtils.isEmpty(featureScoreRuleList)) {
            log.info("featureScoreRuleList is empty, tenantId:{}", tenantId);
            return;
        }
        Map<String, List<IObjectData>> featureIdToScoringRuleMap = featureScoreRuleList.stream()
                .collect(Collectors.groupingBy(feature -> feature.get(FeatureScoreRuleConstants.FEATURE_ID, String.class)));
        List<String> scoringRuleIds = featureScoreRuleList.stream()
                .map(feature -> feature.get(FeatureConstants.SCORING_RULE_ID, String.class)).distinct().collect(Collectors.toList());
        List<IObjectData> scoringRuleList = baseDataMergeManager.findObjectDataByIdsIgnoreAll(user, scoringRuleIds,
                FeatureConstants.SCORING_RULE);
        stopWatch.lap("findScoringRule");
        if (CollectionUtils.isEmpty(scoringRuleList)) {
            log.info("scoringRuleList is empty, tenantId:{}", tenantId);
            return;
        }
        Map<String, IObjectData> scoringRuleMap = scoringRuleList.stream()
                .collect(Collectors.toMap(DBRecord::getId, scoringRule -> scoringRule));
        List<IObjectData> addScoreList = Lists.newArrayList();
        List<IObjectData> updateScoreList = Lists.newArrayList();
        List<IObjectData> addScoreHistoryList = Lists.newArrayList();
        for (IObjectData featureValue : featureValueList) {
            String featureId = featureValue.get(FeatureValueConstants.FEATURE_ID, String.class);
            List<IObjectData> featureScoreRules = featureIdToScoringRuleMap.get(featureId);
            if (CollectionUtils.isEmpty(featureScoreRules)) {
                log.warn("featureScoreRules is empty, tenantId:{}", tenantId);
                continue;
            }
            for (IObjectData featureScoreRule : featureScoreRules) {
                String featureScoreRuleId = featureScoreRule.get(FeatureScoreRuleConstants.SCORING_RULE_ID, String.class);
                if (StringUtils.isBlank(featureScoreRuleId)) {
                    log.warn("featureScoreRuleId is null, tenantId:{}", tenantId);
                    continue;
                }
                IObjectData scoringRule = scoringRuleMap.get(featureScoreRuleId);
                if (scoringRule == null) {
                    log.warn("scoringRule is null, tenantId:{}", tenantId);
                    continue;
                }
                String methodologyId = featureScoreRule.get(FeatureScoreRuleConstants.METHODOLOGY_ID, String.class);
                try {
                    calc(user, scoringRule, featureValue, methodologyId, addScoreList, updateScoreList, addScoreHistoryList);
                } catch (Exception e) {
                    log.error("feature value score calc error, tenantId: {} featureId:{} scoringRule:{}", tenantId, featureId, scoringRule, e);
                }
            }
        }
        stopWatch.lap("calc");
        List<IObjectData> scores = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(addScoreList)) {
            scores.addAll(serviceFacade.bulkSaveObjectData(addScoreList, user));
        }
        if (CollectionUtils.isNotEmpty(updateScoreList)) {
            scores.addAll(serviceFacade.batchUpdate(updateScoreList, user));
        }
        if (CollectionUtils.isNotEmpty(addScoreHistoryList)) {
            serviceFacade.bulkSaveObjectData(addScoreHistoryList, user);
        }

        if (CollectionUtils.isNotEmpty(scores)) {
            List<String> ids = scores.stream().map(IObjectData::getId).distinct().collect(Collectors.toList());
            FeatureNode featureScoring = FeatureNode.builder()
                    .tenantId(user.getTenantId())
                    .objectIds(ids)
                    .build();
            //发MQ计算特征分数
            Message msg = new Message("feature-node", "feature_score", JSON.toJSONString(featureScoring).getBytes());
            producer.send(msg);

        }

        stopWatch.lap("saveData");
        stopWatch.logSlow(1000);
    }

    public void calc(User user, IObjectData scoringRuleData, IObjectData featureValue,
                     String methodologyId, List<IObjectData> addScoreList,
                     List<IObjectData> updateScoreList, List<IObjectData> addScoreHistoryList) {
        String calcMethod = scoringRuleData.get(ScoringRuleConstants.CALC_METHOD, String.class);
        switch (calcMethod) {
            case "scoring":
                // 打分器
                scoringCalc(user, scoringRuleData, featureValue, methodologyId,
                        addScoreList, updateScoreList, addScoreHistoryList);
                break;
            case "llm":
                // LLM
                llmCalc(user, scoringRuleData, featureValue, methodologyId,
                        addScoreList, updateScoreList, addScoreHistoryList);
                break;
            default:
                log.warn("calcMethod is not supported, tenantId:{}", user.getTenantId());
                break;
        }
    }

    private void scoringCalc(User user, IObjectData scoringRuleData, IObjectData featureValue,
                             String methodologyId, List<IObjectData> addScoreList,
                             List<IObjectData> updateScoreList, List<IObjectData> addScoreHistoryList) {
        // 打分器
        BigDecimal score = scoringRuleData.get(ScoringRuleConstants.DEFAULT_VALUE, BigDecimal.class, BigDecimal.ZERO);
        List<Map<String, Object>> ruleContent = JSON.parseObject(scoringRuleData.get(ScoringRuleConstants.RULE_CONTENT, String.class), new TypeReference<List<Map<String, Object>>>() {});
        if (CollectionUtils.isEmpty(ruleContent)) {
            log.warn("ruleContent is null, tenantId:{}", user.getTenantId());
            return;
        }
        IObjectDescribe featureValueDescribe = serviceFacade.findObject(user.getTenantId(),
                FeatureConstants.FEATURE_VALUE);
        for (Map<String, Object> map : ruleContent) {
            ISearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            RuleContentModel rule = JSON.parseObject(JSON.toJSONString(map), RuleContentModel.class);
            BigDecimal ruleScore = rule.getScore();
            List<IFilter> filters = rule.getFilters();
            searchTemplateQuery.setFilters(filters);
            ObjectDataFilter objectDataFilter = ObjectDataFilter.builder()
                    .describeExt(ObjectDescribeExt.of(featureValueDescribe))
                    .queryExt(SearchTemplateQueryExt.of(searchTemplateQuery)).filterLabel("特征值过滤").build();
            List<IObjectData> objectData = objectDataFilter.doFilter(Lists.newArrayList(featureValue));
            if (CollectionUtils.isNotEmpty(objectData)) {
                score = ruleScore;
                break;
            }
        }
        processFeatureScore(user, featureValue, score, methodologyId, addScoreList, updateScoreList, addScoreHistoryList);
    }

    private void llmCalc(User user, IObjectData scoringRuleData, IObjectData featureValue,
                         String methodologyId, List<IObjectData> addScoreList,
                         List<IObjectData> updateScoreList, List<IObjectData> addScoreHistoryList) {
        // LLM
        BigDecimal score = scoringRuleData.get(ScoringRuleConstants.DEFAULT_VALUE, BigDecimal.class, BigDecimal.ZERO);
        Map<String, Object> ruleMap = JSON.parseObject(scoringRuleData.get(ScoringRuleConstants.RULE_CONTENT, String.class), new TypeReference<Map<String, Object>>() {});
        if (ruleMap == null) {
            log.warn("ruleContent is null, tenantId:{}", user.getTenantId());
            return;
        }
        RuleContentModel ruleContent = JSON.parseObject(JSON.toJSONString(ruleMap), RuleContentModel.class);
        String promptApiName = ruleContent.getPromptApiName();
        String fieldName = ruleContent.getFieldName();
        if (StringUtils.isAnyBlank(promptApiName, fieldName)) {
            log.warn("promptApiName or fieldName is null, tenantId:{}", user.getTenantId());
            return;
        }
        StopWatch stopWatch = StopWatch.create("feature score llmCalc");
        AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
                .apiName(promptApiName)
                .sceneVariables(Maps.of("featureValue", featureValue.get(fieldName)))
                .build();
        FeatureScoreModel featureScoreModel = completionsService.requestCompletionData(user, arg, StringUtils.EMPTY,
                FeatureScoreModel.class);
        stopWatch.lap("requestCompletionData");
        stopWatch.logSlow(1000);
        if (featureScoreModel != null && featureScoreModel.getScore() != null) {
            score = featureScoreModel.getScore();
        }
        processFeatureScore(user, featureValue, score, methodologyId, addScoreList, updateScoreList, addScoreHistoryList);
    }

    private void processFeatureScore(User user, IObjectData featureValue, BigDecimal score,
                                     String methodologyId, List<IObjectData> addScoreList,
                                     List<IObjectData> updateScoreList, List<IObjectData> addScoreHistoryList) {
        SearchTemplateQueryPlus featureScoreSearchQuery = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(featureScoreSearchQuery).addFilter(Operator.EQ, FeatureScoreConstants.FEATURE_ID,
                featureValue.get(FeatureValueConstants.FEATURE_ID, String.class));
        SearchTemplateQueryExt.of(featureScoreSearchQuery).addFilter(Operator.EQ, FeatureScoreConstants.OBJECT_API_NAME,
                featureValue.get(FeatureValueConstants.OBJECT_API_NAME, String.class));
        SearchTemplateQueryExt.of(featureScoreSearchQuery).addFilter(Operator.EQ, FeatureScoreConstants.OBJECT_ID,
                featureValue.get(FeatureValueConstants.OBJECT_ID, String.class));
        if (StringUtils.isNotEmpty(methodologyId)) {
            SearchTemplateQueryExt.of(featureScoreSearchQuery).addFilter(Operator.EQ, FeatureScoreConstants.METHODOLOGY_ID,
                    methodologyId);
        } else {
            SearchTemplateQueryExt.of(featureScoreSearchQuery).addFilter(Operator.IS, FeatureScoreConstants.METHODOLOGY_ID,
                    Lists.newArrayList());
        }
        featureScoreSearchQuery.setOrders(Lists.newArrayList(SearchUtil.orderByLastModifiedTime()));
        List<IObjectData> oldFeatureScoreList = serviceFacade
                .findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE_SCORE, featureScoreSearchQuery).getData();
        if (CollectionUtils.isEmpty(oldFeatureScoreList)) {
            addFeatureScore(user, featureValue, score, methodologyId, addScoreList);
        } else {
            IObjectData oldFeatureScore = oldFeatureScoreList.get(0);
            if (oldFeatureScore.get(FeatureScoreConstants.SCORE, BigDecimal.class).compareTo(score) == 0) {
                return;
            }
            saveFeatureScoreHistory(user, oldFeatureScore, addScoreHistoryList);
            oldFeatureScore.set(FeatureScoreConstants.FEATURE_VALUE_ID, featureValue.getId());
            oldFeatureScore.set(FeatureScoreConstants.SCORE, score);
            oldFeatureScore.set(FeatureScoreConstants.CALC_TIME, System.currentTimeMillis());
            updateScoreList.add(oldFeatureScore);
        }
    }

    private void addFeatureScore(User user, IObjectData featureValue, BigDecimal score,
                                 String methodologyId, List<IObjectData> addScoreList) {
        IObjectData objectData = new ObjectData();
        objectData.set(FeatureScoreConstants.FEATURE_ID,
                featureValue.get(FeatureValueConstants.FEATURE_ID, String.class));
        objectData.set(FeatureScoreConstants.FEATURE_VALUE_ID, featureValue.getId());
        objectData.set(FeatureScoreConstants.OBJECT_API_NAME,
                featureValue.get(FeatureValueConstants.OBJECT_API_NAME, String.class));
        objectData.set(FeatureScoreConstants.OBJECT_ID,
                featureValue.get(FeatureValueConstants.OBJECT_ID, String.class));
        objectData.set(FeatureScoreConstants.SCORE, score);
        objectData.set(FeatureScoreConstants.CALC_TIME, System.currentTimeMillis());
        if (StringUtils.isNotEmpty(methodologyId)) {
            objectData.set(FeatureScoreConstants.METHODOLOGY_ID, methodologyId);
        }
        objectData.setTenantId(user.getTenantId());
        objectData.setDescribeApiName(FeatureConstants.FEATURE_SCORE);
        objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        objectData.setOwner(Lists.newArrayList(user.getUserId()));
        objectData.setName(FeatureBaseDataUtils.generateName());
        addScoreList.add(objectData);
    }

    private void saveFeatureScoreHistory(User user, IObjectData oldFeatureScore, List<IObjectData> addScoreHistoryList) {
        IObjectData objectData = new ObjectData();
        objectData.set(FeatureScoreHistoryConstants.SCORE_ID, oldFeatureScore.getId());
        objectData.set(FeatureScoreHistoryConstants.SCORE,
                oldFeatureScore.get(FeatureScoreConstants.SCORE, BigDecimal.class));
        objectData.set(FeatureScoreHistoryConstants.CALC_TIME,
                oldFeatureScore.get(FeatureScoreConstants.CALC_TIME, String.class));
        objectData.setTenantId(user.getTenantId());
        objectData.setDescribeApiName(FeatureConstants.FEATURE_SCORE_HISTORY);
        objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        objectData.setOwner(Lists.newArrayList(user.getUserId()));
        objectData.setName(FeatureBaseDataUtils.generateName());
        addScoreHistoryList.add(objectData);
    }
}
