package com.facishare.crm.task.sfa.model;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by Sundy on 2024/9/30 15:02
 */
public interface ObjectGray {
    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ObjectGrayResponse {
        @Builder.Default
        private List<String> failedTenants = Lists.newArrayList();
    }

    @Data
    class ObjectGrayArg {
        private List<String> tenants;
    }
}
