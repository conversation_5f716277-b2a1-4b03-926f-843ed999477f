package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 段落标签处理结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagProcessingResult {
    /**
     * 段落内容分片
     */
    private  Map<String, String> contentChunks;
    
    /**
     * 标签结果,  key为段落id，value为标签id列表
     */
    private Map<String, List<ParagraphTagResult.TagResult>> tagResults;
}