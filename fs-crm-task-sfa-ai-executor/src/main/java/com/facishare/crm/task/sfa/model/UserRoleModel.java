package com.facishare.crm.task.sfa.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface UserRoleModel {
    @Data
    @Builder
    class UserRoleListWithContextArg {
        private PAASContext authContext;
        private List<String> roles;
    }

    @Data
    @Builder
    class UserRoleUsersWithContextArg {
        private PAASContext authContext;
        private List<String> users;
    }

    @Data
    class UserRoleMapResult {
        private int errCode;
        private String errMessage;
        private Map<String, List<String>> result;
        private boolean success;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class UserRoleChangedMessage {
        private String appId;
        private String tenantId;
        private List<String> roles;
        private List<String> users;
        private int type;
    }

    interface UserRoleChangedType {
        int DELETE_LINK = 1;  //删除角色成员
        int ADD_LINK = 2;    //增加角色成员
        int UPDATE_LINK = 3; //更新角色成员
        int DELETE_ROLE = 4; //删除角色组
        int CLEAR_TENANT = 5; //
        int ACCURATE_UPDATE_LINK = 6; //精确人员角色关系变更
    }

}
