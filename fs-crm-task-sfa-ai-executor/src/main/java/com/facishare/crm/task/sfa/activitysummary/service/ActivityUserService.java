package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2025/4/29 20:32
 * @description:
 */
@Component
public class ActivityUserService {

    @Resource
    private ServiceFacade serviceFacade;

    public static final String ACTIVITY_USER_API_NAME = "ActivityUserObj";



    public List<IObjectData> getActivityUsers(String tenantId, String activeRecordId) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, "active_record_id", activeRecordId);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setOffset(0);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ACTIVITY_USER_API_NAME, searchTemplateQuery);
        if (ObjectUtils.isEmpty(queryResult) || ObjectUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData();
    }


}
