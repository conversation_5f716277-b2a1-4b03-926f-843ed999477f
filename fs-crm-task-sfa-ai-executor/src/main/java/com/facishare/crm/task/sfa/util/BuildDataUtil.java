package com.facishare.crm.task.sfa.util;

import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.crm.task.sfa.bizfeature.service.MethodologyNodeService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.IdUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class BuildDataUtil {

    private static MethodologyNodeService nodeService = SpringUtil.getContext().getBean(MethodologyNodeService.class);
    ;

    /**
     * 构建节点实例的参数对象
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NodeInstanceBuildParam {
        private List<IObjectData> workFlowNodeList;
        private String objectApiName;
        private String objectId;
        private IObjectData matchMethodology;
        private IObjectData methodologyInstance;
        private User user;
        private IObjectData objectData;
    }

    /**
     * 创建方法论实例
     */
    public static IObjectData buildMethodologyInstance(IObjectData matchMethodology, String objectApiName,
                                                       String objectId, User user) {
        IObjectData objectData = new ObjectData();
        objectData.setId(IdUtil.generateId());
        objectData.setTenantId(user.getTenantId());
        objectData.setDescribeApiName(MethodologyInstanceConstants.API_NAME);
        objectData.set(MethodologyInstanceConstants.METHODOLOGY_ID, matchMethodology.getId());
        objectData.set(MethodologyInstanceConstants.STATUS, "1");
        objectData.setOwner(Lists.newArrayList(user.getUserId()));
        objectData.set(MethodologyInstanceConstants.STAGE_LEVEL,
                matchMethodology.get(MethodologyInstanceConstants.STAGE_LEVEL));
        objectData.setName(FeatureBaseDataUtils.generateName());
        String objectField = MethodologyInstanceConstants.OBJECT_FIELD_MAP.get(objectApiName);
        if (StringUtils.isNotBlank(objectField)) {
            objectData.set(objectField, objectId);
        }
        objectData.set(MethodologyInstanceConstants.TYPE, matchMethodology.get(MethodologyConstants.TYPE, String.class, ""));
        objectData.set(MethodologyInstanceConstants.METHODOLOGY_IDS, matchMethodology.get(MethodologyConstants.METHODOLOGY_IDS, List.class, Lists.newArrayList()));

        try {
            // TODO 暂时为了看多个实例是哪个ip生成的
            InetAddress ip = InetAddress.getLocalHost();
            objectData.set(MethodologyInstanceConstants.CONTACT_ID, Lists.newArrayList(ip.getHostAddress()));
        } catch (UnknownHostException e) {

        }
//        String objectFieldListApiNameString = MethodologyInstanceConstants.OBJECT_FIELD_MAP_LIST.get(objectApiName);
//        if (StringUtils.isNotBlank(objectFieldListApiNameString)) {
//            objectData.set(objectFieldListApiNameString, Lists.newArrayList(objectId));
//        }
        return objectData;
    }

    /**
     * 创建节点实例
     *
     * @param param 构建节点实例的参数对象
     */
    public static List<IObjectData> buildNodeInstance(NodeInstanceBuildParam param) {
        List<IObjectData> nodeInstanceList = Lists.newArrayList();
        for (IObjectData workflowNode : param.getWorkFlowNodeList()) {
            IObjectData nodeInstance = createNodeInstance(workflowNode, param);
            nodeInstanceList.add(nodeInstance);
        }
        return nodeInstanceList;
    }

    /**
     * 创建单个节点实例
     */
    private static IObjectData createNodeInstance(IObjectData workflowNode, NodeInstanceBuildParam param) {
        // 创建节点实例对象
        IObjectData nodeInstance = new ObjectData();
        String nodeInstanceId = IdUtil.generateId();
        nodeInstance.setId(nodeInstanceId);
        nodeInstance.setTenantId(param.getMethodologyInstance().getTenantId());
        nodeInstance.setDescribeApiName(NodeInstanceConstants.API_NAME);
        // 设置方法论ID
        nodeInstance.set(NodeInstanceConstants.METHODOLOGY_ID, param.getMatchMethodology().getId());
        // 设置方法论实例ID
        nodeInstance.set(NodeInstanceConstants.METHODOLOGY_INSTANCE_ID, param.getMethodologyInstance().getId());
        // 设置关联对象信息
        nodeInstance.set(NodeInstanceConstants.OBJECT_API_NAME, param.getObjectApiName());
        nodeInstance.set(NodeInstanceConstants.OBJECT_ID, param.getObjectId());
        // 设置节点ID
        nodeInstance.set(NodeInstanceConstants.NODE_ID, workflowNode.getId());

        // 判断节点是否可以启动
        String nodeStatus = determineNodeStatus(workflowNode, param);
        nodeInstance.set(NodeInstanceConstants.STATUS, nodeStatus);

        nodeInstance.set(MethodologyNodeConstants.ROOT_ID, workflowNode.get(MethodologyNodeConstants.ROOT_ID));
        nodeInstance.set(MethodologyNodeConstants.PARENT_ID, workflowNode.get(MethodologyNodeConstants.PARENT_ID));
        nodeInstance.set(MethodologyNodeConstants.TREE_PATH, workflowNode.get(MethodologyNodeConstants.TREE_PATH));
        nodeInstance.set(MethodologyNodeConstants.LEVEL, workflowNode.get(MethodologyNodeConstants.LEVEL));
        nodeInstance.set(MethodologyNodeConstants.NODE_ORDER, workflowNode.get(MethodologyNodeConstants.NODE_ORDER));
        nodeInstance.setName(FeatureBaseDataUtils.generateName());
        nodeInstance.setOwner(Lists.newArrayList(param.getUser().getUserId()));

        return nodeInstance;
    }

    /**
     * 确定节点状态
     * 使用MethodologyNodeService的逻辑判断节点是否可以启动
     */
    private static String determineNodeStatus(IObjectData workflowNode, NodeInstanceBuildParam param) {
        try {
            // 查询对象流程字典，判断当前节点是否匹配对象的当前状态
            String matchedNodeId = findMatchedNodeId(param.getObjectData(), param.getObjectApiName(),
                    param.getMatchMethodology().getId(), param.getUser());

            if (StringUtils.isNotBlank(matchedNodeId) && matchedNodeId.equals(workflowNode.getId())) {
                log.info("节点{}匹配当前对象状态，设置为进行中", workflowNode.getId());
                return NodeInstanceConstants.StatusType.PROGRESS.getStatusType();
            } else {
                log.info("节点{}不匹配当前对象状态，设置为未启动", workflowNode.getId());
                return NodeInstanceConstants.StatusType.NOT_STARTED.getStatusType();
            }
        } catch (Exception e) {
            log.error("判断节点状态异常，节点ID: {}", workflowNode.getId(), e);
            return NodeInstanceConstants.StatusType.NOT_STARTED.getStatusType();
        }
    }

    /**
     * 查找匹配的节点ID
     * 参考MethodologyNodeService的findMatchedNodeId逻辑
     */
    private static String findMatchedNodeId(IObjectData objectData, String objectApiName,
                                            String methodologyId, User user) {
        try {
            // 查询对象流程字典
            List<IObjectData> objectWorkflows = queryObjectWorkflows(user, objectApiName, methodologyId);

            if (CollectionUtils.isEmpty(objectWorkflows)) {
                log.warn("未找到对象流程字典，objectApiName: {}, methodologyId: {}", objectApiName, methodologyId);
                return null;
            }

            return nodeService.findMatchedNodeId(objectData, objectWorkflows);
        } catch (Exception e) {
            log.error("查找匹配节点ID异常", e);
        }
        return null;
    }

    /**
     * 查询对象流程字典
     */
    private static List<IObjectData> queryObjectWorkflows(User user, String objectApiName, String methodologyId) {
        try {
            return nodeService.batchQueryObjectWorkflows(user, objectApiName, Lists.newArrayList(methodologyId));
        } catch (Exception e) {
            log.error("查询对象流程字典异常", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 安全获取字符串值
     */
    private static String getStringValue(IObjectData data, String fieldName) {
        return data.get(fieldName, String.class);
    }

    /**
     * 创建任务实例
     *
     * @param matchMethodology    方法论对象
     * @param methodologyInstance 方法论实例
     * @param nodeInstanceList    节点实例列表
     * @param nodeTaskList        节点任务关系
     * @param user                用户信息
     * @param objectApiName       对象API名称
     * @param objectId            对象ID
     * @return 任务实例列表
     */
    public static List<IObjectData> buildTaskInstance(IObjectData matchMethodology, IObjectData methodologyInstance,
                                                      List<IObjectData> nodeInstanceList, List<IObjectData> nodeTaskList, User user, String objectApiName,
                                                      String objectId) {
        if (CollectionUtils.isEmpty(nodeTaskList) || CollectionUtils.isEmpty(nodeInstanceList)) {
            log.warn("nodeTaskList or nodeInstanceList is empty");
            return Lists.newArrayList();
        }

        List<IObjectData> taskInstanceList = Lists.newArrayList();

        // 按节点ID分组节点实例
        Map<String, List<IObjectData>> nodeInstanceMap = nodeInstanceList.stream()
                .collect(Collectors.groupingBy(node -> node.get(NodeInstanceConstants.NODE_ID).toString()));

        // 遍历任务列表，创建任务实例
        for (IObjectData nodeTask : nodeTaskList) {
            String nodeId = nodeTask.get(TaskInstanceConstants.NODE_ID).toString();
            List<IObjectData> matchedNodeInstances = nodeInstanceMap.get(nodeId);

            if (CollectionUtils.isEmpty(matchedNodeInstances)) {
                log.warn("No matched node instance found for node: {}", nodeId);
                continue;
            }

            // 为每个匹配的节点实例创建任务实例
            for (IObjectData nodeInstance : matchedNodeInstances) {
                IObjectData taskInstance = createTaskInstance(nodeTask, nodeInstance, matchMethodology,
                        methodologyInstance, objectApiName, objectId, user);
                taskInstanceList.add(taskInstance);
            }
        }

        return taskInstanceList;
    }

    /**
     * 创建单个任务实例
     */
    private static IObjectData createTaskInstance(IObjectData nodeTask, IObjectData nodeInstance,
                                                  IObjectData matchMethodology, IObjectData methodologyInstance, String objectApiName, String objectId,
                                                  User user) {
        // 创建任务实例对象
        IObjectData taskInstance = new ObjectData();
        String taskInstanceId = IdUtil.generateId();
        taskInstance.setId(taskInstanceId);
        taskInstance.setTenantId(user.getTenantId());
        taskInstance.setDescribeApiName(TaskInstanceConstants.API_NAME);

        // 设置方法论ID
        taskInstance.set(TaskInstanceConstants.METHODOLOGY_ID, matchMethodology.getId());

        // 设置方法论实例ID
        taskInstance.set(TaskInstanceConstants.METHODOLOGY_INSTANCE_ID, methodologyInstance.getId());

        // 设置任务ID（从workflowTask中获取）
        taskInstance.set(TaskInstanceConstants.TASK_ID, nodeTask.get(NodeTaskConstants.TASK_ID, String.class));

        // 设置节点ID（从workflowTask中获取node_id）
        taskInstance.set(TaskInstanceConstants.NODE_ID, nodeTask.get(NodeTaskConstants.NODE_ID, String.class));

        // 设置节点实例ID（从nodeInstance中获取ID）
        taskInstance.set(TaskInstanceConstants.NODE_INSTANCE_ID, nodeInstance.getId());

        // 设置关联对象信息
        taskInstance.set(TaskInstanceConstants.OBJECT_API_NAME, objectApiName);
        taskInstance.set(TaskInstanceConstants.OBJECT_ID, objectId);

        // 设置状态为未启动（0）
        taskInstance.set(TaskInstanceConstants.STATUS, TaskInstanceConstants.StatusType.NOT_STARTED.getStatusType());
        taskInstance.setName(FeatureBaseDataUtils.generateName());

        taskInstance.setOwner(Lists.newArrayList(user.getUserId()));
        return taskInstance;
    }

    /**
     * 创建实例特征关联
     *
     * @param taskFeatureList     任务特征关联列表
     * @param taskInstanceList    任务实例列表
     * @param methodologyInstance 方法论实例
     * @param user                用户信息
     * @return 实例特征关联列表
     */
    public static List<IObjectData> buildInstanceFeature(List<IObjectData> taskFeatureList,
                                                         List<IObjectData> taskInstanceList,
                                                         IObjectData methodologyInstance,
                                                         User user) {
        if (CollectionUtils.isEmpty(taskFeatureList) || CollectionUtils.isEmpty(taskInstanceList)) {
            log.warn("taskFeatureList or taskInstanceList is empty");
            return Lists.newArrayList();
        }

        List<IObjectData> instanceFeatureList = Lists.newArrayList();

        // 创建任务ID到任务实例的映射
        Map<String, List<IObjectData>> taskIdToInstanceMap = taskInstanceList.stream()
                .collect(Collectors.groupingBy(taskInstance ->
                        taskInstance.get(TaskInstanceConstants.TASK_ID, String.class)));

        // 为每个任务特征关联创建实例特征关联
        for (IObjectData taskFeature : taskFeatureList) {
            String taskId = taskFeature.get(TaskFeatureConstants.TASK_ID, String.class);
            List<IObjectData> relatedTaskInstances = taskIdToInstanceMap.get(taskId);

            if (!CollectionUtils.isEmpty(relatedTaskInstances)) {
                for (IObjectData taskInstance : relatedTaskInstances) {
                    IObjectData instanceFeature = createInstanceFeature(taskFeature, taskInstance,
                            methodologyInstance, user);
                    instanceFeatureList.add(instanceFeature);
                }
            }
        }

        return instanceFeatureList;
    }

    /**
     * 创建单个实例特征关联
     */
    private static IObjectData createInstanceFeature(IObjectData taskFeature, IObjectData taskInstance,
                                                     IObjectData methodologyInstance,
                                                     User user) {
        IObjectData instanceFeature = new ObjectData();
        instanceFeature.setId(IdUtil.generateId());
        instanceFeature.setTenantId(user.getTenantId());
        instanceFeature.setDescribeApiName(FeatureConstants.INSTANCE_FEATURE);

        // 设置基本关联信息
        instanceFeature.set(InstanceFeatureConstants.TASK_ID, taskFeature.get(TaskFeatureConstants.TASK_ID, String.class));
        instanceFeature.set(InstanceFeatureConstants.TASK_INSTANCE_ID, taskInstance.getId());
        instanceFeature.set(InstanceFeatureConstants.NODE_INSTANCE_ID, taskInstance.get(TaskInstanceConstants.NODE_INSTANCE_ID, String.class));
        instanceFeature.set(InstanceFeatureConstants.NODE_ID, taskInstance.get(TaskInstanceConstants.NODE_ID, String.class));
        instanceFeature.set(InstanceFeatureConstants.METHODOLOGY_ID, methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_ID, String.class));
        instanceFeature.set(InstanceFeatureConstants.METHODOLOGY_INSTANCE_ID, methodologyInstance.getId());
        instanceFeature.set(InstanceFeatureConstants.FEATURE_ID, taskFeature.get(TaskFeatureConstants.FEATURE_ID, String.class));

        // 设置对象信息
        instanceFeature.set(InstanceFeatureConstants.RELATED_OBJECT_API_NAME, taskFeature.get(TaskFeatureConstants.RELATED_OBJECT_API_NAME));
        instanceFeature.set(InstanceFeatureConstants.RELATED_FIELD, taskFeature.get(TaskFeatureConstants.RELATED_FIELD));
        instanceFeature.set(InstanceFeatureConstants.FEATURE_OBJECT_API_NAME, taskFeature.get(TaskFeatureConstants.FEATURE_OBJECT_API_NAME));

        instanceFeature.setOwner(Lists.newArrayList(user.getUserId()));
        instanceFeature.setName(FeatureBaseDataUtils.generateName());

        return instanceFeature;
    }

    /**
     * 根据NodeFeatureObj创建实例特征关联（用于画像类型方法论）
     *
     * @param nodeFeatureList     节点特征关联列表
     * @param nodeInstanceList    节点实例列表
     * @param methodologyInstance 方法论实例
     * @param user                用户信息
     * @return 实例特征关联列表
     */
    public static List<IObjectData> buildInstanceFeatureFromNodeFeature(List<IObjectData> nodeFeatureList,
                                                                        List<IObjectData> nodeInstanceList,
                                                                        IObjectData methodologyInstance,
                                                                        User user) {
        if (CollectionUtils.isEmpty(nodeFeatureList) || CollectionUtils.isEmpty(nodeInstanceList)) {
            log.warn("nodeFeatureList or nodeInstanceList is empty");
            return Lists.newArrayList();
        }

        List<IObjectData> instanceFeatureList = Lists.newArrayList();

        // 创建节点ID到节点实例的映射
        Map<String, List<IObjectData>> nodeIdToInstanceMap = nodeInstanceList.stream()
                .collect(Collectors.groupingBy(nodeInstance ->
                        nodeInstance.get(NodeInstanceConstants.NODE_ID, String.class)));

        // 为每个节点特征关联创建实例特征关联
        for (IObjectData nodeFeature : nodeFeatureList) {
            String nodeId = nodeFeature.get(NodeFeatureConstants.NODE_ID, String.class);
            List<IObjectData> relatedNodeInstances = nodeIdToInstanceMap.get(nodeId);

            if (!CollectionUtils.isEmpty(relatedNodeInstances)) {
                for (IObjectData nodeInstance : relatedNodeInstances) {
                    IObjectData instanceFeature = createInstanceFeatureFromNodeFeature(nodeFeature,
                            nodeInstance, methodologyInstance, user);
                    instanceFeatureList.add(instanceFeature);
                }
            }
        }

        return instanceFeatureList;
    }

    /**
     * 根据NodeFeatureObj创建单个实例特征关联
     */
    private static IObjectData createInstanceFeatureFromNodeFeature(IObjectData nodeFeature, IObjectData nodeInstance,
                                                                    IObjectData methodologyInstance, User user) {
        IObjectData instanceFeature = new ObjectData();
        instanceFeature.setId(IdUtil.generateId());
        instanceFeature.setTenantId(user.getTenantId());
        instanceFeature.setDescribeApiName(FeatureConstants.INSTANCE_FEATURE);

        // 设置基本关联信息
        instanceFeature.set(InstanceFeatureConstants.NODE_INSTANCE_ID, nodeInstance.getId());
        instanceFeature.set(InstanceFeatureConstants.NODE_ID, nodeFeature.get(NodeFeatureConstants.NODE_ID, String.class));
        instanceFeature.set(InstanceFeatureConstants.METHODOLOGY_ID, methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_ID, String.class));
        instanceFeature.set(InstanceFeatureConstants.METHODOLOGY_INSTANCE_ID, methodologyInstance.getId());
        instanceFeature.set(InstanceFeatureConstants.FEATURE_ID, nodeFeature.get(NodeFeatureConstants.FEATURE_ID, String.class));

        // 设置对象信息
        instanceFeature.set(InstanceFeatureConstants.FEATURE_OBJECT_API_NAME, nodeFeature.get(NodeFeatureConstants.FEATURE_OBJECT_API_NAME, String.class));
        instanceFeature.set(InstanceFeatureConstants.RELATED_OBJECT_API_NAME, nodeFeature.get(NodeFeatureConstants.RELATED_OBJECT_API_NAME, String.class));
        instanceFeature.set(InstanceFeatureConstants.RELATED_FIELD, nodeFeature.get(NodeFeatureConstants.RELATED_FIELD, String.class));

        instanceFeature.setOwner(Lists.newArrayList(user.getUserId()));
        instanceFeature.setName(FeatureBaseDataUtils.generateName());

        return instanceFeature;
    }
}
