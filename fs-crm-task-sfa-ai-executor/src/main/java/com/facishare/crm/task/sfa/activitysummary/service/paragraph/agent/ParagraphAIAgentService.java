package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.OpenAIChatComplete;
import com.facishare.ai.api.model.Message;
import com.facishare.ai.api.model.service.Openai;
import com.facishare.crm.task.sfa.activitysummary.constants.AIAgentConstants;
import com.facishare.crm.task.sfa.activitysummary.model.EvaluationModel;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphResultModel;
import com.facishare.crm.task.sfa.activitysummary.model.PlanModel;
import com.facishare.crm.task.sfa.activitysummary.model.SegmentModel;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI Agent段落分割服务类
 * 使用AI Agent架构进行更准确的段落分割
 * <p>
 * Agent架构设计:
 * - Planner: 负责分析文本特征，制定分段策略
 * - Executor: 执行具体分段任务
 * - Evaluator: 评估分段结果质量
 * - Controller: 协调各组件工作
 * <p>
 * 工作流程:
 * 1. Planner分析文本，确定分段策略
 * 2. Executor执行分段
 * 3. Evaluator评估结果
 * 4. 如不满足质量要求，调整策略重新执行
 */
@Service
@Slf4j
public class ParagraphAIAgentService {

    @Autowired
    private PlannerService plannerService;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private EvaluatorService evaluatorService;

    @Autowired
    private AdjusterService adjusterService;

    @Autowired
    private ConverterService converterService;

    @Autowired
    private Openai openai;

    @Autowired
    private MultiRoundChatParagraphService multiRoundChatParagraphService;

    private String openai_model = "Alicloud@deepseek-v3";

    private boolean isMultiRoundChat = true;

    // 配置常量，从AIAgentConstants中获取
    private static final int MAX_RETRY_COUNT = AIAgentConstants.MAX_RETRY_COUNT; // 整体重试次数
    private static final int MAX_ADJUSTMENT_ROUNDS = AIAgentConstants.MAX_ADJUSTMENT_ROUNDS; // 最大调整轮数
    private static final double QUALITY_THRESHOLD = AIAgentConstants.QUALITY_THRESHOLD; // 质量评分阈值

    /**
     * 将分段中的ID替换为原始文本内容
     * 这样在传递给Evaluator时，它可以直接处理原始文本而不是ID
     *
     * @param segments    分段模型列表
     * @param idToContentMap 会议内容
     * @return 替换ID为原文的分段模型列表
     */
    private List<SegmentModel> replaceIdsWithOriginalContent(List<SegmentModel> segments,  Map<String, String> idToContentMap) {
        if (CollectionUtils.isEmpty(segments)) {
            return segments;
        }

        try {
            if (idToContentMap.isEmpty()) {
                log.warn("无法从会议内容中提取ID到原文的映射");
                return segments;
            }

            // 创建新的分段列表，替换ID为原文
            List<SegmentModel> newSegments = new ArrayList<>();
            for (SegmentModel segment : segments) {
                SegmentModel newSegment = new SegmentModel();
                newSegment.setSegmentId(segment.getSegmentId());
                newSegment.setSummary(segment.getSummary());
                newSegment.setConfidence(segment.getConfidence());

                // 替换内容ID为原文
                List<String> originalContents = new ArrayList<>();
                for (String contentId : segment.getContentIds()) {
                    String originalContent = idToContentMap.getOrDefault(contentId, contentId);
                    originalContents.add(originalContent);
                }
                newSegment.setContentIds(originalContents);

                newSegments.add(newSegment);
            }

            return newSegments;
        } catch (Exception e) {
            log.error("替换ID为原文时发生错误", e);
            return segments; // 出错时返回原始分段
        }
    }

    /**
     * 从会议内容中提取ID到原文的映射
     * 会议内容格式假设为: 【ID】内容\n【ID】内容\n...
     *
     * @param currentBatchIds 会议内容id
     * @param contentList     会议内容
     * @return ID到原文的映射
     */
    private Map<String, String> extractIdToContentMap(List<String> currentBatchIds, List<String> contentList) {
        Map<String, String> idToContentMap = new HashMap<>();
        for (int i = 0; i < currentBatchIds.size(); i++) {
            idToContentMap.put(currentBatchIds.get(i), contentList.get(i));
        }
        return idToContentMap;
    }


    /**
     * 处理内容分段，带重试机制
     *
     * @param user            用户信息
     * @param meetContent     会议内容
     * @param previousSummary 上一次摘要
     * @return 段落结果模型
     */
    public ParagraphResultModel requestWithRetry(User user, String meetContent, String previousSummary, List<String> currentBatchIds, List<String> contentList) {
        for (int retryCount = 0; retryCount < MAX_RETRY_COUNT; retryCount++) {
            ParagraphResultModel result = processContentSegmentationWithAgent(user, meetContent, previousSummary, currentBatchIds, contentList);
            if (result != null && CollectionUtils.isNotEmpty(result.getChunks())) {
                return result;
            }
            log.warn("AI Agent分段重试 {}/{}", retryCount + 1, MAX_RETRY_COUNT);
        }
        log.error("AI Agent分段重试{}次后失败", MAX_RETRY_COUNT);
        return null;
    }

    /**
     * 使用AI Agent处理内容分段
     *
     * @param user            用户信息
     * @param meetContent     会议内容
     * @param previousSummary 上一次摘要
     * @return 段落结果模型
     */
    public ParagraphResultModel processContentSegmentationWithAgent(User user, String meetContent, String previousSummary, List<String> currentBatchIds, List<String> contentList) {
        if(isMultiRoundChat){
            return processContentSegmentationWithMultiRoundChat(user, meetContent, previousSummary, currentBatchIds, contentList);
        }
        try {
            Map<String, String> idToContentMap = extractIdToContentMap(currentBatchIds, contentList);
            log.info("开始AI Agent分段流程，内容长度: {}, 有摘要: {}",
                    meetContent.length(), StringUtils.isNotEmpty(previousSummary));

            // 1. Planner: 分析文本，制定分段策略
            PlanModel plan = plannerService.runPlanner(user, meetContent, previousSummary);
            if (plan == null) {
                log.error("Planner执行失败，无法获取分段策略");
                return null;
            }
            log.info("Planner执行成功，分段策略: {}, 预期段落数: {}", plan.getStrategy(), plan.getExpectedSegments());

            // 2. Executor: 执行分段
            List<SegmentModel> segments = executorService.runExecutor(user, meetContent, previousSummary, plan);
            if (CollectionUtils.isEmpty(segments)) {
                log.error("Executor执行失败，无法获取分段结果");
                return null;
            }
            log.info("Executor执行成功，生成段落数: {}", segments.size());

            // 3. 将分段ID替换为原文，以便Evaluator能够正确处理
            List<SegmentModel> segmentsWithContent = replaceIdsWithOriginalContent(segments, idToContentMap);
            log.info("已将分段ID替换为原文，用于评估");

            // 3. Evaluator: 评估分段结果 - 使用包含原文的分段
            EvaluationModel evaluation = evaluatorService.runEvaluator(user, meetContent, previousSummary, segmentsWithContent, null);
            if (evaluation == null) {
                log.error("Evaluator执行失败，无法评估分段质量");
                return null;
            }
            log.info("Evaluator执行成功，总体评分: {}, 是否需要修正: {}",
                    evaluation.getOverallScore(), evaluation.isNeedsRevision());

            // 4. 如果评估结果不满足质量要求，进行多轮调整和评估
            int adjustmentRound = 0;

            // 记录最高得分及其对应的分段结果
            double highestScore = evaluation.getOverallScore();
            List<SegmentModel> bestSegments = segments;
            EvaluationModel bestEvaluation = evaluation;

            while (evaluation.isNeedsRevision() &&
                    evaluation.getOverallScore() < QUALITY_THRESHOLD &&
                    adjustmentRound < MAX_ADJUSTMENT_ROUNDS) {

                adjustmentRound++;
                log.info("分段质量不满足要求，开始第{}轮调整", adjustmentRound);

                // 根据评估结果调整计划
                plan = adjusterService.adjustPlanBasedOnEvaluation(plan, evaluation);

                // 重新执行分段 - 传入上一轮的评估结果作为参考
                List<SegmentModel> newSegments = executorService.runExecutor(user, meetContent, previousSummary, plan);
                if (CollectionUtils.isEmpty(newSegments)) {
                    log.error("第{}轮调整后执行分段失败，使用上一轮结果", adjustmentRound);
                    continue; // 继续下一轮调整
                }

                // 将新分段的ID替换为原文，用于评估
                List<SegmentModel> newSegmentsWithContent = replaceIdsWithOriginalContent(newSegments, idToContentMap);
                log.info("第{}轮调整后，已将分段ID替换为原文，用于评估", adjustmentRound);

                // 评估新的分段结果，传入上一轮的评估结果作为参考
                EvaluationModel newEvaluation = evaluatorService.runEvaluator(user, meetContent, previousSummary, newSegmentsWithContent, evaluation);
                if (newEvaluation == null) {
                    log.error("第{}轮调整后评估失败，继续下一轮", adjustmentRound);
                    continue; // 继续下一轮调整
                }

                log.info("第{}轮调整后评分: {} -> {}",
                        adjustmentRound, evaluation.getOverallScore(), newEvaluation.getOverallScore());

                // 更新当前评估结果，用于下一轮调整
                evaluation = newEvaluation;
                segments = newSegments;

                // 更新最高得分记录
                if (newEvaluation.getOverallScore() > highestScore) {
                    highestScore = newEvaluation.getOverallScore();
                    bestSegments = newSegments;
                    bestEvaluation = newEvaluation;
                    log.info("第{}轮调整产生了新的最高得分: {}", adjustmentRound, highestScore);
                }

                // 如果已经达到质量阈值，停止调整
                if (newEvaluation.getOverallScore() >= QUALITY_THRESHOLD) {
                    log.info("已达到质量阈值，停止调整");
                    break;
                }
            }

            // 如果没有达到质量阈值，使用最高得分的结果
            if (evaluation.getOverallScore() < QUALITY_THRESHOLD && bestEvaluation.getOverallScore() > evaluation.getOverallScore()) {
                segments = bestSegments;
                evaluation = bestEvaluation;
                log.info("未达到质量阈值，使用最高得分的结果，得分: {}", bestEvaluation.getOverallScore());
            }

            if (adjustmentRound > 0) {
                log.info("共进行了{}轮调整，最终评分: {}, 段落数: {}",
                        adjustmentRound, evaluation.getOverallScore(), segments.size());
            }

            // 5. 转换为ParagraphResultModel
            return converterService.convertToParagraphResultModel(segments, evaluation);

        } catch (Exception e) {
            log.error("AI Agent分段流程执行失败", e);
            return null;
        }
    }

    /**
     * 使用多轮对话方式处理内容分段
     *
     * @param user            用户信息
     * @param meetContent     会议内容
     * @param previousSummary 上一次摘要
     * @param currentBatchIds 当前批次ID列表
     * @param contentList     内容列表
     * @return 段落结果模型
     */
    public ParagraphResultModel processContentSegmentationWithMultiRoundChat(User user, String meetContent, String previousSummary, List<String> currentBatchIds, List<String> contentList) {
        return multiRoundChatParagraphService.processContentSegmentationWithMultiRoundChat(user, meetContent, previousSummary, currentBatchIds, contentList);
    }

    /**
     * 使用OpenAI进行对话
     *
     * @param tenantId 租户ID
     * @param prompt   系统提示词
     * @param text     用户输入
     * @return 大模型响应
     */
    @SuppressWarnings("deprecation") // 使用了已废弃的chatComplete方法
    public String summaryActivityByOpenAI(String tenantId, String prompt, String text) {
        BaseArgument baseArgument = new BaseArgument();
        baseArgument.setTenantId(tenantId);
        baseArgument.setBusiness("sfa_ai");
        OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
        arg.setModel(openai_model);
        arg.setMessages(getMessage(prompt, text));
        arg.setUser_id("-10000");
        arg.setStream(Boolean.FALSE);
        OpenAIChatComplete.Result result = null;
        try {
            result = openai.chatComplete(baseArgument, arg);
        } catch (Exception e) {
            log.error("summaryActivityByOpenAI error msg is {}", e.getMessage());
            return "";
        }
        log.info("result:{}", result);
        return result.getMessage();
    }

    /**
     * 构建消息列表
     *
     * @param prompt 系统提示词
     * @param text   用户输入
     * @return 消息列表
     */
    private List<Message> getMessage(String prompt, String text) {
        List<Message> messages = new ArrayList<>();
        Message message = new Message();
        message.setRole("system");
        message.setContent(prompt);
        messages.add(message);
        Message message2 = new Message();
        message2.setRole("user");
        message2.setContent(text);
        messages.add(message2);
        return messages;
    }
}