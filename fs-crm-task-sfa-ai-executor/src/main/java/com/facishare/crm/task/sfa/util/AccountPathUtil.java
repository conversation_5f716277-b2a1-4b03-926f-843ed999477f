package com.facishare.crm.task.sfa.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户路径公共方法 class
 *
 * <AUTHOR>
 * @date 2020/3/31
 */
@Slf4j
public class AccountPathUtil {
    private static final MetaDataFindService metaDataFindService = SpringUtil.getContext().getBean(MetaDataFindServiceImpl.class);
    private static final DescribeLogicService describeService = SpringUtil.getContext().getBean(DescribeLogicServiceImpl.class);
    private static final ObjectDataProxy dataProxy = SpringUtil.getContext().getBean(ObjectDataProxy.class);

    /**
     * 根据条件获取客户列表
     *
     * @param user
     * @param filters
     * @return
     */
    public static List<IObjectData> getAccountListWithDeleted(User user, IObjectDescribe objectDescribe, List<IFilter> filters) {
        if (CollectionUtils.empty(filters)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setPermissionType(0);

        IActionContext actionContext = ActionContextExt.of(user)
                .disableDeepQuote()
                .skipRelevantTeam()
                .getContext();
        actionContext.setDoCalculate(false);

        QueryResult<IObjectData> accountList = metaDataFindService.findBySearchQueryWithDeleted(actionContext, objectDescribe,
                searchTemplateQuery);
        if (CollectionUtils.empty(accountList.getData())) {
            return Lists.newArrayList();
        }
        return accountList.getData();
    }

    /**
     * 根据条件获取客户列表
     *
     * @param user
     * @param filters
     * @return
     */
    public static List<IObjectData> getObjectListWithDeleted(User user, IObjectDescribe objectDescribe, List<IFilter> filters) {
        if (CollectionUtils.empty(filters)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        IActionContext actionContext = buildContext(user);
        QueryResult<IObjectData> accountList = metaDataFindService.findBySearchQueryWithDeleted(actionContext, objectDescribe,
                searchTemplateQuery);
        if (CollectionUtils.empty(accountList.getData())) {
            return Lists.newArrayList();
        }
        return accountList.getData();
    }

    /**
     * 根据路径获取客户列表，包含作废删除的
     *
     * @param user
     * @param currentAccountIds
     * @return
     */
    public static List<IObjectData> getAccountListWithDeleted(User user, IObjectDescribe objectDescribe, Set<String> currentAccountIds) {
        if (CollectionUtils.empty(currentAccountIds)) {
            return Lists.newArrayList();
        }
        if (objectDescribe == null) {
            return Lists.newArrayList();
        }
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH);
        if (fieldDescribe == null || !fieldDescribe.isActive()) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        String matchValue = String.format("*.%s.*", Joiner.on("|").join(currentAccountIds));
        SearchUtil.fillFilterMatch(filters, CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, matchValue);
        return getAccountListWithDeleted(user, objectDescribe, filters);
    }

    /**
     * 根据id获取客户列表，包含作废删除的
     *
     * @param user
     * @param currentAccountIds
     * @return
     */
    public static List<IObjectData> getAccountListByIdWithDeleted(User user, IObjectDescribe objectDescribe,
                                                                  List<String> currentAccountIds) {
        if (CollectionUtils.empty(currentAccountIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        if (currentAccountIds.size() > 1) {
            SearchUtil.fillFilterIn(filters, IObjectData.ID, currentAccountIds);
        } else {
            SearchUtil.fillFilterEq(filters, IObjectData.ID, currentAccountIds);
        }
        return getAccountListWithDeleted(user, objectDescribe, filters);
    }

    @NotNull
    private static SearchTemplateQuery getSearchTemplateQuery(List<IFilter> filters) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(2000);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    public static IActionContext buildContext(User user) {
        return ActionContextExt.of(user, RequestContextManager.getContext())
                .set("skip_relevantTeam", true)
                .getContext();
    }

    public static IActionContext buildContext(User user, boolean allowUpdateInvalid) {
        return ActionContextExt.of(user, RequestContextManager.getContext())
                .allowUpdateInvalid(allowUpdateInvalid)
                .setNotValidate(true)
                .getContext();
    }

    /**
     * 组装数据并校验
     *
     * @param user
     * @param objectDataList
     * @param needChangeData
     */
    public static void changeAccountPath(User user, IObjectDescribe accountDescribe, List<IObjectData> objectDataList,
                                         Map<String, IObjectData> needChangeData) {
        StopWatch stopWatch = StopWatch.create("changeAccountPath");
        if (CollectionUtils.empty(objectDataList)) {
            log.info("AccountPathChange objectDataList is null");
            return;
        }

        Set<String> parentAccountIds = Sets.newHashSet();
        parentAccountIds.addAll(objectDataList.stream()
                .filter(n -> !Strings.isNullOrEmpty(n.get(CommonConstant.ACCOUNT_FIELD_PARENT_ACCOUNT_ID, String.class)))
                .map(m -> m.get(CommonConstant.ACCOUNT_FIELD_PARENT_ACCOUNT_ID, String.class))
                .collect(Collectors.toSet()));

        Set<String> accountIds = objectDataList.stream().map(m -> m.getId()).collect(Collectors.toSet());
        parentAccountIds.addAll(accountIds);
        stopWatch.lap("findParentAccountIds");

        List<IObjectData> allDataList = Lists.newArrayList();

        //获取所有可能受影响的数据
        allDataList.addAll(AccountPathUtil.getAccountListWithDeleted(user, accountDescribe, parentAccountIds));
        stopWatch.lap("findAllDataList");

        //先移除后添加保证数据是全面的
        allDataList.removeIf(m -> accountIds.contains(m.getId()));
        allDataList.addAll(objectDataList);

        Set<String> allAccountIds = allDataList.stream().map(m -> m.getId()).collect(Collectors.toSet());
        parentAccountIds.removeAll(allAccountIds);
        if (CollectionUtils.notEmpty(parentAccountIds)) {
            List<IObjectData> accountRootList = AccountPathUtil.getAccountListByIdWithDeleted(user, accountDescribe,
                    Lists.newArrayList(parentAccountIds));
            stopWatch.lap("findAccountRootList");
            if (CollectionUtils.notEmpty(accountRootList)) {
                allDataList.addAll(accountRootList);
            }
        }
        if (CollectionUtils.empty(allDataList)) {
            log.info("AccountPathChange allDataList is null {}", accountIds);
            return;
        }

        //获取在当前数据列表中算作根节点的数据
        List<IObjectData> rootAccountList = getRootObjectData(objectDataList);
        stopWatch.lap("findRootAccountList");

        if (CollectionUtils.empty(rootAccountList)) {
            log.info("AccountPathChange rootAccountList is null {}", accountIds);
            return;
        }

        //处理根节点
        changeRootData(user, accountDescribe, needChangeData, allDataList, rootAccountList);
        stopWatch.lap("changeRootAccountList");

        stopWatch.logSlow(3000);
    }

    /**
     * 处理提供数据的根节点
     *
     * @param needChangeData
     * @param allDataList
     * @param rootAccountList
     */
    private static void changeRootData(User user, IObjectDescribe accountDescribe, Map<String, IObjectData> needChangeData,
                                       List<IObjectData> allDataList, List<IObjectData> rootAccountList) {
        Iterator<IObjectData> rootIt = rootAccountList.iterator();
        while (rootIt.hasNext()) {
            IObjectData child = rootIt.next();
            String oldAccountPath = child.get(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, String.class);
            if (Strings.isNullOrEmpty(child.get(CommonConstant.ACCOUNT_FIELD_PARENT_ACCOUNT_ID, String.class)) ||
                    "invalid".equals(child.get("life_status", String.class)) || child.isDeleted()) {
                String newAccountPath = child.getId();
                if (!Strings.isNullOrEmpty(oldAccountPath) && !Objects.equals(oldAccountPath, newAccountPath)) {
                    child.set(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, newAccountPath);
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, 0);
                continue;
            }
            Optional<IObjectData> parentAccountCurrentOptional = allDataList.stream()
                    .filter(n -> n.getId().equals(child.get(CommonConstant.ACCOUNT_FIELD_PARENT_ACCOUNT_ID, String.class)))
                    .findAny();
            parentAccountCurrentOptional.ifPresent(parent -> {
                String parentAccountPath = getOldAccountPath(parent);

                if (parentAccountPath.contains(child.getId())) {
                    allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    log.info("AccountPathChange childisfather {}", child.getId());
                    return;
                }

                String newAccountPath = parentAccountPath + child.getId();
                if (newAccountPath.split("\\.").length > 10) {
                    allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    log.info("AccountPathChange levelcannotexceedten {}", child.getId());
                    return;
                }
                if (!Objects.equals(oldAccountPath, newAccountPath)) {
                    child.set(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, newAccountPath);
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, 0);
                return;
            });
            if (!parentAccountCurrentOptional.isPresent()) {
                List<IObjectData> parentObect = AccountPathUtil.getAccountListByIdWithDeleted(user, accountDescribe,
                        Lists.newArrayList(child.get(CommonConstant.ACCOUNT_FIELD_PARENT_ACCOUNT_ID, String.class)));
                if (CollectionUtils.notEmpty(parentObect)) {
                    allDataList.addAll(parentObect);
                    recursionChangeData(allDataList, parentObect.get(0), needChangeData, 0);
                    continue;
                }
                String newAccountPath = child.getId();
                if (!Strings.isNullOrEmpty(oldAccountPath) && !Objects.equals(oldAccountPath, newAccountPath)) {
                    child.set(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, newAccountPath);
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, 0);
                continue;
            }
        }
    }

    /**
     * 以当前节点为根节点处理下级节点数据
     *
     * @param parent
     */
    public static void recursionChangeData(List<IObjectData> allDataList, IObjectData parent,
                                           Map<String, IObjectData> needChangeData, int depth) {
        //depth避免无限递归
        if (parent == null || depth > 1000) {
            return;
        }
        depth++;

        List<IObjectData> leafNodeList = allDataList.stream()
                .filter(n -> parent.getId().equals(n.get(CommonConstant.ACCOUNT_FIELD_PARENT_ACCOUNT_ID, String.class)))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(leafNodeList)) {
            Iterator<IObjectData> leafIt = leafNodeList.iterator();
            while (leafIt.hasNext()) {
                IObjectData child = leafIt.next();
                String oldAccountPath = child.get(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, String.class);
                if ("invalid".equals(child.get("life_status", String.class)) || child.isDeleted()) {
                    String newAccountPath = child.getId();
                    if (!Strings.isNullOrEmpty(oldAccountPath) && !Objects.equals(oldAccountPath, newAccountPath)) {
                        child.set(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, newAccountPath);
                        needChangeData.put(child.getId(), child);
                    }
                    recursionChangeData(allDataList, child, needChangeData, depth);
                    continue;
                }

                if (child.getId().equals(child.get(CommonConstant.ACCOUNT_FIELD_PARENT_ACCOUNT_ID, String.class))) {
                    allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    log.info("AccountPathChange ref self {}", child.getId());
                    continue;
                }

                String parentAccountPath = getOldAccountPath(parent);

                if (parentAccountPath.contains(child.getId())) {
                    allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    log.info("AccountPathChange childisfather {}", child.getId());
                    continue;
                }

                String newAccountPath = parentAccountPath + child.getId();
                if (newAccountPath.split("\\.").length > 10) {
                    allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    log.info("AccountPathChange levelcannotexceedten {}", child.getId());
                    continue;
                }

                if (!Objects.equals(oldAccountPath, newAccountPath)) {
                    child.set(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, newAccountPath);
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, depth);
                continue;
            }
        }
    }

    @NotNull
    private static String getOldAccountPath(IObjectData parent) {
        String parentAccountPath = parent.get(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, String.class) + ".";
        if ("invalid".equals(parent.get("life_status", String.class)) || parent.isDeleted()) {
            parentAccountPath = "";
        } else if (Strings.isNullOrEmpty(parent.get(CommonConstant.ACCOUNT_FIELD_ACCOUNT_PATH, String.class))) {
            parentAccountPath = parent.getId() + ".";
        }
        return parentAccountPath;
    }

    /**
     * 获取在当前数据列表中算作根节点的数据
     *
     * @param objectDataList
     * @return
     */
    @Nullable
    private static List<IObjectData> getRootObjectData(List<IObjectData> objectDataList) {
        List<IObjectData> rootAccountList = Lists.newArrayList();
        //标记根节点
        Iterator<IObjectData> it = objectDataList.iterator();
        while (it.hasNext()) {
            IObjectData m = it.next();
            if (Strings.isNullOrEmpty(m.get(CommonConstant.ACCOUNT_FIELD_PARENT_ACCOUNT_ID, String.class)) ||
                    !objectDataList.stream().anyMatch(x -> x.getId().equals(m.get(CommonConstant.ACCOUNT_FIELD_PARENT_ACCOUNT_ID, String.class)))) {
                rootAccountList.add(m);
            }
        }
        return rootAccountList;
    }

    /**
     * 获取描述
     *
     * @param user
     * @return
     */
    public static IObjectDescribe getObjectDescribe(User user, String apiName) {
        IObjectDescribe objectDescribe = describeService.findObject(user.getTenantId(), apiName);
        if (objectDescribe == null) {
            log.warn("AccountPathUtil.getObjectDescribe>获取描述失败={},{}", user.getTenantId(), apiName);
            throw new ValidateException("获取描述失败" + apiName);// ignoreI18n
        }
        return objectDescribe;
    }

    /**
     * 获取描述
     *
     * @param user
     * @return
     */
    public static Map<String, IObjectDescribe> getObjectDescribeMap(User user) {
        Map<String, IObjectDescribe> describiMap = describeService.findObjects(user.getTenantId(),
                Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.ACCOUNT_ADDR_API_NAME));
        if (describiMap.isEmpty()) {
            log.warn("AccountPathUtil.getObjectDescribe>获取描述失败={},{}", user.getTenantId(), Utils.ACCOUNT_API_NAME);
            throw new ValidateException("获取描述失败" + Utils.ACCOUNT_API_NAME);// ignoreI18n
        }
        return describiMap;
    }

    public static List<IObjectData> batchUpdateIgnoreOther(User user, List<IObjectData> dataList,
                                                           List<String> updateFieldList, boolean allowUpdateInvalid) {
        return batchUpdateIgnoreOther(buildContext(user, allowUpdateInvalid), dataList, updateFieldList);
    }

    /**
     * 更新固定字段
     *
     * @param context
     * @param dataList
     * @param updateFieldList
     * @return
     */
    private static List<IObjectData> batchUpdateIgnoreOther(IActionContext context, List<IObjectData> dataList, List<String> updateFieldList) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(updateFieldList)) {
            return dataList;
        }
        try {
            List<IObjectData> updateList = Lists.newArrayList();
            dataList.forEach(x -> {
                IObjectData data = new ObjectData();
                if (ObjectDataExt.of(x).containsExtendObjDataId()) {
                    ObjectDataExt.of(data).setExtendObjDataId(ObjectDataExt.of(x).getExtendObjDataId());
                }
                data.setTenantId(x.getTenantId());
                data.setDescribeApiName(x.getDescribeApiName());
                data.setId(x.getId());
                updateFieldList.forEach(y -> data.set(y, x.get(y)));
                updateList.add(data);
            });

            return dataProxy.batchUpdateIgnoreOther(updateList, updateFieldList, context);
        } catch (MetadataServiceException e) {
            log.warn("AccountPathUtil.batchUpdateIgnoreOther error,context:{},dataList:{},updateFieldList:{}",
                    context, dataList, updateFieldList, e);
            throw new MetaDataBusinessException(e);
        }
    }
}
