package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl.feature;

import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class TrustLevelInsightHandler extends AbstractInsightHandler<AttendeesInsightModel.FeatureInsightResult>  {

    public static final String PROMPT = "prompt_attendee_insight_trust_level";

    @Override
    public String getInsightType() {
        return "trust_level";
    }

    @Override
    public void insight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        doInsight(attendeesInsightMessage);
    }

    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        featureInsight(attendeesInsightMessage, PROMPT);
    }

    protected String getFeatureFieldName() {
        return "trust_level";
    }

    @Override
    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return super.getTheirSideNames(insightMessage);
    }

    @Override
    protected String getParticipantTypes() {
        return "their_side";
    }

    @Override
    protected void addSceneVariables(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, Map<String, Object> sceneVariables) {
        addMeetingObjectives(attendeesInsightMessage, sceneVariables);
    }

}
