package com.facishare.crm.task.sfa.services;

import com.facishare.crm.task.sfa.correct.ConfusionWord;
import com.facishare.crm.task.sfa.correct.ConfusionWords;
import com.facishare.crm.task.sfa.correct.Level;
import com.facishare.crm.task.sfa.correct.ProperCorrector;
import com.facishare.crm.task.sfa.correct.ProperWord;
import com.facishare.crm.task.sfa.correct.ProperWords;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.services.TermBankConfusableService.CONFUSABLE_WORD;
import static com.facishare.crm.task.sfa.services.TermBankConfusableService.SEGMENTATION_LEVEL;

@Slf4j
@Service
public class TermBankService implements InitializingBean {
    private static final String PROP_CACHE_PREFIX = "sfa.TermBank.prop:";
    private static final String CONF_WORD_CACHE_PREFIX = "sfa.TermBank.conf.word:";
    private static final String CONF_SENT_CACHE_PREFIX = "sfa.TermBank.conf.sent:";
    private final Set<String> limit500Tenants = new HashSet<>();
    private final Set<String> limit2000Tenants = new HashSet<>();
    private final ProperCorrector properCorrector;
    private final ServiceFacade serviceFacade;
    private final MergeJedisCmd mergeJedisCmd;

    public TermBankService(ProperCorrector properCorrector, ServiceFacade serviceFacade, @Qualifier("SFAJedisCmd") MergeJedisCmd mergeJedisCmd) {
        this.properCorrector = properCorrector;
        this.serviceFacade = serviceFacade;
        this.mergeJedisCmd = mergeJedisCmd;
    }

    public List<String> correct(String tenantId, List<String> textList) {
        Optional<Session> session = createSession(tenantId);
        if (session.isPresent()) {
            return correct(session.get(), textList);
        } else {
            return textList;
        }
    }

    public List<String> correct(Session session, List<String> textList) {
        return properCorrector.batchCorrect(textList, session.prop, session.conf);
    }

    public String correct(Session session, String text) {
        return properCorrector.correct(text, session.prop, session.conf);
    }

    public Optional<Session> createSession(String tenantId) {
        try {
            ConfusionWords confusionWords = getConfusionWords(tenantId);
            Optional<ProperWords> properWords = getProperWords(tenantId);
            return properWords.map(words -> new Session(words, confusionWords));
        } catch (Exception e) {
            log.error("create session failed", e);
            return Optional.empty();
        }
    }

    public void clearCache(String tenantId) {
        mergeJedisCmd.del(PROP_CACHE_PREFIX + tenantId);
        mergeJedisCmd.del(CONF_WORD_CACHE_PREFIX + tenantId);
        mergeJedisCmd.del(CONF_SENT_CACHE_PREFIX + tenantId);
    }

    private Optional<ProperWords> getProperWords(String tenantId) {
        if (mergeJedisCmd.exists(PROP_CACHE_PREFIX + tenantId)) {
            Map<String, String> cache = mergeJedisCmd.hgetAll(PROP_CACHE_PREFIX + tenantId);
            List<ProperWord> props = new ArrayList<>();
            for (Map.Entry<String, String> entry : cache.entrySet()) {
                props.add(new ProperWord(entry.getKey(), Float.parseFloat(entry.getValue())));
            }
            return Optional.of(new ProperWords(props));
        }
        User user = User.systemUser(tenantId);
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        SearchUtil.fillFilterEq(filters, "biz_status", 1);
        SearchTemplateQuery query = new SearchTemplateQuery();
        setQueryLimit(tenantId, query);
        query.setFilters(filters);
        List<String> termBankIds = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, "TermBankObj", query, Lists.newArrayList(DBRecord.ID)).getData().stream()
                .map(IObjectData::getId).collect(Collectors.toList());
        filters.clear();
        if (termBankIds.isEmpty()) {
            return Optional.empty();
        }
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        SearchUtil.fillFilterIn(filters, "term_bank_id", termBankIds);
        query.resetFilters(filters);
        List<ProperWord> props = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, "TermBankDetailObj", query, Lists.newArrayList(IObjectData.NAME)).getData().stream()
                .map(data -> new ProperWord(data.getName(), getSimThreshold(data))).collect(Collectors.toList());
        if (props.isEmpty()) {
            return Optional.empty();
        }
        Map<String, String> cacheMap = mergeJedisCmd.hgetAll(PROP_CACHE_PREFIX + tenantId);
        for (ProperWord prop : props) {
            cacheMap.put(prop.getText(), String.valueOf(prop.getSim()));
        }
        mergeJedisCmd.hset(PROP_CACHE_PREFIX + tenantId, cacheMap);
        mergeJedisCmd.expire(PROP_CACHE_PREFIX + tenantId, 5 * 60L);
        return Optional.of(new ProperWords(props));
    }

    private ConfusionWords getConfusionWords(String tenantId) {
        List<ConfusionWord> cacheList = getConfusionWordsFromCache(tenantId);
        if (!cacheList.isEmpty()) {
            return new ConfusionWords(cacheList);
        }
        try {
            serviceFacade.findObject(tenantId, TermBankConfusableService.OBJECT_API_NAME);
        } catch (ObjectDefNotFoundError ignore) {
            return new ConfusionWords();
        }
        User user = User.systemUser(tenantId);
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        SearchTemplateQuery query = new SearchTemplateQuery();
        setQueryLimit(tenantId, query);
        query.setFilters(filters);
        query.setOrders(Collections.singletonList(new OrderBy(IObjectData.LAST_MODIFIED_TIME, false)));
        List<IObjectData> dataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, TermBankConfusableService.OBJECT_API_NAME, query, Lists.newArrayList(IObjectData.NAME, CONFUSABLE_WORD, SEGMENTATION_LEVEL)).getData();
        List<ConfusionWord> confusionList = new ArrayList<>();
        for (IObjectData data : dataList) {
            String conf = data.get(CONFUSABLE_WORD, String.class);
            String word = data.getName();
            if (StringUtils.isNotBlank(conf) && StringUtils.isNotBlank(word)) {
                confusionList.add(new ConfusionWord(conf, word, data.get(SEGMENTATION_LEVEL, String.class)));
            }
        }
        saveConfusionWordsToCache(tenantId, confusionList);
        return new ConfusionWords(confusionList);
    }

    private List<ConfusionWord> getConfusionWordsFromCache(String tenantId) {
        List<ConfusionWord> cacheList = new ArrayList<>();
        if (mergeJedisCmd.exists(CONF_WORD_CACHE_PREFIX + tenantId)) {
            Map<String, String> cache = mergeJedisCmd.hgetAll(CONF_WORD_CACHE_PREFIX + tenantId);
            for (Map.Entry<String, String> entry : cache.entrySet()) {
                cacheList.add(new ConfusionWord(entry.getKey(), entry.getValue(), Level.Word));
            }
        }
        if (mergeJedisCmd.exists(CONF_SENT_CACHE_PREFIX + tenantId)) {
            Map<String, String> cache = mergeJedisCmd.hgetAll(CONF_SENT_CACHE_PREFIX + tenantId);
            for (Map.Entry<String, String> entry : cache.entrySet()) {
                cacheList.add(new ConfusionWord(entry.getKey(), entry.getValue(), Level.Sent));
            }
        }
        return cacheList;
    }

    private void saveConfusionWordsToCache(String tenantId, List<ConfusionWord> list) {
        Map<String, String> wordLevelMap = new HashMap<>();
        Map<String, String> sentLevelMap = new HashMap<>();
        for (ConfusionWord word : list) {
            if (Level.Word == word.getLevel()) {
                wordLevelMap.put(word.getConf(), word.getWord());
            }
            if (Level.Sent == word.getLevel()) {
                sentLevelMap.put(word.getConf(), word.getWord());
            }
        }
        if (!wordLevelMap.isEmpty()) {
            mergeJedisCmd.hset(CONF_WORD_CACHE_PREFIX + tenantId, wordLevelMap);
            mergeJedisCmd.expire(CONF_WORD_CACHE_PREFIX + tenantId, 5 * 60L);
        }
        if (!sentLevelMap.isEmpty()) {
            mergeJedisCmd.hset(CONF_SENT_CACHE_PREFIX + tenantId, sentLevelMap);
            mergeJedisCmd.expire(CONF_SENT_CACHE_PREFIX + tenantId, 5 * 60L);
        }
    }

    private static float getSimThreshold(IObjectData data) {
        BigDecimal sim = data.get("sim_threshold", BigDecimal.class);
        if (sim == null) {
            return 0.85f;
        } else {
            return sim.floatValue();
        }
    }

    private void setQueryLimit(String tenantId, SearchTemplateQuery query) {
        if (limit500Tenants.contains(tenantId)) {
            query.setLimit(500);
        } else if (limit2000Tenants.contains(tenantId)) {
            query.setLimit(2000);
        } else {
            query.setLimit(200);
        }
    }

    @Override
    public void afterPropertiesSet() {
        IConfigFactory factory = ConfigFactory.getInstance();
        factory.getConfig("fs-crm-sales-config", c -> {
            String str500 = c.get("term_bank_detail_limit_500");
            if (StringUtils.isNotBlank(str500)) {
                String[] tenantIds = str500.split(",");
                limit500Tenants.addAll(Arrays.asList(tenantIds));
            }
            String str2000 = c.get("term_bank_detail_limit_2000");
            if (StringUtils.isNotBlank(str2000)) {
                String[] tenantIds = str2000.split(",");
                limit2000Tenants.addAll(Arrays.asList(tenantIds));
            }
        });
    }

    @AllArgsConstructor
    public static class Session {
        final ProperWords prop;
        final ConfusionWords conf;
    }
}
