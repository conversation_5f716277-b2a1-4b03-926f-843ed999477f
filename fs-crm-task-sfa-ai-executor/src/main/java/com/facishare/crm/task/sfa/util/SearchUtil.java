package com.facishare.crm.task.sfa.util;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.function.Supplier;

public class SearchUtil {
    public static final String FIELD_VALUE_TYPE_SQL = "sql";
    public static final int MAX_LIMIT = 2000;

    public static void fillFiltersWithUser(User user, List filters) {
        fillFilterEq(filters, IObjectDescribe.TENANT_ID, user.getTenantId());
        fillFilterEq(filters, IObjectDescribe.PACKAGE, "CRM");
    }

    public static void fillFilterGT(List filters, String name, Object value) {
        filters.add(filter(name, Operator.GT, value));
    }

    public static void fillFilterGTE(List filters, String name, Object value) {
        filters.add(filter(name, Operator.GTE, value));
    }

    public static void fillFilterLT(List filters, String name, Object value) {
        filters.add(filter(name, Operator.LT, value));
    }

    public static void fillFilterLTE(List filters, String name, Object value) {
        filters.add(filter(name, Operator.LTE, value));
    }

    public static void fillFilterEq(List filters, String name, Object value) {
        filters.add(filter(name, Operator.EQ, value));
    }

    public static void fillFilterNotEq(List filters, String name, Object value) {
        filters.add(filter(name, Operator.N, value));
    }

    public static void fillFilterLike(List filters, String name, Object value) {
        filters.add(filter(name, Operator.LIKE, value));
    }

    public static void fillFilterIn(List filters, String name, Object value) {
        filters.add(filter(name, Operator.IN, value));
    }

    public static void fillFilterNotIN(List filters, String name, Object value) {
        filters.add(filter(name, Operator.NIN, value));
    }

    public static void fillFilterMatch(List filters, String name, Object value) {
        filters.add(filter(name, Operator.MATCH, value));
    }

    public static void fillFilterIsNull(List filters, String name) {
        filters.add(filter(name, Operator.IS, Lists.newArrayList()));
    }

    public static OrderBy orderByLastModifiedTime() {
        return new OrderBy("last_modified_time", false);
    }

    public static OrderBy orderByDataId() {
        return new OrderBy("_id", false);
    }

    public static OrderBy orderByCreateTime() {
        return new OrderBy("create_time", true);
    }

    public static void fillFilterInBySql(List filters, String name, String fieldValueType, Object value) {
        Filter filter = filter(name, Operator.IN, value);
        filter.setFieldValueType(fieldValueType);
        filters.add(filter);
    }

    public static void fillFilterBySql(List filters, String name, Operator operator, Object value) {
        Filter filter = filter(name, operator, value);
        filter.setFieldValueType(SearchUtil.FIELD_VALUE_TYPE_SQL);
        filters.add(filter);
    }

    public static Filter filter(String name, Operator operator, Object value) {
        Filter filter = new Filter();
        filter.setFieldName(name);
        if (value instanceof List) {
            filter.setFieldValues((List<String>) value);
        } else if (value instanceof Set) {
            filter.setFieldValues(Lists.newArrayList((Set<String>) value));
        } else {
            filter.setFieldValues(Arrays.asList(value.toString()));
        }
        filter.setOperator(operator);
        return filter;
    }

    public static void fillFilterHasAnyOf(List filters, String name, Object value) {
        filters.add(filter(name, Operator.HASANYOF, value));
    }

    public static void fillFilterISNotNull(List filters, String name) {
        filters.add(filter(name, Operator.ISN, Lists.newArrayList()));
    }

    public static OrderBy order(String name, Boolean isAsc) {
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(name);
        orderBy.setIsAsc(isAsc);
        return orderBy;
    }

    public static OrderBy order(String name, String value, Boolean isAsc) {
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(name);
        orderBy.setValue(value);
        orderBy.setIsAsc(isAsc);
        return orderBy;
    }

    public static void fillOrderBy(List orders, String name, Boolean isAsc) {
        orders.add(order(name, isAsc));
    }

    public static List<IObjectData> findAll(SearchTemplateQuery searchTemplateQuery,
            Supplier<QueryResult<IObjectData>> queryResultFunction) {
        List<IObjectData> res = new ArrayList<>();
        for (int limit = 1000, offset = 0; true; offset += limit) {
            searchTemplateQuery.setLimit(limit);
            searchTemplateQuery.setOffset(offset);
            QueryResult<IObjectData> queryResult = queryResultFunction.get();
            if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                return res;
            }
            res.addAll(queryResult.getData());
            if (queryResult.getData().size() < limit) {
                return res;
            }
        }
    }

    public static SearchTemplateQueryPlus buildBaseSearchQuery() {
        SearchTemplateQueryPlus searchQuery = new SearchTemplateQueryPlus();
        searchQuery.setLimit(MAX_LIMIT);
        searchQuery.setNeedReturnQuote(false);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        return searchQuery;
    }
}
