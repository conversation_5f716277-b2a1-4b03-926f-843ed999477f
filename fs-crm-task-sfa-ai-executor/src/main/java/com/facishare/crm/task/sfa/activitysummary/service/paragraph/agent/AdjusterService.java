package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.facishare.crm.task.sfa.activitysummary.model.EvaluationModel;
import com.facishare.crm.task.sfa.activitysummary.model.PlanModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Adjuster服务类
 * 负责根据评估结果调整分段策略
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class AdjusterService {
    
    /**
     * 根据评估结果调整分段策略
     * 使用大模型返回的详细评估结果和调整计划来优化分段策略
     */
    public PlanModel adjustPlanBasedOnEvaluation(PlanModel originalPlan, EvaluationModel evaluation) {
        PlanModel adjustedPlan = new PlanModel();
        adjustedPlan.setStrategy(originalPlan.getStrategy());
        
        // 使用大模型提供的理想段落数量
        if (evaluation.getAdjustmentPlan() != null && evaluation.getAdjustmentPlan().getIdealSegmentCount() > 0) {
            adjustedPlan.setExpectedSegments(evaluation.getAdjustmentPlan().getIdealSegmentCount());
            log.info("根据评估结果调整段落数量: {} -> {}", 
                    originalPlan.getExpectedSegments(), adjustedPlan.getExpectedSegments());
        } else {
            // 如果没有明确的理想段落数量，保持原计划的预期段落数
            adjustedPlan.setExpectedSegments(originalPlan.getExpectedSegments());
        }
        
        // 使用大模型提供的重点关注区域作为新的关注点
        if (evaluation.getAdjustmentPlan() != null && 
                CollectionUtils.isNotEmpty(evaluation.getAdjustmentPlan().getFocusAreas())) {
            adjustedPlan.setFocusPoints(evaluation.getAdjustmentPlan().getFocusAreas());
            log.info("根据评估结果更新关注点: {}", adjustedPlan.getFocusPoints());
        } else {
            // 保留原计划的关注点
            adjustedPlan.setFocusPoints(originalPlan.getFocusPoints());
        }
        
        // 添加特殊处理指令
        List<String> specialHandling = new ArrayList<>();
        
        // 保留原计划中的特殊处理指令
        if (originalPlan.getSpecialHandling() != null) {
            specialHandling.addAll(originalPlan.getSpecialHandling());
        }
        
        // 添加合并段落的指令
        if (evaluation.getAdjustmentPlan() != null && 
                CollectionUtils.isNotEmpty(evaluation.getAdjustmentPlan().getMergeSegments())) {
            specialHandling.add("合并段落: " + String.join(", ", evaluation.getAdjustmentPlan().getMergeSegments()));
            log.info("添加合并段落指令: {}", evaluation.getAdjustmentPlan().getMergeSegments());
        }
        
        // 添加拆分段落的指令
        if (evaluation.getAdjustmentPlan() != null && 
                CollectionUtils.isNotEmpty(evaluation.getAdjustmentPlan().getSplitSegments())) {
            specialHandling.add("拆分段落: " + String.join(", ", evaluation.getAdjustmentPlan().getSplitSegments()));
            log.info("添加拆分段落指令: {}", evaluation.getAdjustmentPlan().getSplitSegments());
        }
        
        // 添加评估中的建议
        if (CollectionUtils.isNotEmpty(evaluation.getSuggestions())) {
            specialHandling.addAll(evaluation.getSuggestions());
        }
        
        adjustedPlan.setSpecialHandling(specialHandling);
        
        // 记录维度评分，帮助理解调整原因
        if (evaluation.getDimensionScores() != null) {
            log.info("维度评分 - 连贯性: {}, 摘要质量: {}, 分段数量: {}, 区分度: {}", 
                    evaluation.getDimensionScores().getCoherence(),
                    evaluation.getDimensionScores().getSummaryQuality(),
                    evaluation.getDimensionScores().getSegmentationCount(),
                    evaluation.getDimensionScores().getDistinction());
        }
        
        return adjustedPlan;
    }
}
