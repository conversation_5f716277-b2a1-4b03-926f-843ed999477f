package com.facishare.crm.task.sfa.bizfeature.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureNode;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureNodeFinishService;
import com.fxiaoke.helper.CollectionHelper;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 特征任务节点完成判断监听
 */
@Slf4j
@Component
public class FeatureNodeListener implements ApplicationListener<ContextRefreshedEvent> {

    @Resource
    private FeatureNodeFinishService nodeService;

    private AutoConfMQPushConsumer consumer;

    private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "crm-feature-node-consumer",
                (MessageListenerConcurrently) (msgs, context) -> {
                    for (MessageExt msg : msgs) {
                        try {
                            FeatureNode featureNode = messageParse(msg);
                            consume(featureNode);
                        } catch (Exception e) {
                            log.error("FeatureNodeListener :{}", msg, e);
                            //throw new RuntimeException(e);
                        }
                    }
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                });
    }

    private void consume(FeatureNode featureNode) {
        if (featureNode == null || StringUtils.isBlank(featureNode.getTenantId())
                || CollectionHelper.isEmpty(featureNode.getObjectIds())) {
            log.error("featureNode error, featureNode:{}", featureNode);
            return;
        }
        if (gray.isAllow("feature-node-skip-tenant", featureNode.getTenantId())) {
            return;
        }
        nodeService.finish(featureNode);
    }

    private FeatureNode messageParse(MessageExt messageExt) {
        try {
            return JSON.parseObject(StringHelper.toString(messageExt.getBody()), FeatureNode.class);
        } catch (Exception e) {
            log.error("feature Node message format failed. msgId:{}, body:{}", messageExt.getMsgId(),
                    StringHelper.toString(messageExt.getBody()));
        }
        return null;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

}