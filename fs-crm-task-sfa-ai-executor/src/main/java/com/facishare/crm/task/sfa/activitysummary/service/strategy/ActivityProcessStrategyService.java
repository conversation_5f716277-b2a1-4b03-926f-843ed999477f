package com.facishare.crm.task.sfa.activitysummary.service.strategy;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphContext;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.ActivityParagraphService;
import com.facishare.paas.appframework.core.model.User;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 活动记录处理策略服务
 * on 2024/6/1
 */
@Service
@Slf4j
public class ActivityProcessStrategyService {

    private static boolean isDirectTaggingEnabled; // 后续可改为动态配置

    @Autowired
    private ActivityParagraphService activityParagraphService;

    @Autowired
    private DirectTaggingService directTaggingService;

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            isDirectTaggingEnabled = config.getBool("isDirectTaggingEnabled", true);
        });
    }

    public void processFileToText(User user, ActivityMessage activityMessage) {
        if (isDirectTaggingEnabled) {
            directTaggingService.process(user, activityMessage, ParagraphContext.TEXT_KEY);
        } else {
            activityParagraphService.processTextWithSegmentation(user, activityMessage.getObjectId());
        }
    }

    public void processRealtimeToTextDone(User user, ActivityMessage activityMessage) {
        if (isDirectTaggingEnabled) {
            directTaggingService.process(user, activityMessage, ParagraphContext.MONGO_KEY);
        } else {
            activityParagraphService.processDocumentsWithSegmentation(user, activityMessage.getObjectId());
        }
    }
} 