package com.facishare.crm.task.sfa.xxl;

import com.facishare.crm.sfa.lto.activity.constants.InteractionStrategyTaskConstants;
import com.facishare.crm.sfa.lto.activity.mongo.InteractionStrategyTaskDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractionStrategyTaskDocument;
import com.facishare.crm.task.sfa.service.StrategyTaskService;
import com.facishare.crm.task.sfa.service.impl.StrategyConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 交互策略任务处理器，处理策略任务的计算
 * @IgnoreI18nFile
 */
@Slf4j
@Component
@JobHander(value = "StrategyTaskJobHandler")
public class StrategyTaskJobHandler extends IJobHandler {

    @Autowired
    private StrategyTaskService strategyTaskService;

    @Autowired
    private InteractionStrategyTaskDao interactionStrategyTaskDao;

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        log.info("StrategyTaskJobHandler#execute {}", triggerParam);

        try {
            executeTaskProcess();
        } catch (Exception e) {
            log.error("Strategy task processing encountered an error", e);
            return ReturnT.FAIL;
        }
        log.info("StrategyTaskJobHandler#execute end {}", triggerParam);
        return ReturnT.SUCCESS;
    }


    /**
     * 执行任务处理流程
     */
    private void executeTaskProcess() {
        // 检查是否允许执行任务
        if (!StrategyConfig.STRATEGY_ALLOW_RUNNING) {
            log.info("Strategy task execution is disabled by configuration");
            return;
        }

        // 获取前N天的时间范围
        long yesterdayStartTime = getYesterdayStartTime();
        long yesterdayEndTime = getYesterdayEndTime();

        // 查询前N天创建的任务
        List<InteractionStrategyTaskDocument> taskList = findTasksForProcessing(yesterdayStartTime, yesterdayEndTime);

        if (CollectionUtils.isEmpty(taskList)) {
            log.info("No tasks found for processing in time range: {} to {}",
                    yesterdayStartTime, yesterdayEndTime);
            return;
        }

        log.info("Found {} tasks for processing", taskList.size());

        // 处理任务
        processTasks(taskList);
    }

    /**
     * 获取前N天开始时间戳（毫秒）
     * 使用StrategyConfig.STRATEGY_TASK_BEFORE_DAY配置
     */
    private long getYesterdayStartTime() {
        // 使用配置的天数，默认为1天前
        int daysBack = StrategyConfig.STRATEGY_TASK_BEFORE_DAY;
        LocalDateTime targetDay = LocalDateTime.now().minusDays(daysBack);
        LocalDateTime startOfDay = targetDay.toLocalDate().atStartOfDay();
        // 使用系统默认时区
        return startOfDay.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
    }

    /**
     * 获取前N天结束时间戳（毫秒）
     * 使用StrategyConfig.STRATEGY_TASK_BEFORE_DAY配置
     */
    private long getYesterdayEndTime() {
        // 使用配置的天数，默认为1天前
        int daysBack = StrategyConfig.STRATEGY_TASK_BEFORE_DAY;
        LocalDateTime targetDay = LocalDateTime.now().minusDays(daysBack);
        LocalDateTime endOfDay = targetDay.toLocalDate().atTime(23, 59, 59, 999000000);
        // 使用系统默认时区
        return endOfDay.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
    }

    /**
     * 查询需要处理的任务
     */
    private List<InteractionStrategyTaskDocument> findTasksForProcessing(long startTime, long endTime) {
        try {
            // 查询状态为初始化和运行中的任务
            return interactionStrategyTaskDao.findByStatusAndCreateTimeBetween(startTime, endTime);
        } catch (Exception e) {
            log.error("Error finding tasks for processing", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理任务列表
     */
    public void processTasks(List<InteractionStrategyTaskDocument> taskList) {
        // 循环处理任务，按创建时间顺序处理
        for (InteractionStrategyTaskDocument task : taskList) {

            try {
                // 对每个任务进行处理
                processTask(task);
            } catch (Exception e) {
                log.error("Error processing task: {}", task.getId(), e);
                // 更新任务状态为失败
                try {
                    interactionStrategyTaskDao.updateStatus(task.getId(),
                            InteractionStrategyTaskConstants.TaskStatus.FAIL);
                    interactionStrategyTaskDao.updateFailReason(task.getId(),
                            "处理异常: " + e.getMessage());
                } catch (Exception ex) {
                    log.error("Error updating task status to failed", ex);
                }
            }
        }
    }

    /**
     * 根据任务ID执行指定任务
     * @param taskId 任务ID
     * @return 处理结果
     */
    public boolean executeTaskById(String taskId) {
        log.info("Executing task by ID: {}", taskId);

        if (taskId == null || taskId.trim().isEmpty()) {
            log.warn("Task ID is null or empty");
            return false;
        }

        try {
            // 查找指定的任务
            InteractionStrategyTaskDocument task = interactionStrategyTaskDao.findById(new ObjectId(taskId));
            if (task == null) {
                log.warn("Task not found for ID: {}", taskId);
                return false;
            }

            // 检查任务状态是否允许执行
            if (!isTaskExecutable(task)) {
                log.warn("Task {} is not in executable state, current status: {}", taskId, task.getStatus());
                return false;
            }

            // 执行任务
            processTask(task);
            log.info("Task {} executed successfully", taskId);
            return true;

        } catch (Exception e) {
            log.error("Error executing task by ID: {}", taskId, e);
            return false;
        }
    }

    /**
     * 检查任务是否可执行
     * @param task 任务
     * @return 是否可执行
     */
    private boolean isTaskExecutable(InteractionStrategyTaskDocument task) {
        String status = task.getStatus();
        return InteractionStrategyTaskConstants.TaskStatus.INIT.equals(status) ||
               InteractionStrategyTaskConstants.TaskStatus.RUNNING.equals(status);
    }

    /**
     * 处理单个任务
     */
    private void processTask(InteractionStrategyTaskDocument task) {
        String tenantId = task.getTenantId();
        String strategyId = task.getStrategyId();

        log.info("Processing task: {}, tenantId: {}, strategyId: {}",
                task.getId(), tenantId, strategyId);
        task.setStatus(InteractionStrategyTaskConstants.TaskStatus.RUNNING);
        task = interactionStrategyTaskDao.saveOrUpdate(task);

        // 查询策略和策略明细
        User user = User.systemUser(tenantId);
        // 查询策略
        IObjectData strategy = strategyTaskService.queryStrategy(user, strategyId);
        if (strategy == null) {
            log.warn("Strategy not found: {}", strategyId);
            task.setStatus(InteractionStrategyTaskConstants.TaskStatus.FAIL);
            task.setFailReason("未找到策略");
            interactionStrategyTaskDao.saveOrUpdate(task);
            return;
        }

        // 查询策略明细
        List<IObjectData> strategyDetails = strategyTaskService.queryStrategyDetails(user, strategyId);
        if (CollectionUtils.isEmpty(strategyDetails)) {
            log.warn("Strategy details not found for strategy: {}", strategyId);
            task.setStatus(InteractionStrategyTaskConstants.TaskStatus.FAIL);
            task.setFailReason("未找到策略明细");
            interactionStrategyTaskDao.saveOrUpdate(task);
            return;
        }

        // 获取客户查询条件
        try {
            // 获取符合条件的客户总数
            Integer totalCustomerCount = strategyTaskService.countCustomersByStrategy(user, strategy);
            task.setRunningTotal(totalCustomerCount);
            task = interactionStrategyTaskDao.saveOrUpdate(task);

            // 批量处理客户
            int processedCount = strategyTaskService.processCustomersForStrategy(user, strategy, strategyDetails, task);

            // 更新任务状态为完成
            interactionStrategyTaskDao.updateStatus(task.getId(),
                    InteractionStrategyTaskConstants.TaskStatus.FINISHED);
            log.info("Task completed: {}, processed {} customers", task.getId(), processedCount);

        } catch (Exception e) {
            log.error("Error processing task: {}", task.getId(), e);
            task.setStatus(InteractionStrategyTaskConstants.TaskStatus.FAIL);
            task.setFailReason("处理异常");
            interactionStrategyTaskDao.saveOrUpdate(task);
        }
    }
}