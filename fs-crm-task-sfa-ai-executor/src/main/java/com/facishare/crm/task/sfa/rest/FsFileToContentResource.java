package com.facishare.crm.task.sfa.rest;


import com.facishare.crm.task.sfa.rest.dto.FileToContentJobSubmit;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(
        value = "FS_FILE_PROCESS",
        desc = "任务调度接口",
        contentType = "application/json",
        codec = "com.facishare.rest.core.codec.DefaultRestCodec"
)
public interface FsFileToContentResource {
    @POST(value = "/FileProcess/Doc/SubmitJob", desc = "提交任务")
    FileToContentJobSubmit.JobSubmitResult submitJob(@HeaderParam("x-fs-ei") String ei,
                                                     @HeaderParam("x-fs-userInfo") String userInfo,
                                                     @Body FileToContentJobSubmit.JobSubmitArg request);


                                               
    @POST(value = "/FileProcess/Doc/QueryJob", desc = "查询任务状态")
    FileToContentJobSubmit.JobQueryResult getJobQuery(@HeaderMap Map<String, String> headers,
                                              @Body FileToContentJobSubmit.JobQueryArg request);


    @POST(value = "/FileProcess/Doc/GetContent", desc = "获取文档内容")
    FileToContentJobSubmit.JobGetContentResult getContent(@HeaderMap Map<String, String> headers,
                                                          @Body FileToContentJobSubmit.JobGetContentArg request);

}
