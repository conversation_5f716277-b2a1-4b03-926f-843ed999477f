package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.constant.ActivityQuestionConstants;
import com.facishare.crm.task.sfa.activitysummary.constant.InteractionStrategyConstants;
import com.facishare.crm.task.sfa.activitysummary.constant.InteractionStrategyDetailConstants;
import com.facishare.crm.task.sfa.activitysummary.model.auditlog.ActivityMessageConverter;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.common.constants.SystemConstants;
import com.facishare.crm.task.sfa.service.impl.StrategyQueryService;
import com.facishare.crm.task.sfa.services.rebate.rest.RuleEngineLogicService;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AccountStrategyService {
    private static final int MAX_STRATEGY_LIMIT = 2000;

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    protected RuleEngineLogicService engineLogicService;
    @Autowired
    private StrategyQueryService strategyQueryService;

    @SFAAuditLog(bizName = "account_strategy", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void consume(ActivityMessage activityMessage) {
        if (!isValidActivityMessage(activityMessage)) {
            log.warn("Invalid activity message: {}", activityMessage);
            return;
        }

        User user = User.systemUser(activityMessage.getTenantId());
        log.info("Processing activity for object: objectId={}, objectApiName={}",
                activityMessage.getObjectId(), activityMessage.getObjectApiName());

        IObjectData objectData = queryObjectDataById(activityMessage, user);
        if (objectData == null) {
            log.warn("Object data not found for id: {},object api name:{}", activityMessage.getObjectId(),  activityMessage.getObjectApiName());
            return;
        }

        List<IObjectData> strategyDataList = queryAllStrategy(user, activityMessage.getObjectApiName());
        if (CollectionUtils.empty(strategyDataList)) {
            log.info("No strategy data found for object type: {}", activityMessage.getObjectApiName());
            return;
        }
        String priorityMaxStrategyId = matchRuleEngine(strategyDataList, objectData, user);
        if (StringUtils.isBlank(priorityMaxStrategyId)) {
            log.info("No matching strategy found for object: {}", activityMessage.getObjectId());
            return;
        }
        log.info("Found matching strategy id: {}", priorityMaxStrategyId);

        List<IObjectData> strategyDetailList = queryStrategyDetails(user, priorityMaxStrategyId);
        if (CollectionUtils.empty(strategyDetailList)) {
            log.warn("No strategy detail found for strategy: {}", priorityMaxStrategyId);
            return;
        }

        createQuestionData(strategyDetailList, user, activityMessage);
    }

    private void createQuestionData(List<IObjectData> strategyDetailList, User user, ActivityMessage activityMessage) {
        List<IObjectData> needSaveData = buildDataList(strategyDetailList, user, activityMessage);
        if (CollectionUtils.notEmpty(needSaveData)) {
            try {
                serviceFacade.bulkSaveObjectData(needSaveData, user);
            } catch (Exception e) {
                log.error("save data error msg is {}", e.getMessage());
            }
        }
    }

    public List<IObjectData> buildDataList(List<IObjectData> strategyDetailList, User user, ActivityMessage activityMessage) {
        List<IObjectData> questionDataList = Lists.newArrayList();
        for (IObjectData objectData : strategyDetailList) {
            IObjectData questionData = new ObjectData();

            // 根据对象类型设置不同的字段
            if (CommonConstant.NEW_OPPORTUNITY_API_NAME.equals(activityMessage.getObjectApiName())) {
                questionData.set(ActivityQuestionConstants.NEW_OPPORTUNITY_ID, activityMessage.getObjectId());
            } else {
                questionData.set(ActivityQuestionConstants.ACCOUNT_ID, activityMessage.getObjectId());
            }

            questionData.set(ActivityQuestionConstants.LIBRARY_ID, objectData.get(InteractionStrategyDetailConstants.LIBRARY_ID, String.class));
            questionData.set(ActivityQuestionConstants.STRATEGY_ID, objectData.get(InteractionStrategyDetailConstants.STRATEGY_ID, String.class));
            questionData.set(ActivityQuestionConstants.ORDER_FIELD, objectData.get(InteractionStrategyDetailConstants.ORDER_FIELD, Integer.class));
            questionData.set(ActivityQuestionConstants.ADVICE_STATUS, ActivityQuestionConstants.AdviceStatusType.NOT_ASKED.getAdviceStatusType());
            questionData.set(ActivityQuestionConstants.QUESTION_TYPE, ActivityQuestionConstants.QuestionType.TWO.getQuestionType());
            questionData.set(SystemConstants.Field.Owner.apiName, Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
            questionData.set(SystemConstants.Field.DataOwnOrganization.apiName, Lists.newArrayList(User.COMPANY_ID));
            questionData.set(Tenantable.TENANT_ID, user.getTenantId());
            questionData.set(SystemConstants.Field.RecordType.apiName, SystemConstants.DEFAULT_RECORD_TYPE);
            questionData.setDescribeApiName(ActivityQuestionConstants.API_NAME);
            questionDataList.add(questionData);
        }
        return questionDataList;
    }

    private List<IObjectData> queryStrategyDetails(User user, String strategyId) {
        return strategyQueryService.queryStrategyDetails(user,strategyId);
    }

    private boolean isValidActivityMessage(ActivityMessage message) {
        return !StringUtils.isAnyBlank(
                message.getObjectId(),
                message.getObjectApiName(),
                message.getTenantId()
        );
    }

    private String matchRuleEngine(List<IObjectData> strategyDataList, IObjectData objectData, User user) {
        List<String> requestRuleIdList = strategyDataList.stream()
                .filter(this::hasValidCondition)
                .map(DBRecord::getId)
                .collect(Collectors.toList());

        if (requestRuleIdList.isEmpty()) {
            log.info("No match rules to evaluate");
            //取优先级最高的
            return strategyDataList.get(0).getId();
        }

        Set<String> matchStrategyIdList = engineLogicService.matchRuleCondition(
                user,
                requestRuleIdList,
                objectData.getDescribeApiName(),
                objectData
        );
        log.info("Match strategy id list: {}", matchStrategyIdList);

        return getPriorityMaxStrategyId(strategyDataList, matchStrategyIdList);
    }

    private boolean hasValidCondition(IObjectData strategy) {
        String condition = strategy.get(InteractionStrategyConstants.CONDITION, String.class);
        if (StringUtils.isBlank(condition)) {
            return false;
        }
        try {
            JSONObject conditionJson = JSON.parseObject(condition);
            String value = conditionJson.getString("value");
            return !"[]".equals(value);
        } catch (Exception e) {
            log.error("Parse condition json error", e);
            return false;
        }
    }

    private String getPriorityMaxStrategyId(List<IObjectData> strategyDataList, Set<String> matchStrategyIdList) {
        for (IObjectData objectData : strategyDataList) {
            if (!hasValidCondition(objectData)) {
                return objectData.getId();
            }
            // 条件不为空,判断是否匹配规则
            String strategyId = objectData.getId();
            if (matchStrategyIdList.contains(strategyId)) {
                return strategyId;
            }
        }
        return null;
    }

    private List<IObjectData> queryAllStrategy(User user, String objectApiName) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(InteractionStrategyConstants.USED_OBJECT_API_NAME, Operator.EQ, objectApiName);
        searchTemplateQueryPlus.setLimit(MAX_STRATEGY_LIMIT);
        searchTemplateQueryPlus.setOffset(0);
        searchTemplateQueryPlus.setSearchSource("db");
        searchTemplateQueryPlus.setOrders(Lists.newArrayList(new OrderBy(InteractionStrategyConstants.PRIORITY, true)));

        List<String> queryFieldList = Lists.newArrayList(
                DBRecord.ID,
                IObjectData.NAME,
                InteractionStrategyConstants.PRIORITY,
                InteractionStrategyConstants.CONDITION,
                InteractionStrategyConstants.USED_OBJECT_API_NAME
        );

        return Optional.ofNullable(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                user,
                InteractionStrategyConstants.INTERACTION_STRATEGY,
                searchTemplateQueryPlus,
                queryFieldList
        ))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    private IObjectData queryObjectDataById(ActivityMessage activityMessage, User user) {
        return serviceFacade.findObjectData(user, activityMessage.getObjectId(), activityMessage.getObjectApiName());
    }
}
