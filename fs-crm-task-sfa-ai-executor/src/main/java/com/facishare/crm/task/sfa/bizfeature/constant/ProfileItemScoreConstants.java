package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 画像分项分数
 */
public interface ProfileItemScoreConstants {
    /**
     * 画像
     */
    String PROFILE_ID = "profile_id";
    /**
     * 类型
     */
    String TYPE = "type";
    enum Type {
        /**
         * 维度
         */
        DIMENSION("dimension"),
        /**
         * 节点
         */
        NODE("node");

        private final String value;

        public String getValue() {
            return value;
        }

        Type(String value) {
            this.value = value;
        }
    }
    /**
     * 阶段
     */
    String NODE_ID = "node_id";
    /**
     * 维度
     */
    String FEATURE_DIMENSION_ID = "feature_dimension_id";
    /**
     * 分数
     */
    String SCORE = "score";
    /**
     * 完成任务
     */
    String COMPLETED_TASK_IDS = "completed_task_ids";
    /**
     * 未完成任务
     */
    String INCOMPLETE_TASK_IDS = "incomplete_task_ids";
    /**
     * 总结
     */
    String SUMMARY = "summary";
    /**
     * 趋势总结
     */
    String TREND_SUMMARY = "trend_summary";
    /**
     * 特征触发值
     */
    String FEATURE_TRIGGER_VALUES = "feature_trigger_values";
}