package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.Data;
import lombok.Builder;
import java.util.List;

/**
 * 活动标签模型
 */
@Data
@Builder
public class ActivityTag {
    /**
     * 标签ID
     */
    private String id;
    
    /**
     * 标签名称
     */
    private String name;
    
    /**
     * 标签描述
     */
    private String description;
    
    /**
     * 父标签ID
     */
    private String parentId;
    
    /**
     * 子标签列表
     */
    private List<ActivityTag> activityTags;
} 