package com.facishare.crm.task.sfa.model.proxy.bi;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface LwtViewDetailDataQuery extends BiCrmRestQuery {
    @Data
    class Arg {
        private QueryLwtDetailArg queryLwtDetailArg;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class QueryLwtDetailArg extends Page {
        private String id; // 拼表id
        private List<LwtExpandCell> expendCellArgs;
    }

    @Data
    class LwtExpandCell {
        @SerializedName("fieldID")
        @JSONField(name = "fieldID")
        private String fieldId;
        private String fieldType;
        private String formattedValue;
        private String value;
        private Integer isGroup;
        private String formatStr;
        private Integer isPre;
        private Integer objectSequence;
        private String firstDimensionValue;
        private String firstDimensionValueCode;
        private String secondDimensionValue;
        private String secondDimensionValueCode;
    }

    @Data
    class Result {
        private Integer code;
        private String message;
        private ResultData data;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class ResultData extends DetailResultData {}
}
