package com.facishare.crm.task.sfa.service.impl;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.activity.constants.InteractionStrategyTaskConstants;
import com.facishare.crm.sfa.lto.activity.mongo.InteractionStrategyTaskDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractionStrategyTaskDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.constant.ActivityQuestionConstants;
import com.facishare.crm.task.sfa.activitysummary.constant.InteractionStrategyConstants;
import com.facishare.crm.task.sfa.activitysummary.constant.InteractionStrategyDetailConstants;
import com.facishare.crm.task.sfa.activitysummary.service.AccountStrategyService;
import com.facishare.crm.task.sfa.bizfeature.model.RuleWhere;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.util.InteractionStrategyUtil;
import com.facishare.crm.task.sfa.util.QueryUtils;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.service.impl.StrategyConfig.*;

/**
 * 策略数据处理服务
 * 负责客户话题数据的处理和生成
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class StrategyDataProcessService {

    @Autowired
    private InteractionStrategyTaskDao interactionStrategyTaskDao;

    @Autowired
    private SpecialTableMapper specialTableMapper;

    @Autowired
    private StrategyDataProcessService proxy;

    @Autowired
    private AccountStrategyService accountStrategyService;

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private ObjectDataProxy dataProxy;

        /**
     * 使用Guava的RateLimiter进行限流
     * 令牌个数使用StrategyConfig中的limit值
     */
    private static RateLimiter rateLimiter;

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            rateLimiter = RateLimiter.create(config.getDouble("strategy_allow_limit", 20));
        });
    }
    /**
     * 处理策略下的客户数据
     *
     * @param user            用户信息
     * @param strategy        策略对象
     * @param strategyDetails 策略明细列表
     * @param task            任务对象
     * @return 处理的客户数量
     */
    public int processCustomersForStrategy(User user, IObjectData strategy,
                                           List<IObjectData> strategyDetails,
                                           InteractionStrategyTaskDocument task) {

        SearchTemplateQueryPlus searchTemplateQuery = buildSearchTemplateQuery(strategy);
        AtomicInteger atomicInteger = new AtomicInteger(0);
        String usedObjectApiName = strategy.get(InteractionStrategyConstants.USED_OBJECT_API_NAME, String.class);

        // 用于批量处理的集合
        List<String> deleteTopicIds = Lists.newArrayList();
        List<IObjectData> saveTopics = Lists.newArrayList();

        // 批量处理客户数据
        QueryUtils.templateQueryBatchIgnoreAll(searchTemplateQuery, user, usedObjectApiName,
                Lists.newArrayList(DBRecord.ID, DBRecord.CREATE_TIME), STRATEGY_ALLOW_RUNNING,
                (List<IObjectData> objectDataList, Integer batchNo) -> {
                    rateLimiter.acquire();
                    atomicInteger.set(batchNo);
                    // 处理每个客户
                    for (IObjectData accountData : objectDataList) {
                        String accountId = accountData.getId();
                        try {
                            // 收集要删除的ID和要保存的话题
                            collectTopicsForProcessing(user, usedObjectApiName, accountId, strategy, strategyDetails, deleteTopicIds,
                                    saveTopics);

                            // 当达到批量处理阈值时，执行批量操作
                            if (deleteTopicIds.size() >= BATCH_LIMIT || saveTopics.size() >= BATCH_LIMIT) {
                                try {
                                    // 执行批量操作
                                    proxy.executeBatchOperations(user, deleteTopicIds, saveTopics);
                                } catch (Exception e) {
                                    log.error("批量处理客户 {} 的话题失败，重试3次后仍然失败", accountId, e);
                                    // 异常了只记录日志
                                } finally {
                                    // 清空集合以便下一批处理
                                    deleteTopicIds.clear();
                                    saveTopics.clear();
                                }
                            }
                        } catch (Exception e) {
                            log.error("处理客户 {} 的话题数据失败", accountId, e);
                        }
                    }

                    // 更新任务处理进度
                    updateTaskProgress(task, batchNo);
                });

        // 处理剩余的批量操作
        if (!deleteTopicIds.isEmpty() || !saveTopics.isEmpty()) {
            try {
                // 执行批量操作
                proxy.executeBatchOperations(user, deleteTopicIds, saveTopics);
            } catch (Exception e) {
                log.error("处理剩余批量操作失败", e);
                // 更新任务错误信息
                task.setStatus(InteractionStrategyTaskConstants.TaskStatus.FAIL);
                task.setFailReason("处理剩余批量操作失败: " + e.getMessage());
                interactionStrategyTaskDao.saveOrUpdate(task);
            }
        }
        return atomicInteger.get();
    }

    /**
     * 执行批量操作（删除和保存）
     * 使用事务确保操作的原子性，失败时进行重试
     */
    @Transactional(rollbackFor = Exception.class)
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 200))
    public void executeBatchOperations(User user, List<String> deleteTopicIds, List<IObjectData> saveTopics) {
        try {
            log.info("开始执行批量操作，删除: {} 条记录，保存: {} 条记录",
                    deleteTopicIds.size(), saveTopics.size());

            // 执行批量删除
            if (!deleteTopicIds.isEmpty()) {
                deleteTopicsBySql(user, deleteTopicIds);
                log.info("已批量删除 {} 个建议话题", deleteTopicIds.size());
            }

            // 执行批量保存
            if (!saveTopics.isEmpty()) {
                IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext())
                        .setNotValidate(true)
                        .getContext();
                List<IObjectData> objectDataResult = dataProxy.bulkCreate(saveTopics, true, context);
                log.info("已批量保存 {} 个建议话题", objectDataResult.size());
            }
            log.info("批量操作执行成功");
        } catch (Exception e) {
            log.error("批量操作执行失败，将进行重试", e);
            // 由于@Retryable注解，异常会触发重试
            try {
                throw e;
            } catch (MetadataServiceException ex) {
                throw new RuntimeException(ex);
            }

        }
    }

    /**
     * 使用SQL直接删除话题数据
     *
     * @param user     用户信息
     * @param topicIds 需要删除的话题ID列表
     */
    private void deleteTopicsBySql(User user, List<String> topicIds) {
        if (CollectionUtils.isEmpty(topicIds)) {
            return;
        }

        try {
            // 使用Guava的Joiner将ID列表转换为以逗号分隔的字符串
            String idStr = Joiner.on("','").join(topicIds);

            // 构建DELETE SQL语句，直接删除记录
            String sql = String.format(
                    "DELETE FROM biz_activity_question WHERE tenant_id = '%s' AND id in ('%s')",
                    user.getTenantId(),
                    idStr);

            // 执行SQL语句
            specialTableMapper.setTenantId(user.getTenantId()).deleteBySql(sql);

            log.info("已通过SQL删除 {} 个建议话题", topicIds.size());
        } catch (Exception e) {
            log.error("通过SQL删除建议话题出错", e);
            throw e;
        }
    }

    /**
     * 使用SQL查询客户现有建议话题
     *
     * @param user              用户信息
     * @param usedObjectApiName 使用对象API名称
     * @param objectId          客户ID
     * @return 现有建议话题列表
     */
    private List<IObjectData> queryExistingTopics(User user, String usedObjectApiName, String objectId) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                .addFilter(ActivityQuestionConstants.QUESTION_TYPE, Operator.EQ, ActivityQuestionConstants.QuestionType.TWO.getQuestionType())
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode());
        if (Utils.ACCOUNT_API_NAME.equals(usedObjectApiName)) {
            searchTemplateQueryPlus.addFilter(CommonConstant.ACCOUNT_ID, Operator.EQ, objectId);
        } else if (CommonConstant.NEW_OPPORTUNITY_API_NAME.equals(usedObjectApiName)) {
            searchTemplateQueryPlus.addFilter(ActivityQuestionConstants.NEW_OPPORTUNITY_ID, Operator.EQ, objectId);
        }
        searchTemplateQueryPlus.setOffset(0);
        searchTemplateQueryPlus.setLimit(2000);
        searchTemplateQueryPlus.setSearchSource("db");
        searchTemplateQueryPlus.setOrders(Lists.newArrayList(new OrderBy(ActivityQuestionConstants.ORDER_FIELD, true)));
        searchTemplateQueryPlus.setPermissionType(0);
        searchTemplateQueryPlus.setNeedReturnCountNum(false);
        searchTemplateQueryPlus.setNeedReturnQuote(false);
        List<IObjectData> questionList = Optional.ofNullable(serviceFacade.findBySearchQueryIgnoreAll(user, CommonConstant.ACTIVITY_QUESTION_API_NAME, searchTemplateQueryPlus))
                .map(QueryResult::getData)
                .orElse(new ArrayList<>());
        log.info("查询{} {} 的话题数据成功，共有 {} 条记录", usedObjectApiName, objectId, questionList.size());
        return questionList;
    }

    /**
     * 收集需要处理的话题（删除和保存）
     */
    private void collectTopicsForProcessing(User user, String usedObjectApiName, String accountId, IObjectData strategy,
                                            List<IObjectData> strategyDetails,
                                            List<String> deleteTopicIds,
                                            List<IObjectData> saveTopics) {
        // 1. 查询客户现有的建议话题
        List<IObjectData> existingTopics = queryExistingTopics(user, usedObjectApiName, accountId);

        if (CollectionUtils.isEmpty(existingTopics)) {
            // 根据明细创建建议话题
            List<IObjectData> newTopics = generateNewTopics(user, usedObjectApiName, accountId, strategyDetails);
            // 添加到保存列表
            saveTopics.addAll(newTopics);
            return;
        }

        // 2. 检查是否存在相同策略
        boolean isSameStrategy = isSameStrategy(existingTopics, strategy, strategyDetails);
        if (isSameStrategy) {
            // 完全相同，跳过处理
            log.info("客户 {} 的建议话题与当前策略完全相同，跳过处理", accountId);
            return;
        }

        // 3. 将现有话题ID添加到删除列表
        List<String> topicIds = existingTopics.stream()
                .map(IObjectData::getId)
                .collect(Collectors.toList());
        deleteTopicIds.addAll(topicIds);

        // 4. 生成新的建议话题
        List<IObjectData> newTopics = generateNewTopics(user, usedObjectApiName, accountId, strategyDetails);

        // 5. 处理历史话题
        List<IObjectData> processedTopics = processHistoricalTopics(existingTopics, newTopics);

        // 6. 添加到保存列表
        saveTopics.addAll(processedTopics);
    }

    /**
     * 判断话题是否与策略相同
     */
    private boolean isSameStrategy(List<IObjectData> existingTopics, IObjectData strategy,
                                   List<IObjectData> strategyDetails) {
        // 筛选出非历史标记的话题
        List<IObjectData> nonHistoryTopics = existingTopics.stream()
                .filter(topic -> StringUtils.isEmpty(topic.get(ActivityQuestionConstants.HISTORY_FLAG, String.class)))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nonHistoryTopics)) {
            return false;
        }

        // 获取现有话题的策略ID
        String existingStrategyId = nonHistoryTopics.stream()
                .map(topic -> topic.get(ActivityQuestionConstants.STRATEGY_ID, String.class))
                .filter(StringUtils::isNotEmpty)
                .findFirst()
                .orElse(null);

        // 如果策略ID不匹配，则不是相同策略
        if (StringUtils.isBlank(existingStrategyId) || !existingStrategyId.equals(strategy.getId())) {
            return false;
        }

        // 如果策略ID匹配，继续判断策略明细的顺序是否一致
        return isTopicsCompletelyIdentical(nonHistoryTopics, strategyDetails);
    }

    /**
     * 判断话题是否完全相同
     */
    private boolean isTopicsCompletelyIdentical(List<IObjectData> existingTopics,
                                                List<IObjectData> strategyDetails) {
        if (existingTopics.size() != strategyDetails.size()) {
            return false;
        }

        // 构建库ID到序号的映射
        Map<String, Integer> detailOrderMap = new HashMap<>();
        for (IObjectData detail : strategyDetails) {
            String libraryId = detail.get(InteractionStrategyDetailConstants.LIBRARY_ID, String.class);
            Integer order = detail.get(InteractionStrategyDetailConstants.ORDER_FIELD, Integer.class);
            detailOrderMap.put(libraryId, order);
        }

        // 检查每个现有话题是否与策略明细一致
        for (IObjectData topic : existingTopics) {
            String libraryId = topic.get(ActivityQuestionConstants.LIBRARY_ID, String.class);
            Integer order = topic.get(ActivityQuestionConstants.ORDER_FIELD, Integer.class);

            // 库ID不存在或序号不一致
            if (!detailOrderMap.containsKey(libraryId) || !detailOrderMap.get(libraryId).equals(order)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 构建客户查询条件
     */
    private SearchTemplateQueryPlus buildSearchTemplateQuery(IObjectData strategy) {
        List<com.facishare.crm.task.sfa.bizfeature.model.RuleWhere> ruleWhereList = InteractionStrategyUtil.getRuleWhereListByUseRange(strategy);

        // 如果没有特定条件，使用基本条件
        if (CollectionUtils.isEmpty(ruleWhereList)) {

            SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                    .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
            searchTemplateQueryPlus.setLimit(STRATEGY_QUERY_ACCOUNT_LIMIT);
            searchTemplateQueryPlus.setOrders(Lists.newArrayList(new OrderBy(DBRecord.CREATE_TIME, true)));
            return searchTemplateQueryPlus;
        }

        // 有特定条件，构建复杂查询
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus();
        List<com.facishare.paas.metadata.api.search.Wheres> wheres = searchTemplateQueryPlus.getWheres();

        for (RuleWhere ruleWhere : ruleWhereList) {
            List<IFilter> filters = Lists.newArrayList();
            List<RuleWhere.FiltersBean> innerFilter = ruleWhere.getFilters();
            if (CollectionUtils.isEmpty(innerFilter)) {
                continue;
            }

            innerFilter.forEach(filter -> filters.add(SearchTemplateQueryPlus.getFilter(
                    filter.getFieldName(),
                    Operator.valueOf(filter.getOperator()),
                    filter.getFieldValues())));

            com.facishare.paas.metadata.api.search.Wheres wheres1 = new com.facishare.paas.metadata.api.search.Wheres();
            wheres1.setFilters(filters);
            wheres.add(wheres1);
        }

        searchTemplateQueryPlus.setWheres(wheres);
        searchTemplateQueryPlus.addFilter(DBRecord.IS_DELETED, Operator.EQ,
                String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        searchTemplateQueryPlus.setLimit(STRATEGY_QUERY_ACCOUNT_LIMIT);
        searchTemplateQueryPlus.setOrders(Lists.newArrayList(new OrderBy(DBRecord.CREATE_TIME, true)));

        return searchTemplateQueryPlus;
    }

    /**
     * 生成新的建议话题
     */
    private List<IObjectData> generateNewTopics(User user, String objectApiName, String objectId,
                                                List<IObjectData> strategyDetails) {
        ActivityMessage activityMessage = new ActivityMessage();
        activityMessage.setObjectApiName(objectApiName);
        activityMessage.setObjectId(objectId);
        return accountStrategyService.buildDataList(strategyDetails, user, activityMessage);
    }

    /**
     * 处理历史话题
     */
    private List<IObjectData> processHistoricalTopics(List<IObjectData> existingTopics, List<IObjectData> newTopics) {
        Map<String, IObjectData> libraryIdToNewTopicMap = Maps.newLinkedHashMap();

        for (IObjectData newTopic : newTopics) {
            String libraryId = newTopic.get(ActivityQuestionConstants.LIBRARY_ID, String.class);
            libraryIdToNewTopicMap.put(libraryId, newTopic);
        }
        List<IObjectData> needSaveTopics = Lists.newArrayList();
        // 处理历史话题
        for (IObjectData existTopic : existingTopics) {
            String libraryId = existTopic.get(ActivityQuestionConstants.LIBRARY_ID, String.class);
            // 检查历史话题是否在新话题中
            if (!libraryIdToNewTopicMap.containsKey(libraryId)) {
                existTopic.set(ActivityQuestionConstants.HISTORY_FLAG, ActivityQuestionConstants.HISTORY_FLAG_VALUE);
                existTopic.set(ActivityQuestionConstants.ORDER_FIELD, HISTORY_TOPIC_ORDER_VALUE);
                needSaveTopics.add(existTopic);
                continue;
            }
            // 如果历史话题存在于新话题中,更新历史话题的排序和策略ID,并替换新话题
            IObjectData newTopicData = libraryIdToNewTopicMap.get(libraryId);
            // 更新历史话题的排序字段
            existTopic.set(ActivityQuestionConstants.ORDER_FIELD,
                    newTopicData.get(ActivityQuestionConstants.ORDER_FIELD, Integer.class));
            // 更新历史话题的策略ID
            existTopic.set(ActivityQuestionConstants.STRATEGY_ID,
                    newTopicData.get(ActivityQuestionConstants.STRATEGY_ID, String.class));
            existTopic.set(ActivityQuestionConstants.HISTORY_FLAG, null);
            // 用历史话题替换新话题
            libraryIdToNewTopicMap.put(libraryId, existTopic);
        }
        needSaveTopics.addAll(libraryIdToNewTopicMap.values());
        return needSaveTopics;
    }

    /**
     * 更新任务处理进度
     */
    private void updateTaskProgress(InteractionStrategyTaskDocument task, int processedCount) {
        try {
            task.setBatchTotal(processedCount);
            interactionStrategyTaskDao.saveOrUpdate(task);
        } catch (Exception e) {
            log.error("更新任务进度出错", e);
        }
    }
}