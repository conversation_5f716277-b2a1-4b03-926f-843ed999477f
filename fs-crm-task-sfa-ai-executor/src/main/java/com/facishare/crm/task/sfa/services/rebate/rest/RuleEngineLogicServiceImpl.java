package com.facishare.crm.task.sfa.services.rebate.rest;

import com.facishare.crm.task.sfa.services.rebate.dto.AdvancedCompute;
import com.facishare.crm.task.sfa.services.rebate.dto.BaseEngine;
import com.facishare.crm.task.sfa.services.rebate.dto.ComputeAggregateValues;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 调用规则引擎业务逻辑类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleEngineLogicServiceImpl implements RuleEngineLogicService {
    @Autowired
    private RuleEngineProxy ruleEngineProxy;

    private BaseEngine.Context buildContext(User user) {
        return new BaseEngine.Context(user.getTenantId(), user.getUpstreamOwnerIdOrUserId());
    }

    private boolean logEngineException(BaseEngine.Result rst) {
        if (Objects.isNull(rst)) {
            log.warn("request rule engine service exception, result is null");
            return false;
        }
        if (!Objects.equals(rst.getErrCode(), 0)) {
            log.warn("request rule engine service exception,errorCode:{},errorMsg:{}", rst.getErrCode(),
                    rst.getErrMessage());
            return false;
        }

        return true;
    }

    @Override
    public Set<String> matchRuleCondition(User user, List<String> strategyIdList, String masterApiName,
            IObjectData masterData) {
        Set<String> matchStrategyIdList = Sets.newHashSet();
        AdvancedCompute.Arg arg = AdvancedCompute.Arg.builder()
                .macroGroupApiNames(strategyIdList)
                .data(ObjectDataExt.of(masterData).toMap())
                .apiNameDetailDataList(Maps.newHashMap())
                .build();
        arg.setContext(buildContext(user));
        AdvancedCompute.Result engineRst = ruleEngineProxy.advancedCompute(SFAHeaderUtil.getHeaders(user), arg);
        if (logEngineException(engineRst)) {
            matchStrategyIdList = engineRst.getResult().getMacroGroupApiNameRuleCodesMap()
                    .entrySet().stream().filter(map -> CollectionUtils.notEmpty(map.getValue()))
                    .collect(Collectors.toMap(map -> map.getKey().replaceAll(masterApiName, ""), Map.Entry::getValue))
                    .keySet();
        }
        return matchStrategyIdList;
    }

    @Override
    public Map<String, String> computeAggregateValues(User user,
            Set<String> aggregateRuleIds,
            String masterApiName,
            IObjectData masterData,
            List<IObjectData> detailDataList) {
        Map<String, String> aggregateValueMap = Maps.newHashMap();
        if (CollectionUtils.empty(aggregateRuleIds)) {
            return aggregateValueMap;
        }
        ComputeAggregateValues.Arg arg = ComputeAggregateValues.Arg.builder()
                .aggregateRuleIds(aggregateRuleIds)
                .data(ObjectDataExt.of(masterData).toMap())
                .apiNameDetailDataList(buildDetailMap(detailDataList))
                .build();
        arg.setContext(buildContext(user));
        ComputeAggregateValues.Result engineRst = ruleEngineProxy
                .computeAggregateValues(SFAHeaderUtil.getHeaders(user.getTenantId()), arg);
        if (logEngineException(engineRst)) {
            aggregateValueMap = engineRst.getResult();
        }

        return aggregateValueMap;
    }

    private Map<String, List<Map<String, Object>>> buildDetailMap(List<IObjectData> detailDataList) {
        if (CollectionUtils.empty(detailDataList)) {
            return Maps.newHashMap();
        }
        Map<String, List<Map<String, Object>>> result = Maps.newHashMapWithExpectedSize(1);
        result.put(detailDataList.get(0).get("object_describe_api_name",String.class), detailDataList.stream()
                .map(x -> ObjectDataExt.of(x).toMap())
                .collect(Collectors.toList()));
        return result;
    }

}
