package com.facishare.crm.task.sfa.services.rebate.rest;


import com.facishare.crm.task.sfa.services.rebate.dto.AdvancedCompute;
import com.facishare.crm.task.sfa.services.rebate.dto.ComputeAggregateValues;
import com.facishare.crm.task.sfa.services.rebate.dto.FindRuleCodesMatchedDataIds;
import com.facishare.crm.task.sfa.services.rebate.dto.QueryAggregateRuleMatchedDataIds;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * 规则引擎REST接口
 *
 * <AUTHOR>
 */
@RestResource(value = "RuleEngine", desc = "规则引擎接口", contentType = "application/json")
public interface RuleEngineProxy {

    @POST(value = "fs-paas-rule/api/rule_macro_group/advanced_compute", desc = "匹配规则条件")
    AdvancedCompute.Result advancedCompute(@HeaderMap Map<String, String> headers, @Body AdvancedCompute.Arg arg);

    @POST(value = "fs-paas-rule/api/rule_group/findRuleCodesMatchedDataIds", desc = "匹配修改量条件")
        //{"dataIndex":["pricePolicyRuleId","pricePolicyRuleId"]}
    FindRuleCodesMatchedDataIds.Result findRuleCodesMatchedDataIds(@HeaderMap Map<String, String> headers, @Body FindRuleCodesMatchedDataIds.Arg arg);

    @POST(value = "fs-paas-rule/api/aggregate_rule/compute_aggregate_values", desc = "获取聚合值")
    ComputeAggregateValues.Result computeAggregateValues(@HeaderMap Map<String, String> headers, @Body ComputeAggregateValues.Arg arg);

    @POST(value = "fs-paas-rule/api/aggregate_rule/queryAggregateRuleMatchedDataIds", desc = "获取组合聚合值中的dataIndex列表")
    QueryAggregateRuleMatchedDataIds.Result queryAggregateRuleMatchedDataIds(@HeaderMap Map<String, String> headers, @Body QueryAggregateRuleMatchedDataIds.Arg arg);
}
