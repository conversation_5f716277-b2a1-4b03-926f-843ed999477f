package com.facishare.crm.task.sfa.activitysummary.model;

import com.facishare.feeds.common.model.UrlInfoDNet;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.social.model.PaasObject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/1/22 17:02
 * @description:
 */
public interface SocialAddActionVo {
    @Data
    class Argument extends BaseObjectSaveAction.Arg implements Serializable {

        /**
         * 关联的 paas 对象, 用于内部服务
         */
        @Deprecated
        private List<PaasObject> paasObjects;
        /**
         * url 信息
         */
        private UrlInfoDNet urlInfo;
        /**
         *
         DEFAULT(0, "feeds.source.unknown", "未知类型"),
         FXXK_WEB(1, "feeds.source.web", "网页版"),
         PHONE_WEB(2, "feeds.source.mobileWeb", "手机网页版"),
         DESKTOP(3, "feeds.source.desktop", "桌面版"),
         WECHAT(4, "feeds.source.wechat", "企业微信版"),
         IPHONE_WEB(201, "feeds.source.iPhoneWeb", "iPhone网页版"),
         IPAD_WEB(202, "feeds.source.iPadWeb", "iPad网页版"),
         IPHONE(203, "feeds.source.iPhone", "iPhone"),
         IPAD(204, "feeds.source.iPad", "iPad"),
         ANDROID_WEB(301, "feeds.source.AndroidWeb", "Android网页版"),
         ANDROID_HD_WEB(302, "feeds.source.AndroidHDWeb", "Android HD网页版"),
         ANDROID(303, "feeds.source.android", "Android"),
         ANDROID_HD(304, "feeds.source.androidHD", "Android HD"),
         WINDOWS_PHONE(401, "feeds.source.WP", "Windows Phone"),
         IBSS(501, "feeds.source.ibss", "系统"),
         OPEN_API(601, "feeds.source.open", "开放平台"),
         CRM_IMPORT(701, "feeds.source.crmImported", "CRM导入"),
         PAAS_WORKFLOW(801, "feeds.source.ProcessEvent", "流程事件"),
         DRILLER(998, "feeds.source.Driller", "Driller"),
         CARDIO(999, "feeds.source.Cardio", "Cardio"),
         */
        private Integer source;
    }
}
