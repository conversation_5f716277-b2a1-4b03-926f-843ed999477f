package com.facishare.crm.task.sfa.bizfeature.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureEngineService;
import com.facishare.crm.task.sfa.model.ObjectData;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 特征引擎监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FeatureEngineListener implements
        ApplicationListener<ContextRefreshedEvent> {
    @Resource
    private FeatureEngineService featureEngineService;

    private AutoConfMQPushConsumer consumer;

    private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private Set<String> FEATURE_API_NAMES = Sets.newHashSet("AccountObj", "LeadsObj", "NewOpportunityObj",
            "NewOpportunityContactsObj","ContactObj","SalesOrderObj","QuoteObj","SaleContractObj","CompetitiveLinesObj",
            "ActiveRecordObj","NewOpportunityContactRelationshipObj");

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", getSection(),
                (MessageListenerConcurrently) (msgs, context) -> {

                    List<ObjectData.ObjectChange> objectChangeList = msgs.stream()
                            .map(it -> messageParse(it))
                            .filter(it -> null != it
                                    && "object_data".equals(it.getName())
                                    && !gray.isAllow("feature-engine-skip-tenant", it.getTenantId()))
                            .map(ObjectData::getBody)
                            .flatMap(Collection::stream)
                            .filter(it -> FEATURE_API_NAMES.contains(it.getEntityId()))
                            .collect(Collectors.toList());

                    for (ObjectData.ObjectChange msg : objectChangeList) {
                        try {
                            consumeResponse(msg);
                        } catch (Exception e) {
                            log.error("FeatureEngineListener error:{}", msg, e);
                            //throw new RuntimeException(e);
                        }
                    }
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                });
    }

    private ObjectData messageParse(MessageExt messageExt) {
        try {
            return JSON.parseObject(StringHelper.toString(messageExt.getBody()),
                    ObjectData.class);
        } catch (Exception e) {
            log.error("feature message format failed. msgId:{}, body:{}",
                    messageExt.getMsgId(), StringHelper.toString(messageExt.getBody()));
        }
        return null;
    }

    private void consumeResponse(ObjectData.ObjectChange message) {

        featureEngineService.generate(message);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    String getSection() {
        return "crm-feature-engine-consumer";
    }

}