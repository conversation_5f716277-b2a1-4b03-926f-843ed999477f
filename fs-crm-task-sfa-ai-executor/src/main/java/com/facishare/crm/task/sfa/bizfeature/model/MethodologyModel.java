package com.facishare.crm.task.sfa.bizfeature.model;

import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface MethodologyModel {
    @Data
    @Builder
    class MatchMethodology {
        private IObjectData matchFlow;
        private List<IObjectData> matchProfileList;
    }

    @Data
    @Builder
    class AllInstanceData {
        @Builder.Default
        private List<IObjectData> upMethodologyInstance = Lists.newArrayList();
        @Builder.Default
        private List<IObjectData> methodologyInstance = Lists.newArrayList();
        @Builder.Default
        private List<IObjectData> nodeInstanceList = Lists.newArrayList();
        @Builder.Default
        private List<IObjectData> taskInstanceList = Lists.newArrayList();
        @Builder.Default
        private List<IObjectData> instanceFeatureList = Lists.newArrayList();
    }
}
