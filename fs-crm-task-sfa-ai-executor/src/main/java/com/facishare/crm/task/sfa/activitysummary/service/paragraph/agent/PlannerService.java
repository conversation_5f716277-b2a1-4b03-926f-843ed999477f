package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.facishare.crm.task.sfa.activitysummary.constants.AIAgentConstants;
import com.facishare.crm.task.sfa.activitysummary.model.PlanModel;
import com.facishare.crm.task.sfa.activitysummary.model.PlanResponse;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Planner服务类
 * 负责分析文本特征，制定分段策略
 */
@Service
@Slf4j
public class PlannerService {

    @Autowired
    private CompletionsService completionsService;

    /**
     * 运行Planner组件，分析文本并制定分段策略
     * <p>
     * 提示词模板:
     * ```
     * 你是一个专业的文本分析专家，负责分析会议内容并制定分段策略。
     * <p>
     * 请分析以下会议内容，并制定一个合理的分段策略:
     * <p>
     * 会议内容:
     * ${sence_variables.custom_sence.meetingContent}
     * <p>
     * 上次会议摘要:
     * ${sence_variables.custom_sence.lastSummary}
     * <p>
     * 请考虑以下因素:
     * 1. 主题变化和连贯性
     * 2. 关键词密度
     * 3. 语义相关性
     * 4. 段落长度平衡
     * <p>
     * 输出格式要求:
     * {
     * "plan": {
     * "strategy": "分段策略名称",
     * "expected_segments": 预期段落数量,
     * "focus_points": ["关注点1", "关注点2", ...],
     * "special_handling": ["特殊处理1", "特殊处理2", ...]
     * }
     * }
     * ```
     */
    public PlanModel runPlanner(User user, String meetContent, String previousSummary) {
        try {
            AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
            arg.setApiName(AIAgentConstants.API_NAME_PLANNER);

            // 创建custom_sence子对象
            Map<String, Object> customSence = Maps.newHashMap();
            customSence.put("meetingContent", meetContent);
            customSence.put("lastSummary", StringUtils.isBlank(previousSummary) ? "": previousSummary);


            // 直接将customSence赋值给arg.setSceneVariables()
            arg.setSceneVariables(customSence);

            // 使用requestCompletionData方法直接获取对象
            PlanResponse planResponse = completionsService.requestCompletionData(
                    user, arg, AIAgentConstants.JSON_FORMAT_PLAN, PlanResponse.class);

            if (planResponse == null) {
                return null;
            }

            return planResponse.getPlan();
        } catch (Exception e) {
            log.error("Planner执行失败", e);
            return null;
        }
    }
}
