package com.facishare.crm.task.sfa.bizfeature.service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.crm.task.sfa.common.util.DateUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import com.facishare.crm.task.sfa.bizfeature.constant.ParseRuleConstants;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

@Slf4j
@Component
public class ParseActivityAggRule extends ParseCommonAggRule {

    @Override
    public String getRuleType() {
        return ParseRuleConstants.CalcMethodType.ACTIVITY_AGG.getCalcMethodType();
    }

    @Override
    protected FeatureModel.ParseExtFilter getExtFilter(User user, IObjectData afterData,
            FeatureModel.AggCondition aggCondition) {
        FeatureModel.ParseExtFilter ret = FeatureModel.ParseExtFilter.builder().hasErr(false).build();
        FeatureModel.ParseExtData parseExtData = getExtData(afterData);
        if (parseExtData == null) {
            ret.setHasErr(true);
            return ret;
        }

        String objectApiName = parseExtData.getObjectApiName();
        String objectId = parseExtData.getObjectId();
        IObjectData activityData = serviceFacade.findObjectData(user, objectId, objectApiName);
        List<String> ids = getActivityIds(user, objectApiName, activityData, aggCondition, objectId);
        if (CollectionUtils.empty(ids) || !ids.contains(objectId)) {
            ret.setHasErr(true);
            log.warn("未包含当前活动ID: {}", objectId);
        }

        IFilter filter = SearchTemplateQueryPlus.getFilter("active_record_id", Operator.IN, ids);
        ret.setFilters(Lists.newArrayList(filter));

        return ret;
    }

    private List<String> getActivityIds(User user, String objectApiName, IObjectData activityData,
            FeatureModel.AggCondition aggCondition, String objectId) {
        List<String> ids = Lists.newArrayList(objectId);
        if (FeatureModel.AggType.HISTORY.getValue().equals(aggCondition.getAggType())) {
            // 查询历史的activity
            String dimension = aggCondition.getDimension();
            SearchTemplateQueryPlus featureValueSearchQueryActivity = SearchUtil.buildBaseSearchQuery();
            featureValueSearchQueryActivity.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
            featureValueSearchQueryActivity.addFilter(dimension, Operator.EQ,
                    activityData.get(dimension, String.class));
            featureValueSearchQueryActivity.setOrders(Lists.newArrayList(new OrderBy(DBRecord.CREATE_TIME, false)));

            String dateRange = aggCondition.getDateRange();
            Date startDate = null;
            if (FeatureModel.DateRange.WEEK.getValue().equals(dateRange)) {
                startDate = DateUtils.getOneWeekAgo();
            } else if (FeatureModel.DateRange.YEAR.getValue().equals(dateRange)) {
                startDate = DateUtils.getOneYearAgo();
            } else if (FeatureModel.DateRange.QUARTER.getValue().equals(dateRange)) {
                startDate = DateUtils.getOneQuarterAgo();
            } else if (FeatureModel.DateRange.TWO_WEEK.getValue().equals(dateRange)) {
                startDate = DateUtils.getTwoWeekAgo();
            } else if (FeatureModel.DateRange.MONTH.getValue().equals(dateRange)) {
                startDate = DateUtils.getOneMonthAgo();
            }

            if (null != startDate) {
                featureValueSearchQueryActivity.addFilter(DBRecord.CREATE_TIME, Operator.GTE,
                        String.valueOf(DateUtils.getStartOfDay(startDate).getTime()));
            }

            if (dateRange.startsWith("limit")) {
                int limit = Integer.parseInt(dateRange.substring(5));
                featureValueSearchQueryActivity.setLimit(limit);
            } else {
                featureValueSearchQueryActivity.setLimit(1000);
            }
            List<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, objectApiName,
                    featureValueSearchQueryActivity).getData();
            ids = queryResult.stream().map(IObjectData::getId).collect(Collectors.toList());

        }
        return ids;
    }

}
