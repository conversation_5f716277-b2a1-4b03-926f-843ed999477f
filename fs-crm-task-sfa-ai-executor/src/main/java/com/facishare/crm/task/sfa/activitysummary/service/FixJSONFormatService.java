package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.facishare.crm.task.sfa.rest.AiRestProxy;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class FixJSONFormatService {
    private static String FIX_PROMPT_API_NAME = "";
    @Autowired
    private AiRestProxy aiRestProxy;

    public <T> List<T> getDataListFixedInvalidJSON(User user, String jsonFormat, String jsonData, Class<T> tClass, boolean retainNewline) {
        if (StringUtils.isBlank(jsonData)) {
            return Collections.emptyList();
        }
        return parseJsonArray(user, jsonFormat, jsonData, tClass, retainNewline);
    }

    public <T> T getDataFixedInvalidJSON(User user, String jsonFormat, String jsonData, Class<T> tClass) {
        if (StringUtils.isBlank(jsonData)) {
            return null;
        }

        try {
            jsonData = truncateMarkdownJSON(jsonData);
            String cleaned = removeMarkdown(jsonData);
            cleaned = removeEscapeCharacters(cleaned);
            return JSON.parseObject(cleaned, tClass);
        } catch (Exception e) {
            log.warn("JSON解析失败，尝试AI修复: {}", jsonData, e);
            String fixedJson = chatCompleteNoPrompt(user, jsonFormat, jsonData);
            return StringUtils.isBlank(fixedJson) ?
                   null :
                   JSON.parseObject(fixedJson, tClass);
        }
    }

    private <T> List<T> parseJsonArray(User user, String jsonFormat, String jsonData, Class<T> tClass, boolean retainNewline) {
        try {
            String cleanedJson = fixJSON(jsonData, retainNewline);
            return JSON.parseArray(cleanedJson, tClass);
        } catch (JSONException e) {
            log.warn("JSON解析失败，尝试AI修复: {}", jsonData, e);
            String fixedJson = chatCompleteNoPrompt(user, jsonFormat, jsonData);
            return StringUtils.isBlank(fixedJson) ? 
                   Collections.emptyList() : 
                   JSON.parseArray(fixedJson, tClass);
        }
    }

    public String fixJSON(String jsonData, boolean retainNewline) {
        jsonData = truncateMarkdownJSON(jsonData);
        String cleaned = removeMarkdown(jsonData);
        if (!retainNewline) {
            cleaned = removeEscapeCharacters(cleaned);
        }

        if (cleaned.contains("[") && cleaned.contains("]")) {
            return extractJsonArray(cleaned);
        }
        if (cleaned.contains("{") && cleaned.contains("}")) {
            return wrapObjectInArray(cleaned);
        }
        return "[]";
    }

    private String truncateMarkdownJSON(String json) {
        int startIndex = json.indexOf("```json");
        if (startIndex == -1) {
            return json;
        }
        startIndex += "```json".length();
        int endIndex = json.indexOf("```", startIndex);
        if (endIndex == -1) {
            return json;
        }
        return json.substring(startIndex, endIndex).trim();
    }

    private String removeMarkdown(String json) {
        return json.replaceAll("```json", "")
                  .replaceAll("```", "")
                  .replace("***", "")
                  .trim();
    }

    private String removeEscapeCharacters(String json) {
        return json.replaceAll("\\\\\\\\n", "")
                  .replaceAll("\\\\n", "")
                  .replaceAll("\\\\", "");
    }

    private String extractJsonArray(String json) {
        int start = json.indexOf("[");
        int end = json.lastIndexOf("]") + 1;
        return json.substring(start, end);
    }

    private String wrapObjectInArray(String json) {
        return "[" + json.replaceAll("^\"|\"$", "") + "]";
    }

    private String chatCompleteNoPrompt(User context, String jsonFormat, String jsonData) {

        return "";
    }

}
