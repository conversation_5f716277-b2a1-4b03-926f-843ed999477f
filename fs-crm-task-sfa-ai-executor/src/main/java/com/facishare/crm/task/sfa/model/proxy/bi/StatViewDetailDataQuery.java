package com.facishare.crm.task.sfa.model.proxy.bi;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

public interface StatViewDetailDataQuery extends BiCrmRestQuery {
    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends Page {
        private String viewId;
        /**
         * 维度字段id
         */
        @SerializedName("dimensionFieldID")
        @JSONField(name = "dimensionFieldID")
        private String dimensionFieldId;
        /**
         * 指标字段id
         */
        @SerializedName("measureFieldID")
        @JSONField(name = "measureFieldID")
        private String measureFieldId;
    }

    @Data
    class Result {
        private Integer code;
        private String message;
        private ResultData data;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class ResultData extends DetailResultData {}
}
