package com.facishare.crm.task.sfa.activitysummary.service;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2024/12/3 16:59
 */
@Component
public class ActivityInteractionServiceManager implements ApplicationContextAware {
    private static final Map<String, ActivityInteractionService>  interactionServiceMap= Maps.newHashMap();
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initActivityInteractionServiceMap(applicationContext);
    }
    private void initActivityInteractionServiceMap(ApplicationContext applicationContext) {
        Map<String, ActivityInteractionService> springBeanMap = applicationContext.getBeansOfType(ActivityInteractionService.class);
        springBeanMap.values().forEach(provider -> {
            if (StringUtils.isNotEmpty(provider.getObjectApiName())) {
                interactionServiceMap.put(provider.getObjectApiName(), provider);
            }
        });
    }

    public ActivityInteractionService getInteractionService(String apiName) {
        return interactionServiceMap.getOrDefault(apiName, null);
    }
}
