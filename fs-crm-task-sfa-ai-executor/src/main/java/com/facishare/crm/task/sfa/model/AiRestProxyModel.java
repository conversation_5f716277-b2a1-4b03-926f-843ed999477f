package com.facishare.crm.task.sfa.model;

import com.facishare.ai.api.model.ObjectData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface AiRestProxyModel {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg{
        private String apiName;
        private String bingObjectDataId;
        private Map<String, Object> objectData;
        private List<ObjectData> otherObjectData;
        private Map<String, Object> sceneVariables;
        private Boolean supportAdvanced;
        private Integer maxTokens;
    }
    @Data
    class Resposne{
        private Integer errCode;
        private String errMessage;
        private Result result;
    }
    @Data
    class Result{
        private String message;
        private String type;
    }


    @Data
    class FastQueryResposne{
        private Integer errCode;
        private String errMessage;
        private FastQueryResult result;
    }

    @Data
    class FastQueryResult{
        private String content;
        private List<Hit> hits;
    }
    @Data
    class Hit{
        private String id;
        private String objectApiName;
        private String content;
        private String retrievalType;
        private String titleFieldName;
        private double score;
    }

    @Data
    class FastQueryArg{
        private String ragApiName;
        private List<Filter> filters;
        private String query;
    }
    @Data
    class Filter{
        private String fieldName;
        private List<String> paasFieldValue;
        private String paasOperator;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class PromptPreViewArg {
        private String promptApiName;
        private String dataId;
        private String lang;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class PromptPreViewResponse {
        private Integer errCode;
        private String errMessage;
        private PromptPreViewResult result;
    }

    @Data
    class PromptPreViewResult {
        private String content;
    }
    @Data
    class RagResposne{
        private Boolean success;
    }

    @Data
    class CreateRagArg{
        private String apiName;
        private String bindingApiName;
        private String dataSourceType;
        private String describe;
        private String name;
        private String searchQueryInfo;
        private QueryStructure fieldMapping;
    }
    @Data
    class QueryStructure {
        private List<Map<String,String>> content;
        private Map<String,String> title;
    }
}
