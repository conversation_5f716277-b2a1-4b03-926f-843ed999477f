package com.facishare.crm.task.sfa.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyInstanceConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class ProfileUtil {

    public static Long getOneWeekAgoTimestamp() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.DAY_OF_YEAR, -7);
        return calendar.getTimeInMillis();
    }

    public static Date getNextTimestamp() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.WEEK_OF_YEAR, 1);
        return calendar.getTime();
    }

    public static String parseFilterFiled(String objectApiName) {
        String filterField = null;
        switch (objectApiName) {
            case Utils.LEADS_API_NAME:
                filterField = MethodologyInstanceConstants.LEAD_ID;
                break;
            case Utils.ACCOUNT_API_NAME:
                filterField = MethodologyInstanceConstants.ACCOUNT_ID;
                break;
            case Utils.NEW_OPPORTUNITY_API_NAME:
                filterField = MethodologyInstanceConstants.OPPORTUNITY_ID;
                break;
            default:
                break;
        }
        return filterField;
    }

    public static String parseProfileType(String objectApiName) {
        String type = null;
        switch (objectApiName) {
            case Utils.LEADS_API_NAME:
                type = "lead";
                break;
            case Utils.ACCOUNT_API_NAME:
                type = "account";
                break;
            case Utils.NEW_OPPORTUNITY_API_NAME:
                type = "opportunity";
                break;
            default:
                break;
        }
        return type;
    }

    public static String reParseProfileType(String type) {
        String objectApiName = null;
        switch (type) {
            case "lead":
                objectApiName = Utils.LEADS_API_NAME;
                break;
            case "account":
                objectApiName = Utils.ACCOUNT_API_NAME;
                break;
            case "opportunity":
                objectApiName = Utils.NEW_OPPORTUNITY_API_NAME;
                break;
            default:
                break;
        }
        return objectApiName;
    }

    public static String reParseProfileField(String type) {
        String fieldApiName = null;
        switch (type) {
            case "lead":
                fieldApiName = MethodologyInstanceConstants.LEAD_ID;
                break;
            case "account":
                fieldApiName = MethodologyInstanceConstants.ACCOUNT_ID;
                break;
            case "opportunity":
                fieldApiName = MethodologyInstanceConstants.OPPORTUNITY_ID;
                break;
            default:
                break;
        }
        return fieldApiName;
    }

    public static List<String> methodologyToObjectApiName(IObjectData methodologyInstance) {
        List<String> objectApiNames = Lists.newArrayList();
        String leadId = methodologyInstance.get(MethodologyInstanceConstants.LEAD_ID, String.class);
        if (StringUtils.isNotEmpty(leadId)) {
            objectApiNames.add(Utils.LEADS_API_NAME);
        }
        String accountId = methodologyInstance.get(MethodologyInstanceConstants.ACCOUNT_ID, String.class);
        if (StringUtils.isNotEmpty(accountId)) {
            objectApiNames.add(Utils.ACCOUNT_API_NAME);
        }
        String opportunityId = methodologyInstance.get(MethodologyInstanceConstants.OPPORTUNITY_ID, String.class);
        if (StringUtils.isNotEmpty(opportunityId)) {
            objectApiNames.add(Utils.NEW_OPPORTUNITY_API_NAME);
        }
        return objectApiNames;
    }
}
