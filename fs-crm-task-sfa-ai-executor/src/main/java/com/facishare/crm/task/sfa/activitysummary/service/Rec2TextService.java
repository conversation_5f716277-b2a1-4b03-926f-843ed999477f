package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTaskMessage;
import com.facishare.crm.task.sfa.procurement.service.NomonTask;
import com.facishare.crm.task.sfa.rest.EgressApiProxy;
import com.facishare.crm.task.sfa.rest.StoneAuthProxy;
import com.facishare.crm.task.sfa.rest.dto.EgressApiModels;
import com.facishare.crm.task.sfa.rest.dto.StoneAuthModels;
import com.facishare.crm.task.sfa.services.TermBankService;
import com.facishare.crm.task.sfa.util.HttpHeaderUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.task.sfa.activitysummary.service.ActivityUserService.ACTIVITY_USER_API_NAME;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/9 19:52
 * @description:
 */
@Slf4j
@Component
public class Rec2TextService {

    @Resource
    private StoneAuthProxy stoneAuthProxy;

    @Resource
    private EgressApiProxy egressApiProxy;

    @Resource
    private IObjectDataService objectDataService;

    @Resource
    private NomonTask nomonTask;

    @Resource
    private GDSHandler gdsHandler;

    @Resource
    ActivityMongoDao activityMongoDao;

    @Resource
    ActivityRocketProducer activityRocketProducer;

    @Autowired
    ServiceFacade serviceFacade;

    @Autowired
    private TermBankService termBankService;


    // 声道数
    private static final Integer CHANNEL_NUM = 1;
    // 是否开启说话人分离 0-不开启 1-开启
    private static final Integer SPEAKER_DIARIZATION = 1;
    // 说话人数量
    private static final Integer SPEAKER_NUMBER = 0;


    public void rec2Text(ActivityMessage message) {
        IObjectData activityData = findById(message);
        if (activityData == null || activityData.get("interaction_records") == null) {
            log.error("not found objectdata, message: {}", message);
            return;
        }
        String owner = activityData.getOwner().get(0);
        String tenantId = message.getTenantId();
        List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) activityData.get("interaction_records");
        for (Map<String, Object> interactionRecord : interactionRecords) {
            String path = (String) interactionRecord.get("path");
            String filename = (String) interactionRecord.get("filename");
            String extension = (String) interactionRecord.get("ext");
            Integer size = (Integer) interactionRecord.get("size");
            String url = getNoSignAcUrl(tenantId, gdsHandler.getEAByEI(tenantId), owner, path);
            log.info("getNoSignAcUrl, objectId:{}, path:{}, filename:{}, extension:{}, size:{}, url:{}", message.getObjectId(), path, filename, extension, size, url);
            String taskId = createAsrRecTask(url, CHANNEL_NUM, SPEAKER_DIARIZATION, SPEAKER_NUMBER);
            log.info("createAsrRecTask , objectId:{}, taskId:{}", message.getObjectId(), taskId);
            ActivityTaskMessage.Rec2TextTask rec2TextTask = ActivityTaskMessage.Rec2TextTask.builder()
                    .tenantId(tenantId)
                    .objectId(message.getObjectId())
                    .objectApiName(message.getObjectApiName())
                    .actionCode(message.getInteractiveTypes())
                    .stage(message.getStage())
                    .opId(message.getOpId())
                    .sourceId(message.getSourceId())
                    .language(message.getLanguage())
                    .taskId(taskId)
                    .build();
            //发送语音识别结果的任务，2-4分钟后收到MQ 获取识别的结果。
            nomonTask.sendActivityTextTask(rec2TextTask);
            log.info("sendActivityTextTask to nomonTask , objectId:{}, rec2TextTask:{}", message.getObjectId(), rec2TextTask);
        }
        //Object path = activityData.get("path");
        log.info("processing activityData for objectId:{}, interaction_records count:{}", 
            message.getObjectId(), 
            interactionRecords.size()
        );
    }

    /**
     * 轮询查询语音识别结果
     */

    public void pollingAsrRecTaskResult(ActivityTaskMessage.Rec2TextTask rec2TextTask) {
        if (StringUtils.isBlank(rec2TextTask.getTaskId())) {
            log.info("rec2TextTask taskId is null objectId:{}", rec2TextTask.getObjectId());
            return;
        }
        
        EgressApiModels.AsrRecTask.TaskResult result = getAsrRecTaskResult(rec2TextTask.getTaskId());
        log.info("objectId:{},taskId:{} result code:{}, status:{}, text length:{}",
                rec2TextTask.getObjectId(),
                rec2TextTask.getTaskId(),
                result.getCode(),
                result.getData() != null ? result.getData().getStatus() : "null",
                result.getData() != null && result.getData().getText() != null ? result.getData().getText().length() : 0
        );

        // 成功处理
        if (result.getData() != null &&
            EgressApiModels.TaskStatus.SUCCESS.getValue().equals(result.getData().getStatus())) {
            String fullText = saveToMongo(rec2TextTask, result);
            updateActivityText(rec2TextTask.getTenantId(), rec2TextTask.getObjectId(), rec2TextTask.getObjectApiName(), fullText);
            sendActivityToTextMessage(rec2TextTask);
        } else if (result.getData() != null &&
                 EgressApiModels.TaskStatus.FAILED.getValue().equals(result.getData().getStatus())) {
            log.error("ASR task failed permanently, objectId:{}, taskId:{},message:{}", rec2TextTask.getObjectId(), rec2TextTask.getTaskId(), result.getData().getMessage());
        } else {
            nomonTask.sendActivityTextTask(rec2TextTask);
        }
    }


    private void sendActivityToTextMessage(ActivityTaskMessage.Rec2TextTask rec2TextTask) {
            ActivityMessage activityMessage = ActivityMessage.builder()
            .tenantId(rec2TextTask.getTenantId())
            .actionCode("file2text")
            .objectId(rec2TextTask.getObjectId())
            .objectApiName(rec2TextTask.getObjectApiName())
            .interactiveTypes(rec2TextTask.getInteractiveTypes())
            .opId(rec2TextTask.getOpId())
            .stage("file2text")
            .language(rec2TextTask.getLanguage())
            .build();
        activityRocketProducer.sendActivityToTextMessage(activityMessage);
    }


    /**
     * 保存语音识别结果到MongoDB
     *
     * @param rec2TextTask 语音识别任务
     * @param result       语音识别结果
     */
    private String saveToMongo(ActivityTaskMessage.Rec2TextTask rec2TextTask, EgressApiModels.AsrRecTask.TaskResult result) {
        if (result.getData() == null) {
            log.error("语音识别结果为空, taskId:{}", rec2TextTask.getTaskId());
            return "";
        }
        StringBuilder sb = new StringBuilder();
        String text = result.getData().getText();
        String[] segments = text.split("\n");
        List<InteractiveDocument> documents = new ArrayList<>();
        Map<String, IObjectData> activityUserMap = new HashMap<>();
        long seq = 0;
        Optional<TermBankService.Session> termBankSession = termBankService.createSession(rec2TextTask.getTenantId());
        for (String segment : segments) {
            if (segment.trim().isEmpty()) {
                continue;
            }

            // 解析时间区间和内容
            // 格式如 [0:5.020,1:5.500] 内容
            int timeEndIndex = segment.indexOf("]");
            if (timeEndIndex == -1) {
                continue;
            }
            String timeRange = segment.substring(1, timeEndIndex);
            String content = segment.substring(timeEndIndex + 1).trim();
            if (termBankSession.isPresent()) {
                try {
                    content = termBankService.correct(termBankSession.get(), content);
                } catch (Exception e) {
                    log.warn("term bank correct failed", e);
                }
            }
            sb.append(content).append("\n");
            String[] times = timeRange.split(",");
            // [0:2.510,0:4.330,1] 最后一位发言人序号
            if (times.length < 2 || times.length > 3) {
                continue;
            }



            String startTime = times[0];
            String endTime = times[1];

            // 将时间格式转换为标准的小时:分钟:秒.毫秒格式
            startTime = formatTimeString(startTime);
            endTime = formatTimeString(endTime);
            
                                    
            InteractiveDocument document = new InteractiveDocument();
            document.setId(ObjectId.get());
            document.setTenantId(rec2TextTask.getTenantId());
            document.setObjectApiName(rec2TextTask.getObjectApiName());
            document.setObjectId(rec2TextTask.getObjectId());
            document.setType("VOICE_TO_TEXT");
            document.setCreateTime(System.currentTimeMillis());
            document.setStatus(result.getData().getStatus());
            document.setContent(content);
            document.setStartTime(startTime);
            document.setEndTime(endTime);
            document.setSeq(seq++);
            document.setOriginalUserName("user_1");
            document.setUserName("user_1");
            document.setNameAvaId(0);
            if (times.length == 3) {
                try {
                    // reset userName if speakerDiarization is enabled
                    int speakerIntNum = Integer.parseInt(times[2]);
                    String speakerNum = String.valueOf(speakerIntNum + 1);
                    document.setOriginalUserName("user_" + speakerNum);
                    document.setUserName("user_" + speakerNum);
                    document.setUserId(speakerNum);
                    document.setNameAvaId(speakerIntNum);

                    if (!activityUserMap.containsKey(speakerNum)) {
                        IObjectData activityUser = buildActivityUser(rec2TextTask, document);
                        activityUserMap.put(speakerNum, activityUser);
                    } else {
                        IObjectData activityUser = activityUserMap.get(speakerNum);
                        document.setActivityUserId(activityUser.getId());
                    }
                } catch (Exception e) {
                    log.warn("解析说话人编号失败, taskId:{}, segment:{}", rec2TextTask.getTaskId(), segment, e);
                }
            }
            documents.add(document);
        }
        if (!documents.isEmpty()) {
            activityMongoDao.deleteByObjectId(rec2TextTask.getTenantId(), rec2TextTask.getObjectId());
            activityMongoDao.batchInsert(rec2TextTask.getTenantId(), documents);
            log.info("保存语音识别结果到MongoDB成功, taskId:{}, segments count:{}", rec2TextTask.getTaskId(), documents.size());
        }
        if (!activityUserMap.isEmpty()) {
            serviceFacade.bulkSaveObjectData(new ArrayList<>(activityUserMap.values()), User.systemUser(rec2TextTask.getTenantId()));
        }
        return sb.toString();
    }

    private IObjectData buildActivityUser(ActivityTaskMessage.Rec2TextTask rec2TextTask, InteractiveDocument document) {
        IObjectData activityUser = new ObjectData();
        activityUser.setDescribeApiName(ACTIVITY_USER_API_NAME);
        activityUser.setCreateTime(System.currentTimeMillis());
        activityUser.setCreatedBy(User.SUPPER_ADMIN_USER_ID);
        activityUser.setTenantId(rec2TextTask.getTenantId());
        activityUser.set("active_record_id", rec2TextTask.getObjectId());
        activityUser.set("is_default_speaker", 0); // 非默认发言人
        activityUser.set("user_id", document.getUserId());
        activityUser.set("user_name", document.getUserName());
        activityUser.set("name_ava_id", document.getNameAvaId());
        activityUser.set("original_user_id", document.getUserId());
        activityUser.set("original_user_name", document.getOriginalUserName());
        activityUser.setName(document.getUserName());
        String generatedId = serviceFacade.generateId();
        activityUser.setId(generatedId);
        document.setActivityUserId(generatedId);
        return activityUser;
    }


    // getNoSignAcUrl

    /**
     * 获取无签名访问URL
     *
     * @param path              文件路径
     * @param enterpriseAccount 当前租户ea
     * @param userId            当前用户ID
     * @return 无签名访问URL
     */
    public String getNoSignAcUrl(String ei, String enterpriseAccount, String userId, String path) {
        Map<String, String> pathParams = Maps.newHashMap();
        pathParams.put("employeeAccount", enterpriseAccount);
        pathParams.put("employeeId", userId);
        pathParams.put("path", path);
        pathParams.put("expireTime", "360000");
        StoneAuthModels.GenerateDownloadUrlResponse response = stoneAuthProxy.generateDownloadUrl(HttpHeaderUtil.getHeaders(new User(ei, userId)), pathParams);

        if (response != null && response.isSuccess() && response.getCode() == 200) {
            return response.getData();
        }
        log.error("获取文件下载签名URL失败, path={}, response={}", path, response);
        return null;
    }

    /**
     * 创建语音识别任务
     *
     * @param url                音频文件URL
     * @param channelNum         音频声道数
     * @param speakerDiarization 是否开启说话人分离 0-不开启 1-开启
     * @param speakerNumber      说话人数量
     * @return 任务ID，如果创建失败返回null
     */
    public String createAsrRecTask(String url, Integer channelNum, Integer speakerDiarization, Integer speakerNumber) {
        EgressApiModels.AsrRecTask.Request request = new EgressApiModels.AsrRecTask.Request();
        request.setUrl(url);
        request.setChannelNum(channelNum);
        request.setSpeakerDiarization(speakerDiarization);
        request.setSpeakerNumber(speakerNumber);
        request.setEngineModelType("16k_zh");  // 16k_zh - 中文普通话通用引擎

        try {
            Map<String, String> headers = new HashMap<>();
            EgressApiModels.AsrRecTask.Response response = egressApiProxy.createAsrRecTask(headers, request);
            if (response != null && response.getCode() == 200 && response.getData() != null) {
                return response.getData().getTaskId();
            }
            log.error("创建语音识别任务失败, request={}, response={}", request, response);
        } catch (Exception e) {
            log.error("创建语音识别任务异常, request={}", request, e);
        }
        return null;
    }

    /**
     * 获取语音识别任务结果
     *
     * @param taskId 任务ID
     * @return 任务结果，如果获取失败返回null
     */
    public EgressApiModels.AsrRecTask.TaskResult getAsrRecTaskResult(String taskId) {
            Map<String, String> headers = new HashMap<>();
            Map<String, String> pathParams = new HashMap<>();
            pathParams.put("taskId", taskId);
            EgressApiModels.AsrRecTask.TaskResult result = egressApiProxy.getAsrRecTaskResult(headers, pathParams);
            if (result == null) {
                log.error("获取语音识别任务结果失败, taskId={}, result={}", taskId, result);
                throw new RuntimeException();
            }
            return result;
    }

    public IObjectData findById(ActivityMessage message) {
        IActionContext context = new ActionContext();
        context.setEnterpriseId(message.getTenantId());
        context.setUserId("-10000");
        IObjectData accountObj;
        try {
            accountObj = objectDataService.findById(message.getObjectId(), message.getTenantId(), context, "ActiveRecordObj");
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
        return accountObj;
    }

    /**
     * 更新活动文本
     *
     * @param tenantId      租户ID
     * @param objectId      对象ID
     * @param objectApiName 对象API名称
     * @param fullText      语音识别结果
     */
    public void updateActivityText(String tenantId, String objectId, String objectApiName, String fullText) {
        IActionContext context = new ActionContext();
        context.setEnterpriseId(tenantId);
        context.setUserId("-10000");
        IObjectData data = new ObjectData();
        data.setTenantId(tenantId);
        data.setId(objectId);
        data.setDescribeApiName(objectApiName);
        data.set("interactive_content__e", fullText);
        data.set("interactive_content", fullText);
        List<String> updateFieldList = new ArrayList<>();
        updateFieldList.add("interactive_content");
        updateFieldList.add("interactive_content__e");
        try {
            objectDataService.batchUpdateIgnoreOther(Lists.newArrayList(data), updateFieldList, context);
        } catch (MetadataServiceException e) {
            log.error("更新活动文本失败, tenantId:{}, objectId:{}, objectApiName:{}", tenantId, objectId, objectApiName, e);
        }
    }

    /**
     * 更新互动语料 interaction_records
     *
     * @param tenantId      租户ID
     * @param objectId      对象ID
     * @param objectApiName 对象API名称
     * @param path      大附件路径
     */
    public void updateActivityRecords(String tenantId, String objectId, String objectApiName, String path) {
        // 拼一个这样的 map
        /*
         * 
         * String path = (String) interactionRecord.get("path");
            String filename = (String) interactionRecord.get("filename");
            String extension = (String) interactionRecord.get("ext");
            Integer size = (Integer) interactionRecord.get("size");
         * 
         */
        Map<String, Object> map = new HashMap<>();
        map.put("path", path);
        // map.put("filename", filename);
        // map.put("extension", extension);
        // map.put("size", size);

        IActionContext context = new ActionContext();
        context.setEnterpriseId(tenantId);
        context.setUserId("-10000");
        IObjectData data = new ObjectData();
        data.setTenantId(tenantId);
        data.setId(objectId);
        data.setDescribeApiName(objectApiName);
        data.set("interaction_records", map);
        List<String> updateFieldList = new ArrayList<>();
        updateFieldList.add("interaction_records");
        try {
            objectDataService.batchUpdateIgnoreOther(Lists.newArrayList(data), updateFieldList, context);
        } catch (MetadataServiceException e) {
            log.error("更新互动语料失败, tenantId:{}, objectId:{}, objectApiName:{}", tenantId, objectId, objectApiName, e);
        }
    }


        /**
     * 将时间字符串转换为标准的小时:分钟:秒.毫秒格式
     * 支持从"分钟:秒.毫秒"或"小时:分钟:秒.毫秒"格式转换
     * 例如将"187:47.660"转换为"3:07:47.660"
     *
     * @param timeStr 原始时间字符串
     * @return 标准格式的时间字符串
     */
    private String formatTimeString(String timeStr) {
        try {
            if (timeStr == null || timeStr.trim().isEmpty()) {
                return timeStr;
            }
            
            String[] parts = timeStr.split(":");
            if (parts.length == 1) {
                // 如果只有秒，直接返回
                return "0:00:" + timeStr;
            } else if (parts.length == 2) {
                // 格式为"分钟:秒.毫秒"
                int minutes = Integer.parseInt(parts[0]);
                String seconds = parts[1];
                
                if (minutes >= 60) {
                    // 需要转换为小时:分钟:秒.毫秒
                    int hours = minutes / 60;
                    int remainingMinutes = minutes % 60;
                    return String.format("%d:%02d:%s", hours, remainingMinutes, seconds);
                } else {
                    // 分钟小于60
                    return String.format("%02d:%s", minutes, seconds);
                }
            } else if (parts.length == 3) {
                // 已经是"小时:分钟:秒.毫秒"格式，检查分钟是否需要规范化
                int hours = Integer.parseInt(parts[0]);
                int minutes = Integer.parseInt(parts[1]);
                String seconds = parts[2];
                
                if (minutes >= 60) {
                    hours += minutes / 60;
                    minutes = minutes % 60;
                }
                
                return String.format("%d:%02d:%s", hours, minutes, seconds);
            }
            
            return timeStr;
        } catch (Exception e) {
            log.error("格式化时间字符串异常, timeStr={}", timeStr, e);
            return timeStr;
        }
    }

    
}
