package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.NodeInstanceConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.TaskInstanceConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TaskInstanceDao {

    @Autowired
    private ServiceFacade serviceFacade;

    public List<IObjectData> fetchTaskInstancesByNodeInstance(User user, IObjectData nodeInstance) {
        String treePath = nodeInstance.get(NodeInstanceConstants.TREE_PATH, String.class);
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.MATCH, NodeInstanceConstants.TREE_PATH, treePath);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, NodeInstanceConstants.METHODOLOGY_INSTANCE_ID, nodeInstance.get(NodeInstanceConstants.METHODOLOGY_INSTANCE_ID, String.class));
        List<IObjectData> childNodeInstances = serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.NODE_INSTANCE, query).getData();
        if (CollectionUtils.isEmpty(childNodeInstances)) {
            return new ArrayList<>();
        }
        List<String> nodeInstanceIds = childNodeInstances.stream().map(IObjectData::getId).collect(Collectors.toList());
        query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, TaskInstanceConstants.NODE_INSTANCE_ID, nodeInstanceIds);
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.TASK_INSTANCE, query).getData();
    }
}
