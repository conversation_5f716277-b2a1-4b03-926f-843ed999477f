package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * PDF附件处理工厂类，根据灰度标识决定使用新的还是旧的PDF处理实现
 */
@Component
@Slf4j
public class PDFAttachmentFactory {

    @Resource
    private NewPDFAttachment newPDFAttachment;

    
    /**
     * 根据是否为灰度企业返回对应的PDF处理实现
     * 
     * @param tenantId 企业ID
     * @return PDF处理实现
     */
    public File2Text getPDFAttachment(String tenantId) {
        return newPDFAttachment;
    }
    
    /**
     * 执行PDF处理流程
     * 
     * @param message 活动消息
     * @param activityData 活动数据
     */
    public void execute(ActivityMessage message, IObjectData activityData) {
        File2Text handler = getPDFAttachment(message.getTenantId());
        handler.execute(message, activityData);
    }
} 