package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.bizfeature.constant.ParseRuleConstants;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dataconvert.DataConvertContext;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverter;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.common.StopWatch;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class ParseSemanticRule extends AbsParseRuleService {

    @Resource
    private CompletionsService completionsService;

    @Override
    public String getRuleType() {
        return ParseRuleConstants.CalcMethodType.SEMANTIC.getCalcMethodType();
    }

    @Override
    protected FeatureModel.ParseValueData getValue(User user, IObjectData feature, IObjectData rule,
            IObjectData afterData, IObjectDescribe dataDescribe) {
        String[] fields = this.getFields(feature);

        StopWatch stopWatch = StopWatch.createStarted(getRuleType());
        String ruleString = rule.get(ParseRuleConstants.RULE_CONTENT, String.class);
        FeatureModel.ParseLLMRule parseLLMRule = JSONObject.parseObject(ruleString, FeatureModel.ParseLLMRule.class);
        String promptApiName = parseLLMRule.getPromptApiName();
        String[] fieldNames = this.getFields(parseLLMRule.getFieldName());
        FeatureModel.ParseValueData ret = new FeatureModel.ParseValueData();
        if (StringUtils.isAnyBlank(promptApiName, fieldNames[0])) {
            log.warn("promptApiName or fieldName is null");
            return ret;
        }
        stopWatch.lap(promptApiName);
        Map<String, Object> sceneVariables = new HashMap<>();

        if (fieldNames.length > 1 && fieldNames.length == fields.length) {
            for (int i = 0; i < fieldNames.length; i++) {
                sceneVariables.put(fieldNames[i], getFiledStringValue(user, afterData, dataDescribe, fields[i]));
            }
        } else {
            StringBuilder value = new StringBuilder(getFiledStringValue(user, afterData, dataDescribe, fields[0]));
            for (int i = 1; i < fields.length; i++) {
                value.append(getFiledStringValue(user, afterData, dataDescribe, fields[i]));
            }
            String sValue = value.toString();
            if (StringUtils.isBlank(sValue)) {
                log.warn("field value is null,fields:{}", fields[0]);
                return ret;
            }

            sceneVariables.put(fieldNames[0], sValue);
        }

        if (StringUtils.isNotBlank(parseLLMRule.getComparison())) {
            sceneVariables.put(parseLLMRule.getComparison(), comparisonFieldValue(user, feature, rule, afterData));
        }

        AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
                .apiName(promptApiName)
                .sceneVariables(sceneVariables)
                .build();
        String result = completionsService.requestCompletion(user, arg);
        ret.setValue(result);
        Map<String, Object> srcValue = getSrcObjectMap(afterData, fields);
        ret.setObjectApiName(afterData.getDescribeApiName());
        ret.setObjectId(afterData.getId());
        ret.setTriggerValue(srcValue);
        stopWatch.lap("requestCompletion");
        stopWatch.logSlow(500);
        return ret;
    }

    private String  getFiledStringValue(User user, IObjectData afterData, IObjectDescribe dataDescribe, String fieldApiName) {
        IFieldDescribe fieldDescribe = dataDescribe.getFieldDescribe(fieldApiName);
        if (CONVERT_FIELD_TYPE.contains(fieldDescribe.getType())) {
            FieldDataConverter fieldDataConverter = fieldDataConverterManager.getFieldDataConverter(fieldDescribe.getType());
            return fieldDataConverter.convertFieldData(afterData, fieldDescribe, DataConvertContext.of(user));
        }else {
            return afterData.get(fieldApiName, String.class);
        }
    }

}
