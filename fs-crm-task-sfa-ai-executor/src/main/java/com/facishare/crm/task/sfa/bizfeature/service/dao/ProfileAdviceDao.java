package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileAdviceConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProfileAdviceDao {

    @Autowired
    private ServiceFacade serviceFacade;
    public List<IObjectData> fetchProfileAdviceByProfileIdAndDimensionIds(User user, String type, List<String> profileIds, List<String> featureDimensionIds,String tenantType) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, ProfileAdviceConstants.PROFILE_ID, profileIds);
        if (Strings.isNotEmpty(tenantType)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileAdviceConstants.TYPE, tenantType);
        }

        if (type.equals(ProfileAdviceConstants.RangeType.PROFILE.getValue())) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileAdviceConstants.RANGE_TYPE, ProfileAdviceConstants.RangeType.PROFILE.getValue());
            SearchTemplateQueryExt.of(query).addFilter(Operator.IS, ProfileAdviceConstants.FEATURE_DIMENSION_ID, new ArrayList<>());
        } else {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileAdviceConstants.RANGE_TYPE, ProfileAdviceConstants.RangeType.DIMENSION.getValue());
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, ProfileAdviceConstants.FEATURE_DIMENSION_ID, featureDimensionIds);
        }

        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.PROFILE_ADVICE, query).getData();
    }
}
