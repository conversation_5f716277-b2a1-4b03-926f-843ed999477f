package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.change.set.util.JsonUtil;
import com.facishare.crm.task.sfa.activitysummary.service.AIKnowledgeBaseService;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureCrmNoteContext;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileAdviceModel;
import com.facishare.crm.task.sfa.bizfeature.service.dao.*;
import com.facishare.crm.task.sfa.bizfeature.util.NodeFinishUtil;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.util.FeatureBaseDataUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.IdUtil;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.bizfeature.constant.PrompotConstants.PROMPOT_INFO;

@Component
@Slf4j
public class ProfileAdviceService {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ProfileAdviceDao profileAdviceDao;
    @Resource
    ProfileItemScoreDao profileItemScoreDao;
    @Resource
    ProfileProsConsDao profileProsConsDao;
    @Resource
    NodeInstanceDao nodeInstanceDao;
    @Resource
    TaskInstanceDao taskInstanceDao;
    @Resource
    AIKnowledgeBaseService aiKnowledgeBaseService;
    @Resource
    private CompletionsService completions;
    @Resource
    TaskFeatureDao taskFeatureDao;
    @Resource
    FeatureDao featureDao;
    @Resource
    KnowledgeDocumentDao knowledgeDocumentDao;
    @Resource
    ProfileDao profileDao;
    @Resource
    MethodologyInstanceDao methodologyInstanceDao;
    @Resource
    InstanceFeatureDao instanceFeatureDao;
    @Resource
    ProfileScoreService profileScoreService;

    public void generateAdvice(ProfileAdviceModel param, FeatureCrmNoteContext featureCrmNoteContext) {
        StopWatch stopWatch = StopWatch.createStarted("generateAdvice" + param.getProfileId());
        String profileId = param.getProfileId();
        if (Strings.isNullOrEmpty(profileId)) {
            log.error("Profile ID is null or empty.");
            return;
        }

        // 1. 查询画像及基础信息
        User user = User.systemUser(param.getTenantId());
        IObjectData profile = param.getProfile();
        String methodologyId = profile.get(ProfileConstants.METHODOLOGY_ID, String.class);
        String type = profile.get(ProfileConstants.TYPE, String.class);
        String objectId = param.getObjectId();

        if (Strings.isNullOrEmpty(methodologyId) || Strings.isNullOrEmpty(type) || Strings.isNullOrEmpty(objectId)) {
            log.error("Missing required fields in profile data");
            featureCrmNoteContext.setSucess(false);
            return;
        }

        IObjectData methodology = serviceFacade.findObjectData(user, methodologyId, FeatureConstants.METHODOLOGY);
        if (methodology == null) {
            log.error("methodology not found for ID: {}", methodologyId);
            featureCrmNoteContext.setSucess(false);
            return;
        }

        String objectApiLabel = I18N.text(featureCrmNoteContext.getApiName() + ".attribute.self.display_name");
        // 2. 通用流程按阶段查找任务生成建议
        processAdvice(user, profile, methodology, objectId, stopWatch,objectApiLabel,featureCrmNoteContext.getApiName());

        stopWatch.logSlow(500);
    }

    /**
     * 生成当前阶段建议
     */
    private void processAdvice(User user, IObjectData profile, IObjectData methodology, String objectId, StopWatch stopWatch,String objectApiLabel,String objectApiName) {
        // 4.1 查询所有分项得分
        List<IObjectData> profileItemScoreList = profileItemScoreDao.fetchProfileItemScoresByProfileId(user, profile.getId());
        if (CollectionUtils.isEmpty(profileItemScoreList)) {
            log.error("No dimension scores found for profile ID: {}", profile.getId());
            return;
        }
        if (methodology.get(MethodologyConstants.TYPE, String.class).equals(MethodologyConstants.Type.PROFILE.getType())) {
            generateMethodologyAdvice(user, profile, methodology, objectId, stopWatch, objectApiName,objectApiLabel, profileItemScoreList);
        } else {
            generateFlowAdvice(user, profile, methodology, objectId, stopWatch, objectApiLabel, profileItemScoreList);
        }
    }

    private void generateFlowAdvice(User user, IObjectData profile, IObjectData methodology, String objectId, StopWatch stopWatch, String objectApiLabel, List<IObjectData> profileItemScoreList) {
        // 3.1 获取阶段实例
        String methodologyInstanceId = profile.get(ProfileConstants.METHODOLOGY_INSTANCE_ID, String.class);
        List<IObjectData> inProgressNodeInstances = nodeInstanceDao.fetchNodeInstancesByMethodologyAndObjectIdWithOrder(user, methodology, methodologyInstanceId, objectId);
        if (CollectionUtils.isEmpty(inProgressNodeInstances)) {
            log.error("No node instance found");
            return;
        }
        String currentNodeId = StringUtil.EMPTY;
        String nextNodeId = StringUtil.EMPTY;
        IObjectData inProgressNodeInstance = null;
        for (IObjectData instance : inProgressNodeInstances) {
            String nodeStatus = instance.get(NodeInstanceConstants.STATUS, String.class);
            if (nodeStatus.equals(NodeInstanceConstants.StatusType.PROGRESS.getStatusType())) {
                currentNodeId = instance.get(NodeInstanceConstants.NODE_ID, String.class);
                inProgressNodeInstance = instance;
                if (inProgressNodeInstances.indexOf(instance) != inProgressNodeInstances.size() - 1) {
                    nextNodeId = inProgressNodeInstances.get(inProgressNodeInstances.indexOf(instance) + 1).get(NodeInstanceConstants.NODE_ID, String.class);
                }
            }
        }
        stopWatch.lap("fetchNodeInstancesByMethodologyAndObjectIdWithOrder");

        if (inProgressNodeInstance == null) {
            log.error("No in-progress node instance found");
            return;
        }

        // 3.2 查询当前阶段得分
        IObjectData currentNodeScore = profileItemScoreDao.fetchProfileItemScoreByNodeId(user, profile.getId(), currentNodeId);
        if (currentNodeScore == null) {
            log.error("No score found for current node: {}", currentNodeId);
            return;
        }

        BigDecimal score = currentNodeScore.get(ProfileItemScoreConstants.SCORE, BigDecimal.class);

        if (Strings.isNullOrEmpty(currentNodeId)) {
            log.error("Target node ID is null or empty");
            return;
        }
        log.info("profile_advice_info_currentNodeId:{}", currentNodeId);
        // 3.4 生成并保存建议
        List<IObjectData> taskInstances = taskInstanceDao.fetchTaskInstancesByNodeInstance(user, inProgressNodeInstance);
        log.info("profile_advice_info_taskInstances:{}", taskInstances);
        List<IObjectData> unCompleteTaskInstances = taskInstances.stream()
                .filter(x -> !x.get(TaskInstanceConstants.STATUS, String.class).equals(TaskInstanceConstants.StatusType.COMPLETED.getStatusType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unCompleteTaskInstances)) {
            log.error("no unCompleteTaskInstances");
            return;
        }
        List<String> taskIds = unCompleteTaskInstances.stream()
                .distinct()
                .map(x -> x.get(TaskInstanceConstants.TASK_ID, String.class))
                .collect(Collectors.toList());
        log.info("profile_advice_info_taskIds:{}", taskIds);
        stopWatch.lap("fetchTaskInstancesByNodeInstance");
        List<IObjectData> tasks = serviceFacade.findObjectDataByIds(user.getTenantId(), taskIds, FeatureConstants.METHODOLOGY_TASK);
        saveAdviceByTenantType(user, profile, methodology, tasks, ProfileAdviceConstants.Type.FS.getValue(), stopWatch, objectApiLabel);
        saveAdviceByTenantType(user, profile, methodology, tasks, ProfileAdviceConstants.Type.CONS.getValue(), stopWatch, objectApiLabel);

        updateProfileNode(user, profile, currentNodeId, nextNodeId, score);

        //生成维度建议
        List<IObjectData> instanceFeatureList = instanceFeatureDao.fetchInstanceFeaturesByMethodologyInstanceId(user, methodologyInstanceId);
        List<String> instanceFeatureTaskIds = instanceFeatureList.stream()
                .map(x -> x.get(InstanceFeatureConstants.TASK_ID, String.class))
                .collect(Collectors.toList());
        //tasks = serviceFacade.findObjectDataByIds(user.getTenantId(), taskIds, FeatureConstants.METHODOLOGY_TASK);
        List<IObjectData> filterdTasks = tasks.stream()
                .filter(x -> instanceFeatureTaskIds.contains(x.getId()))
                .collect(Collectors.toList());
        List<IObjectData> adviceDatasAll = new ArrayList<>();
        List<String> targetIds = new ArrayList<>();
        List<String> featureDimensionIds = profileItemScoreList.stream()
                .map(x -> x.get(ProfileItemScoreConstants.FEATURE_DIMENSION_ID, String.class))
                .collect(Collectors.toList());
        List<IObjectData> features = featureDao.fetchFeaturesByDimensionIds(user, featureDimensionIds);
        Map<String,List<IObjectData>> dimensionFeaturesMap =features.stream()
                .collect(Collectors.groupingBy(x -> x.get(FeatureConstants.FEATURE_DIMENSION_1, String.class)));
        for (IObjectData profileItemScore : profileItemScoreList) {
            String itemType = profileItemScore.get(ProfileItemScoreConstants.TYPE, String.class);
            if (itemType.equals(ProfileItemScoreConstants.Type.NODE.getValue())) {
                //  跳过流程+节点，不生成建议信息
                continue;
            }
            String featureDimensionId = profileItemScore.get(ProfileItemScoreConstants.FEATURE_DIMENSION_ID, String.class);
            targetIds.add(featureDimensionId);

            log.info("profile_advice_info_featureDimensionId:{}", featureDimensionId);
            // 根据维度ID过滤特征
            List<IObjectData> dimensionFeatures = dimensionFeaturesMap.get(featureDimensionId);
            List<String> featureIds = dimensionFeatures.stream()
                    .map(DBRecord::getId)
                    .collect(Collectors.toList());
            List<IObjectData> filteredInstanceFeatureList = instanceFeatureList.stream()
                    .filter(instanceFeature -> featureIds.contains(instanceFeature.get(TaskFeatureConstants.FEATURE_ID, String.class)))
                    .collect(Collectors.toList());

            List<String> itemTaskIds = filteredInstanceFeatureList.stream()
                    .distinct()
                    .map(x -> x.get(TaskFeatureConstants.TASK_ID, String.class))
                    .collect(Collectors.toList());
            List<IObjectData> itemTasks = filterdTasks.stream().filter(task -> itemTaskIds.contains(task.getId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(itemTaskIds)) {
                log.info("profile_pros_info_featureDimensionId:{},taskIds is empty", featureDimensionId);
                continue;
            }
            log.info("profile_pros_info_featureDimensionId:{},taskIds:{}", featureDimensionId, itemTasks);
            stopWatch.lap("loopGetTasks");
            adviceDatasAll.addAll(getAdviceByTasks(user, profile, methodology, itemTasks
                    , ProfileAdviceConstants.RangeType.DIMENSION.getValue(), featureDimensionId, ProfileAdviceConstants.Type.FS.getValue(), objectApiLabel));
            adviceDatasAll.addAll(getAdviceByTasks(user, profile, methodology, itemTasks
                    , ProfileAdviceConstants.RangeType.DIMENSION.getValue(), featureDimensionId, ProfileAdviceConstants.Type.CONS.getValue(), objectApiLabel));
            stopWatch.lap("getUdefAdviceByTasks");
        }
        saveItemAdvice(user, profile, targetIds, adviceDatasAll);
        stopWatch.lap("bulkSaveObjectData");
    }

    private void generateMethodologyAdvice(User user, IObjectData profile, IObjectData methodology, String objectId, StopWatch stopWatch, String objectApiName
            , String objectApiLabel, List<IObjectData> profileItemScoreList) {
        //判断是否关联了流程
        IObjectData methodologyInstance = getRelatedMethodology(user, methodology.getId(), objectId, objectApiName);
        if (methodologyInstance != null) { //关联了流程，走流程生成建议模式
            String flowMethodologyInstanceId = methodologyInstance.getId();
            String methodologyId = methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_ID, String.class);
            methodology = serviceFacade.findObjectData(user, methodologyId, FeatureConstants.METHODOLOGY);
            log.info("generateMethodologyAdvice methodologyInstanceId:{},methodologyId,{}", flowMethodologyInstanceId, methodologyId);
            List<String> taskIds = getProgressNodeUnCompletedTaskIds(user, methodology, objectId, flowMethodologyInstanceId);
            List<IObjectData> tasks = serviceFacade.findObjectDataByIds(user.getTenantId(), taskIds, FeatureConstants.METHODOLOGY_TASK);
            log.info("profile_advice_info_taskIds:{}", taskIds);
            saveAdviceByTenantType(user, profile, methodology, tasks, ProfileAdviceConstants.Type.FS.getValue(), stopWatch, objectApiLabel);
            saveAdviceByTenantType(user, profile, methodology, tasks, ProfileAdviceConstants.Type.CONS.getValue(), stopWatch, objectApiLabel);

            //维度建议
            List<IObjectData> adviceDatasAll = new ArrayList<>();
            List<String> targetIds = new ArrayList<>();
            String methodologyInstanceId = profile.get(ProfileConstants.METHODOLOGY_INSTANCE_ID, String.class);
            List<IObjectData> instanceFeatureList = instanceFeatureDao.fetchInstanceFeaturesByMethodologyInstanceId(user, methodologyInstanceId);

            List<String> featureIds = instanceFeatureList.stream()
                    .map(x -> x.get(InstanceFeatureConstants.FEATURE_ID, String.class)).collect(Collectors.toList());
            List<IObjectData> taskFeatureList = taskFeatureDao.fetchTaskFeaturesByFeatureIds(user, featureIds);
            for (IObjectData profileItemScore : profileItemScoreList) {
                String nodeId = profileItemScore.get(ProfileItemScoreConstants.NODE_ID, String.class);
                targetIds.add(nodeId);

                List<String> taskFeatureTaskIds = taskFeatureList.stream()
                        .map(x -> x.get(TaskFeatureConstants.TASK_ID, String.class))
                        .collect(Collectors.toList());
                List<IObjectData> nodeInstanceFeatureList = instanceFeatureList.stream()
                        .filter(x -> x.get(InstanceFeatureConstants.NODE_ID, String.class).equals(nodeId))
                        .collect(Collectors.toList());
                List<String> filteredTaskIds = nodeInstanceFeatureList.stream()
                        .filter(x -> taskFeatureTaskIds.contains(x.get(TaskFeatureConstants.TASK_ID, String.class)))
                        .map(x -> x.get(TaskFeatureConstants.TASK_ID, String.class))
                        .collect(Collectors.toList());
                // 3.1 获取阶段实例
                List<String> itemTaskIds = new ArrayList<>(filteredTaskIds);
                itemTaskIds.retainAll(taskIds);
                if (CollectionUtils.isEmpty(itemTaskIds)) {
                    continue;
                }
                List<IObjectData> itemTasks = tasks.stream().filter(x -> itemTaskIds.contains(x.getId())).collect(Collectors.toList());
                log.info("profile_pros_info_nodeId:{},itemTaskIds:{}", nodeId, itemTaskIds);
                stopWatch.lap("loopGetTasks");
                adviceDatasAll.addAll(getAdviceByTasks(user, profile, methodology, itemTasks
                        , ProfileAdviceConstants.RangeType.DIMENSION.getValue(), nodeId, ProfileAdviceConstants.Type.FS.getValue(), objectApiLabel));
                adviceDatasAll.addAll(getAdviceByTasks(user, profile, methodology, itemTasks
                        , ProfileAdviceConstants.RangeType.DIMENSION.getValue(), nodeId, ProfileAdviceConstants.Type.CONS.getValue(), objectApiLabel));
                stopWatch.lap("getUdefAdviceByTasks");
            }
            saveItemAdvice(user, profile, targetIds, adviceDatasAll);
            stopWatch.lap("bulkSaveObjectData");
        } else {  //未关联了流程，走流程生成建议模式
            String methodologyInstanceId = profile.get(ProfileConstants.METHODOLOGY_INSTANCE_ID, String.class);
            List<IObjectData> instanceFeatureList = instanceFeatureDao.fetchInstanceFeaturesByMethodologyInstanceId(user, methodologyInstanceId);

            List<String> featureIds = instanceFeatureList.stream()
                    .map(x -> x.get(InstanceFeatureConstants.FEATURE_ID, String.class)).collect(Collectors.toList());
            List<IObjectData> taskFeatures = taskFeatureDao.fetchTaskFeaturesByFeatureIds(user, featureIds);
            List<IObjectData> featureScoreList = profileScoreService.getTaskFeatureScores(user, methodology.getId(), objectApiName, objectId, taskFeatures);
            Map<String, List<IObjectData>> taskIdToTaskFeatureMap = taskFeatures.stream()
                    .collect(Collectors.groupingBy(x -> x.get(TaskFeatureConstants.TASK_ID, String.class)));

            List<String> taskIds = new ArrayList<>();
            for (Map.Entry<String, List<IObjectData>> entry : taskIdToTaskFeatureMap.entrySet()) {
                String taskId = entry.getKey();
                boolean finish = NodeFinishUtil.finishNode(NodeFinishUtil.TASK_FINISH_SCORE, taskIdToTaskFeatureMap.get(taskId), featureScoreList);
                if (finish) {
                    taskIds.add(taskId);
                }
            }
            List<IObjectData> tasks = serviceFacade.findObjectDataByIds(user.getTenantId(), taskIds, FeatureConstants.METHODOLOGY_TASK);
            saveAdviceByTenantType(user, profile, methodology, tasks, ProfileAdviceConstants.Type.FS.getValue(), stopWatch, objectApiLabel);
            saveAdviceByTenantType(user, profile, methodology, tasks, ProfileAdviceConstants.Type.CONS.getValue(), stopWatch, objectApiLabel);



            List<IObjectData> adviceDatasAll = new ArrayList<>();
            List<String> targetIds = new ArrayList<>();
            for (IObjectData profileItemScore : profileItemScoreList) {
                String nodeId = profileItemScore.get(ProfileItemScoreConstants.NODE_ID, String.class);
                targetIds.add(nodeId);
                List<IObjectData> nodeInstanceFeatureList = instanceFeatureList.stream()
                        .filter(x -> x.get(InstanceFeatureConstants.NODE_ID, String.class).equals(nodeId))
                        .collect(Collectors.toList());
                List<String> nodeFeatureIds = nodeInstanceFeatureList.stream()
                        .map(x -> x.get(InstanceFeatureConstants.FEATURE_ID, String.class)).collect(Collectors.toList());
                List<IObjectData> nodeTaskFeatures = taskFeatures.stream()
                        .filter(x -> nodeFeatureIds.contains(x.get(TaskFeatureConstants.FEATURE_ID, String.class)))
                        .collect(Collectors.toList());

                taskIdToTaskFeatureMap = nodeTaskFeatures.stream()
                        .collect(Collectors.groupingBy(x -> x.get(TaskFeatureConstants.TASK_ID, String.class)));
                List<String> itemTaskIds = new ArrayList<>();
                for (Map.Entry<String, List<IObjectData>> entry : taskIdToTaskFeatureMap.entrySet()) {
                    String taskId = entry.getKey();
                    boolean finish = NodeFinishUtil.finishNode(NodeFinishUtil.TASK_FINISH_SCORE, taskIdToTaskFeatureMap.get(taskId), featureScoreList);
                    if (finish) {
                        itemTaskIds.add(taskId);
                    }
                }

                if (CollectionUtils.isEmpty(itemTaskIds)) {
                    continue;
                }
                List<IObjectData> itemTasks = tasks.stream().filter(x -> itemTaskIds.contains(x.getId())).collect(Collectors.toList());
                log.info("profile_pros_info_nodeId:{},taskIds:{}", nodeId, taskIds);
                stopWatch.lap("loopGetTasks");
                adviceDatasAll.addAll(getAdviceByTasks(user, profile, methodology, itemTasks
                        , ProfileAdviceConstants.RangeType.DIMENSION.getValue(), nodeId, ProfileAdviceConstants.Type.FS.getValue(), objectApiLabel));
                adviceDatasAll.addAll(getAdviceByTasks(user, profile, methodology, itemTasks
                        , ProfileAdviceConstants.RangeType.DIMENSION.getValue(), nodeId, ProfileAdviceConstants.Type.CONS.getValue(), objectApiLabel));
                stopWatch.lap("getUdefAdviceByTasks");
            }
            saveItemAdvice(user, profile, targetIds, adviceDatasAll);
            stopWatch.lap("bulkSaveObjectData");
        }
    }

    private IObjectData getRelatedMethodology(User user, String methodologyId, String objectId,String objectApiName) {
        List<IObjectData> methodologyInstances = methodologyInstanceDao.fetchInstanceByMethodologyAndObject(user, Lists.newArrayList(methodologyId), objectId,objectApiName);
        if (CollectionUtils.isEmpty(methodologyInstances)) {
            return null;
        }
        IObjectData methodologyInstance = methodologyInstances.get(0);
        List<String> relatedMethodologyIds = methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_IDS, List.class);
        if (CollectionUtils.isEmpty(relatedMethodologyIds)) {
            return null;
        }
        List<IObjectData> relatedMethodologyInstances = methodologyInstanceDao.fetchInstanceByMethodologyAndObject(user, relatedMethodologyIds, objectId, objectApiName);
        if (CollectionUtils.isEmpty(relatedMethodologyInstances)) {
            return null;
        }
        List<IObjectData> filteredRelatedMethodologyInstances = relatedMethodologyInstances.stream()
                .filter(x -> x.get(MethodologyInstanceConstants.STATUS, String.class).equals(MethodologyInstanceConstants.StatusType.ENABLE.getStatusType()))
                .filter(x -> x.get(MethodologyInstanceConstants.TYPE, String.class).equals(MethodologyInstanceConstants.Type.FLOW.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredRelatedMethodologyInstances)) {
            return null;
        }
        return filteredRelatedMethodologyInstances.get(0);
    }

    private void saveItemAdvice(User user, IObjectData profile, List<String> targetIds, List<IObjectData> adviceDatasAll) {
        if (CollectionUtils.isNotEmpty(targetIds)) {
            List<IObjectData> existAdviceList = profileAdviceDao.fetchProfileAdviceByProfileIdAndDimensionIds(user
                    , ProfileAdviceConstants.RangeType.DIMENSION.getValue(), Lists.newArrayList(profile.getId()), targetIds, null);

            if (!CollectionUtils.isEmpty(existAdviceList)) {
                serviceFacade.bulkDeleteDirect(existAdviceList, user);
            }
        }

        if (!CollectionUtils.isEmpty(adviceDatasAll)) {
            serviceFacade.bulkSaveObjectData(adviceDatasAll, user);
        }
    }

    private List<String> getProgressNodeUnCompletedTaskIds(User user, IObjectData methodology, String objectId, String methodologyInstanceId) {
        List<IObjectData> inProgressNodeInstances = nodeInstanceDao.fetchNodeInstancesByMethodologyAndObjectIdWithOrder(user, methodology, methodologyInstanceId, objectId);
        if (CollectionUtils.isEmpty(inProgressNodeInstances)) {
            log.error("No node instance found");
            return null;
        }
        IObjectData inProgressNodeInstance = null;
        for (IObjectData instance : inProgressNodeInstances) {
            String nodeStatus = instance.get(NodeInstanceConstants.STATUS, String.class);
            if (nodeStatus.equals(NodeInstanceConstants.StatusType.PROGRESS.getStatusType())) {
                inProgressNodeInstance = instance;
            }
        }
        // 3.4 生成并保存建议
        List<IObjectData> taskInstances = taskInstanceDao.fetchTaskInstancesByNodeInstance(user, inProgressNodeInstance);
        log.info("profile_advice_info_taskInstances:{}", taskInstances);
        List<IObjectData> unCompleteTaskInstances = taskInstances.stream()
                .filter(x -> !x.get(TaskInstanceConstants.STATUS, String.class).equals(TaskInstanceConstants.StatusType.COMPLETED.getStatusType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unCompleteTaskInstances)) {
            log.error("no unCompleteTaskInstances");
            return null;
        }
        List<String> taskIds = unCompleteTaskInstances.stream()
                .distinct()
                .map(x -> x.get(TaskInstanceConstants.TASK_ID, String.class))
                .collect(Collectors.toList());
        return taskIds;
    }

    private void saveAdviceByTenantType(User user, IObjectData profile, IObjectData methodology, List<IObjectData> tasks
            , String tenantType, StopWatch stopWatch,String objectApiLabel) {
        List<String> profileIds = new ArrayList<>();
        profileIds.add(profile.getId());

        List<IObjectData> adviceDatas = getAdviceByTasks(user, profile, methodology, tasks, ProfileAdviceConstants.RangeType.PROFILE.getValue(), null, tenantType,objectApiLabel);
        stopWatch.lap("getAdviceByTasks");
        List<IObjectData> existAdviceList = profileAdviceDao.fetchProfileAdviceByProfileIdAndDimensionIds(user
                , ProfileAdviceConstants.RangeType.PROFILE.getValue(), profileIds, null, tenantType);

        if (!CollectionUtils.isEmpty(existAdviceList)) {
            serviceFacade.bulkDeleteDirect(existAdviceList, user);
        }
        List<IObjectData> allObjectData = new ArrayList<>();
        if (!CollectionUtils.isEmpty(adviceDatas)) {
            allObjectData.addAll(adviceDatas);
            serviceFacade.bulkSaveObjectData(allObjectData, user);
        }
        stopWatch.lap("bulkSaveObjectData");
    }

    private List<IObjectData> getAdviceByTasks(User user, IObjectData profile, IObjectData methodology, List<IObjectData> tasks
            ,String rangeType,String targetId,String tenantType,String objectApiLabel) {
        Map<String, List<String>> taskKnowledgeMap = new HashMap<>();
        String targetNodeAdvice = fetchNodeAdvice(user, profile.getId(), methodology, tasks, tenantType, taskKnowledgeMap, objectApiLabel);
        if (Strings.isNullOrEmpty(targetNodeAdvice)) {
            return new ArrayList<>();
        }
        JSONObject targetNodeAdviceJson = null;
        try {
            targetNodeAdviceJson = JsonUtil.parseObject(targetNodeAdvice);
        } catch (Exception e) {
            log.error("Failed to parse targetNodeAdvice as JSON,profileID:{}, value: {}", profile.getId(), targetNodeAdvice, e);
            return new ArrayList<>();
        }
        List<IObjectData> adviceDatas = new ArrayList<>();
        int count = 1;
        for (IObjectData task : tasks) {
            if (!targetNodeAdviceJson.containsKey(task.getId())) {
                continue;
            }
            String taskAdvice = targetNodeAdviceJson.getString(task.getId());
            if (Strings.isNullOrEmpty(taskAdvice)) {
                continue;
            }
            if (!Strings.isNullOrEmpty(taskAdvice) && taskAdvice.contains("advice")) {
                JSONObject jsonObj = JsonUtil.parseObject(taskAdvice);
                taskAdvice = jsonObj.getString("advice");
            }
            List<String> knowledgeIds = taskKnowledgeMap.get(task.getId());
            IObjectData adviceData = BuildAdvice(user, profile.getId(), rangeType, targetId, taskAdvice, task.getId(), tenantType, count, knowledgeIds);
            adviceDatas.add(adviceData);
            count++;
        }

        return adviceDatas;
    }

    private void updateProfileNode(User user, IObjectData profile, String currentNodeId, String nextNodeId, BigDecimal score) {
        //更新画像节点数据
        List<String> updateFields = new ArrayList<>();
        if (!Strings.isNullOrEmpty(currentNodeId)) {
            profile.set(ProfileConstants.CURRENT_NODE_ID, currentNodeId);
            updateFields.add(ProfileConstants.CURRENT_NODE_ID);
        }
        if (!Strings.isNullOrEmpty(nextNodeId)) {
            profile.set(ProfileConstants.NEXT_NODE_ID, nextNodeId);
            updateFields.add(ProfileConstants.NEXT_NODE_ID);
        }
        if (!Objects.isNull(score)) {
            profile.set(ProfileConstants.CURRENT_SCORE, score);
            updateFields.add(ProfileConstants.CURRENT_SCORE);
        }
        serviceFacade.batchUpdateByFields(user, Lists.newArrayList(profile), updateFields);
    }

    private String fetchNodeAdvice(User user,String profileId, IObjectData methodology, List<IObjectData> tasks, String tenantType, Map<String
            , List<String>> taskKnowledgeMap,String objectApiLabel) {
        String tenantId = user.getTenantId();
        String ragApiName = PrompotConstants.RAG_KNOWLEDGE_USER;
        String filterName = "title";
        if (tenantType.equals(ProfileAdviceConstants.Type.FS.getValue())) {
            tenantId = GrayUtils.getKnowledgeBaseTenant();//从配置取
            ragApiName = PrompotConstants.RAG_KNOWLEDGE_BASE;
            //filterName = "name";
        }
        List<AiRestProxyModel.Filter> filters = new ArrayList<>();
        AiRestProxyModel.Filter filter = new AiRestProxyModel.Filter();
        filter.setFieldName(filterName);
        filter.setPaasFieldValue(Lists.newArrayList(methodology.getName()));
        filter.setPaasOperator("EQ");
        filters.add(filter);
        Map<String, String> taskAdviceMap = new HashMap<>();
        for (IObjectData task : tasks) {
            String searchWord = task.get(MethodologyTaskConstants.NOTE, String.class);
            List<String> knowledgeIds = new ArrayList<>();
            String advice = aiKnowledgeBaseService.queryKnowledgeForProfileAdvice(new User(tenantId, User.SUPPER_ADMIN_USER_ID)
                    , searchWord, ragApiName, filters, knowledgeIds);
            log.info("profile_knowledge_prompt_info_{}: ragApiName:{},searchWord:{},result:{}"
                    , profileId, ragApiName, searchWord, advice);
            if (tenantType.equals(ProfileAdviceConstants.Type.CONS.getValue()) && !searchWord.equals(advice)) {
                taskAdviceMap.put(task.getId(), advice);
                taskKnowledgeMap.put(task.getId(), knowledgeIds);
            }
            if (tenantType.equals(ProfileAdviceConstants.Type.FS.getValue())) {
                taskAdviceMap.put(task.getId(), String.format("我在跟进一个:{%s},目前有以下应做的事项还没有做好:{%s},请根据以下知识:{%s},给我改善的行动建议", objectApiLabel, task.getName(), advice));// ignoreI18n
            }
        }
        if (tenantType.equals(ProfileAdviceConstants.Type.CONS.getValue()) && taskAdviceMap.isEmpty()) {
            return "";
        }
        String promptApiName = PrompotConstants.PROMPT_ADVICE_METHODOLOGY;
        AiRestProxyModel.Arg aiArg = new AiRestProxyModel.Arg();
        aiArg.setApiName(promptApiName);
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put(PROMPOT_INFO, JsonUtil.toJsonString(taskAdviceMap));
        aiArg.setSceneVariables(sceneParamMap);
        String result = completions.requestCompletion(user, aiArg);
        log.info("profile_pros_prompt_info_{}:apiName:{},requestInfo:{},result:{}"
                , profileId, aiArg.getApiName(), aiArg.getSceneVariables(), result);
        return result;
    }

    private IObjectData BuildAdvice(User user, String profileId, String rangeType, String targetId
            , String targetNodeAdvice, String taskId, String tenantType, int count, List<String> knowledgeIds) {
        IObjectData objectData = new ObjectData();
        objectData.setId(IdGenerator.get());
        objectData.setName(FeatureBaseDataUtils.generateName());
        objectData.setTenantId(user.getTenantId());
        objectData.setDescribeApiName(FeatureConstants.PROFILE_ADVICE);
        objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        objectData.setOwner(Lists.newArrayList(user.getUserId()));

        objectData.set(ProfileAdviceConstants.PROFILE_ID, profileId);
        if (!Strings.isNullOrEmpty(targetId)) {
            objectData.set(ProfileAdviceConstants.FEATURE_DIMENSION_ID, targetId);
        }
        objectData.set(ProfileAdviceConstants.TASK_ID, taskId);
        objectData.set(ProfileAdviceConstants.RANGE_TYPE, rangeType);

        objectData.set(ProfileAdviceConstants.ADVICE, targetNodeAdvice);
        objectData.set(ProfileAdviceConstants.TYPE, tenantType);
        objectData.set(ProfileAdviceConstants.SEQ, count);
        if (!CollectionUtils.isEmpty(knowledgeIds)) {
            objectData.set(ProfileAdviceConstants.KNOWLEDGE_DOCUMENT_IDS, knowledgeIds);
        }
        return objectData;
    }

}
