package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.facishare.crm.task.sfa.activitysummary.model.EvaluationModel;
import com.facishare.crm.task.sfa.activitysummary.model.SegmentModel;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 多轮对话结果跟踪器
 * 用于保存和跟踪最佳分段结果
 */
@Slf4j
public class ChatResultTracker {
    private double highestScore;
    private List<SegmentModel> bestSegments;
    private EvaluationModel bestEvaluation;
    
    public ChatResultTracker(double initialScore, List<SegmentModel> initialSegments, EvaluationModel initialEvaluation) {
        this.highestScore = initialScore;
        this.bestSegments = initialSegments;
        this.bestEvaluation = initialEvaluation;
    }
    
    /**
     * 如果新结果更好，则更新最佳结果
     */
    public void updateIfBetter(double newScore, List<SegmentModel> newSegments, EvaluationModel newEvaluation, int round) {
        if (newScore > this.highestScore) {
            this.highestScore = newScore;
            this.bestSegments = newSegments;
            this.bestEvaluation = newEvaluation;
            log.info("第{}轮多轮对话调整产生了新的最高得分: {}", round, highestScore);
        }
    }

    public double getHighestScore() {
        return highestScore;
    }

    public List<SegmentModel> getBestSegments() {
        return bestSegments;
    }

    public EvaluationModel getBestEvaluation() {
        return bestEvaluation;
    }
} 