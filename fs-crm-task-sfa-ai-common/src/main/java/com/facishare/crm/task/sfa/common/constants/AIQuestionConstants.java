package com.facishare.crm.task.sfa.common.constants;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2024/12/3 11:47
 * @IgnoreI18nFile
 */
public interface AIQuestionConstants {
    class Field {

        public static final String active_record_id = "active_record_id";
        public static final String library_id = "library_id";
        public static final String question_content = "question_content";
        public static final String question_proposer = "question_proposer";
        public static final String response_status = "response_status";
        public static final String match_suggest_topic = "match_suggest_topic";
        public static final String question_type = "question_type";
        public static final String answer_summary = "answer_summary";
        public static final String answer_person = "answer_person";
        public static final String answer_time = "answer_time";
        public static final String answer_version = "answer_version";
        public static final String proposer_attitude = "proposer_attitude";
        public static final String proposer_attitude_analysis = "proposer_attitude_analysis";
        public static final String ai_answer_content = "ai_answer_content";
        public static final String ai_answer_time = "ai_answer_time";
        public static final String tags = "tags";
        public static final String question_seq = "question_seq";
        public static final String answer_seq = "answer_seq";
        public static final String question_user = "question_user";
        public static final String answer_user = "answer_user";
    }

    enum QuestionTypeEnum {
        INTERACTION("1","互动话题"),
        SUGGESTION("2","建议话题");
        private String value;
        private String label;

        QuestionTypeEnum(String value,String label) {
            this.value = value;this.label = label;
        }

        public String getValue() {
            return value;
        }
    }

    enum ResponseStatusEnum {
        NOT_RESPONDED("not_responded","未应答"),
        ANSWERED_ALREADY("answered_already","已应答");
        private String value;
        private String label;

        ResponseStatusEnum(String value,String label) {
            this.value = value;this.label = label;
        }

        public String getValue() {
            return value;
        }
    }
    enum ProposerAttitudeEnum {
        DISSATISFIED("dissatisfied","不满意"),
        GENERAL("general","一般"),
        SATISFIED("satisfied","满意");
        private String value;
        private String label;

        ProposerAttitudeEnum(String value,String label) {
            this.value = value;this.label = label;
        }

        public String getValue() {
            return value;
        }
        public static String of(String label) {
            for (ProposerAttitudeEnum e : values()) {
                if (e.label.equals(label)) {
                    return e.value;
                }
            }
            return DISSATISFIED.value;
        }
    }

    @Data
    class InteractionResult{
        private String questionId;
        private String question;
        private String answer;
        private String questioner;
        private String responder;
        private String questioner_attitude;
        private String attitude_analysis;
        private List<String> matching_question;
        private String aiAnswerContent;
        private String tags;
        private String questionSeq;
        private String answerSeq;
        private List<String> answerSeqList;
        private List<WordResult> aiAnswerWord;
        private List<String> questionUser;
        private List<String> answerUser;
    }
    @Data
    class WordResult {
        private String dataId;
        private String title;
        private String url;
    }
}
