package com.facishare.crm.task.sfa.common.constants;

import lombok.Data;

/**
 * <AUTHOR> lik
 * @date : 2024/12/3 11:47
 * @IgnoreI18nFile
 */
public interface RequirementConstants {
    class Field {
        public static final String content = "content";
        public static final String category = "category";
        public static final String four_quadrant = "four_quadrant";
        public static final String kano = "kano";
        public static final String assessment_dimension = "assessment_dimension";
        public static final String response_status = "response_status";
        public static final String initiator = "initiator";
        public static final String contact_id = "contact_id";
        public static final String leads_id = "leads_id";
        public static final String active_record_id = "active_record_id";
        public static final String account_id = "account_id";
        public static final String opportunity_id = "opportunity_id";
        public static final String partner_id = "partner_id";
        public static final String clearance_status = "clearance_status";
        public static final String requirement_solve = "requirement_solve";
        public static final String knowledge_base_word = "knowledge_base_word";
        public static final String knowledge_base_word_id = "knowledge_base_word_id";
        public static final String seq_num = "seq_num";
    }
    @Data
    class WordResult {
        private String dataId;
        private String title;
        private String url;
    }
}
