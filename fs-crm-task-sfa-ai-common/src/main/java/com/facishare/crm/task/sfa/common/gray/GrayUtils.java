package com.facishare.crm.task.sfa.common.gray;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * @Description 灰度工具类
 * <AUTHOR>
 * @Date 2020/12/25 16:35
 */

@Component
public class GrayUtils {
    private CopyOnWriteArrayList<String> grayTenants = new CopyOnWriteArrayList();

    private Map<String, CopyOnWriteArrayList<String>> graySkipTenantObjects = new HashMap<>();
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");

    /**知识库的场景*/
    public static String KNOWLEDGE_BASE_TENANT = "";

    static {
        ConfigFactory.getConfig("variables_appframework", config -> {
            KNOWLEDGE_BASE_TENANT = config.get("knowledge_base_tenant", "");
        });
    }
    /***
     * 读取版本灰度配置文件:fs-metadata-gray
     */
    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-metadata-gray", config -> grayTenants = new CopyOnWriteArrayList(Arrays.asList(config.get("gray_tenantIds").split(","))));
        grayTenants.removeIf(x -> Strings.isNullOrEmpty(String.valueOf(x)));


        ConfigFactory.getConfig("fs-gray-sfa-follow", config -> {
            graySkipTenantObjects = new HashMap<>();
            List<String> followSkips = Arrays.asList(config.get("object_data_crm_follow_skip").split(","));
            for (String s : followSkips) {
                String[] split = s.split(":");
                graySkipTenantObjects.put(split[0], new CopyOnWriteArrayList(Arrays.asList(split[1].split("\\|"))));
            }
        });
    }

    /**
     * 空   返回true ，不走灰度
     * 全网 -1或all
     *
     * @param tenantId
     * @return
     */
    public Boolean isFSMetadataGray(String tenantId) {
        if (CollectionUtils.empty(grayTenants)) {
            return true;
        }
        if (grayTenants.size() == 1 && ("-1".equalsIgnoreCase(grayTenants.get(0)) || "all".equalsIgnoreCase(grayTenants.get(0)))) {
            return true;
        }
        if (grayTenants.contains(tenantId)) {
            return true;
        }
        return false;
    }

    public static String getKnowledgeBaseTenant() {
        return KNOWLEDGE_BASE_TENANT;
    }

    public Boolean isSkipObjectDataConsume(String tenantId, String apiName) {
        if (CollectionUtils.empty(graySkipTenantObjects)) {
            return false;
        }
        if (CollectionUtils.empty(graySkipTenantObjects.get(tenantId))) {
            return false;
        }
        if (graySkipTenantObjects.get(tenantId).size() == 1 && graySkipTenantObjects.get(tenantId).get(0).equals("*")) {
            return true;
        }
        if (graySkipTenantObjects.get(tenantId).size() == 1 && graySkipTenantObjects.get(tenantId).get(0).equals("-1")) {
            return false;
        }
        if (graySkipTenantObjects.get(tenantId).contains(apiName)) {
            return true;
        }
        return false;
    }

    public static boolean isGrayPaymentPlanStatus(String tenantId) {
        return gray.isAllow("payment_plan_new_task", tenantId);
    }

    public static boolean isGraySkipAccountToAddress(String tenantId) {
        return gray.isAllow("skip_account_to_address", tenantId);
    }

    public static boolean isGrayCreateLinkRelation(String tenantId) {
        return gray.isAllow("create_link_relation", tenantId);
    }


    public boolean isGrayFollowVersion(String tenantId) {
        return gray.isAllow("follow_800", tenantId);
    }

    public boolean isGrayDispatch(String tenantId) {
        return gray.isAllow("follow_dispatch", tenantId);
    }

    public boolean isSkipFollowObjectData(String tenantId) {
        return gray.isAllow("follow_object_data_skip", tenantId);
    }

    public Boolean skipObjectDataChangeTenantId(String tenantId) {
        return gray.isAllow("skip_object_data_change_tenant_id", tenantId);
    }

    public Boolean skipLeadsDuplicatedTenantId(String tenantId) {
        return gray.isAllow("skip_leads_duplicated_tenant_id", tenantId);
    }

    public static boolean skipQywxConversion(String tenantId) {
        return gray.isAllow("skip_qywx_conversion", tenantId);
    }

    public static boolean skipQywxEmployeeChange(String tenantId) {
        return gray.isAllow("skip_qywx_employee_change", tenantId);
    }

    public static boolean skipWechatFriendSync(String tenantId) {
        return gray.isAllow("skip_qywx_wechat_friend_sync", tenantId);
    }

    public boolean triggerWorkflowAfterCreateProject(String tenantId) {
        return gray.isAllow("trigger_workflow_after_create_project", tenantId);
    }

    public static boolean skipQywxSessionShared(String tenantId) {
        return gray.isAllow("skip_qywx_session_shard", tenantId);
    }

    public static boolean bomMasterSlaveMode(String tenantId) {
        return gray.isAllow("bom_master_slave_mode", tenantId);
    }

    public static boolean verifyFlattenIssue(String tenantId) {
        return gray.isAllow("verify_flatten_issue", tenantId);
    }

    public static boolean allocateCallJarNoRest(String tenantId) {
        return gray.isAllow("allocate_call_jar_no_rest", tenantId);
    }

    public static boolean allocateUserSortById(String tenantId) {
        return gray.isAllow("allocate_user_sort_by_id", tenantId);
    }

    public static boolean useWechatFriendSyncRule(String tenantId) {
        return gray.isAllow("use_wechat_friend_sync_rule", tenantId);
    }

    public static boolean employeeFirstReply(String tenantId) {
        return gray.isAllow("employee_first_reply", tenantId);
    }


    public static boolean alasRelationTemplateSkip(String tenantId) {
        return gray.isAllow("alas_relation_template_skip_tenant_id", tenantId);
    }

    public static boolean doOldChangePartnerAndOwnerMethod(String tenantId) {
        return gray.isAllow("do_old_change_out_owner_method", tenantId);
    }

    public static boolean isChannelGray930(String tenantId) {
        return gray.isAllow("channel_930", tenantId);
    }

    public static boolean skipChangeOwnerTenant(String tenantId) {
        return gray.isAllow("skip_change_owner_tenant", tenantId);
    }

    public static boolean availableRangeCalculateSkipTenant(String tenantId) {
        return gray.isAllow("available-range-calculate-skip-tenant", tenantId);
    }

    public static boolean availableRangeNewConsumerGray(String tenantId) {
        return gray.isAllow("available_range_new_consumer_gray", tenantId);
    }

    public static boolean objectDataForwardSkipTenant(String tenantId) {
        return gray.isAllow("object_data_forward_skip_tenant", tenantId);
    }


    public static boolean skipCountHistorySession(String tenantId) {
        return gray.isAllow("skip_count_history_session", tenantId);
    }
    public static boolean isVersionGrayTenant(String tenantId) {
        return gray.isAllow("version_gray_tenant", tenantId);
    }

    public static boolean availableRangeRateLimitTenant(String tenantId) {
        return gray.isAllow("available_range_rate_limit_tenant", tenantId);
    }

    public static boolean priceBookRateLimitTenant(String tenantId) {
        return gray.isAllow("price_book_rate_limit_tenant", tenantId);
    }

    public static boolean pricePolicyRateLimitTenant(String tenantId) {
        return gray.isAllow("price_policy_rate_limit_tenant", tenantId);
    }

    public static boolean incentivePolicyRateLimitTenant(String tenantId) {
        return gray.isAllow("incentive_policy_rate_limit_tenant", tenantId);
    }

    public static boolean isSkipActivityInteraction(String tenantId) {
        return gray.isAllow("skip_sfa_interaction_tenant", tenantId);
    }
    public static boolean skipAttendeesInsight(String tenantId) {
        return gray.isAllow("skip_attendees_insight", tenantId);
    }
}
